#!/usr/bin/env python3
"""
测试JSON修复功能
"""

import sys
import os
import json

sys.path.append(os.path.dirname(os.path.abspath(__file__)))


def test_json_fix():
    """测试JSON修复功能"""
    print("🔍 测试JSON修复功能...")

    try:
        from app.services.ai_model_service import AIModelService

        ai_service = AIModelService()

        # 模拟有问题的JSON（基于实际日志）
        problematic_json = """{
  "checkResultArr": [
    {
      "quesType": "规范性",
      "quesDesc": "招标文件在"投标人须知前附表"中规定了投标人提出问题、要求澄清招标文件的截止时间及方式为"以书面方式通过安天智采招标采购电子交易平台（https:
      "originalArr": [
        "第一章 招标公告 -> 八、联系方式 -> 电子邮件：<EMAIL>",
        "第二章 投标人须知前附表 -> 15. 投标人提出问题、要求澄清招标文件的截止时间及方式"
      ],
      "point": "答疑机制的明确性与一致性",
      "advice": "建议在"投标人须知前附表"中明确答疑提交方式，如果只接受平台提交，则无需在答疑机制中提及邮箱；如果接受邮箱提交，则需明确邮箱地址和提交要求。"
    }
  ]
}"""

        print("原始有问题的JSON:")
        print(problematic_json[:200] + "...")

        # 测试JSON修复
        fixed_json = ai_service._aggressive_json_fix(problematic_json)

        print("\n修复后的JSON:")
        print(fixed_json[:300] + "...")

        # 尝试解析修复后的JSON
        try:
            parsed = json.loads(fixed_json)
            print(f"\n✅ JSON解析成功!")
            print(f"   检查结果数量: {len(parsed.get('checkResultArr', []))}")

            if parsed.get("checkResultArr"):
                first_item = parsed["checkResultArr"][0]
                print(f"   第一个问题类型: {first_item.get('quesType')}")
                print(f"   问题描述长度: {len(first_item.get('quesDesc', ''))}")
                print(f"   原文数量: {len(first_item.get('originalArr', []))}")

            return True

        except json.JSONDecodeError as e:
            print(f"❌ JSON解析仍然失败: {e}")
            print(f"   错误位置: 第{e.lineno}行，第{e.colno}列")
            return False

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback

        traceback.print_exc()
        return False


def test_complete_json():
    """测试完整的JSON修复"""
    print("\n🔍 测试完整的JSON修复...")

    # 模拟完整的有问题的JSON
    complete_json = """{
  "checkResultArr": [
    {
      "quesType": "规范性",
      "quesDesc": "招标文件在"投标人须知前附表"中规定了投标人提出问题、要求澄清招标文件的截止时间及方式为"以书面方式通过安天智采招标采购电子交易平台（https://www.xinecai.com/）提交"，但在"招标公告"的联系方式中提供了电子邮件地址。",
      "originalArr": [
        "第一章 招标公告 -> 八、联系方式 -> 电子邮件：<EMAIL>"
      ],
      "point": "答疑机制的明确性与一致性",
      "advice": "建议在"投标人须知前附表"中明确答疑提交方式。"
    },
    {
      "quesType": "合规性",
      "quesDesc": "第二个问题的描述，包含"引号"问题。",
      "originalArr": [
        "原文内容"
      ],
      "point": "测试要点",
      "advice": "测试建议"
    }
  ]
}"""

    try:
        from app.services.ai_model_service import AIModelService

        ai_service = AIModelService()

        # 测试完整的JSON清理流程
        cleaned_json = ai_service.clean_json_data(complete_json)

        print(f"✅ 完整JSON清理成功!")
        print(f"   清理后长度: {len(cleaned_json)}")

        # 尝试解析
        try:
            parsed = json.loads(cleaned_json)
            print(f"   解析成功，问题数量: {len(parsed.get('checkResultArr', []))}")
            return True
        except json.JSONDecodeError as e:
            print(f"❌ 解析失败: {e}")
            return False

    except Exception as e:
        print(f"❌ 完整测试失败: {e}")
        return False


if __name__ == "__main__":
    print("🚀 开始测试JSON修复功能...")

    success = True
    success &= test_json_fix()
    success &= test_complete_json()

    if success:
        print("\n🎉 JSON修复功能测试通过!")
        print("现在应该能正确解析AI模型返回的JSON了。")
    else:
        print("\n❌ JSON修复功能仍有问题，需要进一步优化")
        sys.exit(1)
