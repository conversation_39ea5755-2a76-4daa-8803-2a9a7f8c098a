# -*- coding: utf-8 -*-
"""
配置管理模块
"""

import os
from typing import Optional
from pydantic import Field
from dotenv import load_dotenv

try:
    from pydantic_settings import BaseSettings
except ImportError:
    # 兼容旧版本的pydantic
    from pydantic import BaseSettings

# 加载环境变量
load_dotenv()


class Settings(BaseSettings):
    """应用配置类"""

    # 基础配置
    environment: str = Field(default="development", env="ENVIRONMENT")
    debug: bool = Field(default=True, env="DEBUG")

    # AI模型配置
    model_apikey: str = Field(..., env="MODEL_APIKEY")
    model_name: str = Field(default="gemini-2.5-flash", env="MODEL_NAME")
    model_url: str = Field(default="http://localhost:3002/v1", env="MODEL_URL")
    model_top_p: float = Field(default=0.5, env="MODEL_TOP_P")
    model_seed: int = Field(default=42, env="MODEL_SEED")
    model_temperature: float = Field(default=0.0, env="MODEL_TEMPERATURE")
    max_context_length: int = Field(default=1048576, env="MAX_CONTEXT_LENGTH")
    max_output_tokens: int = Field(default=65536, env="MAX_OUTPUT_TOKENS")

    # 敏感词检测API配置
    sensitive_word_api_url: str = Field(
        default="http://*************:8087", env="SENSITIVE_WORD_API_URL"
    )

    # 文件处理配置
    max_file_size: int = Field(default=300 * 1024 * 1024, env="MAX_FILE_SIZE")  # 300MB
    allowed_extensions: list = Field(
        default=[".docx", ".pdf"], env="ALLOWED_EXTENSIONS"
    )

    # 日志配置
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    log_file_path: str = Field(default="./logs", env="LOG_FILE_PATH")

    # HTTP请求配置
    request_timeout: int = Field(default=300, env="REQUEST_TIMEOUT")  # 5分钟
    max_retries: int = Field(default=3, env="MAX_RETRIES")

    # 性能优化配置
    # 缓存配置
    cache_max_size_mb: int = Field(default=100, env="CACHE_MAX_SIZE_MB")
    cache_ttl_seconds: int = Field(default=3600, env="CACHE_TTL_SECONDS")  # 1小时
    enable_document_cache: bool = Field(default=True, env="ENABLE_DOCUMENT_CACHE")

    # 并发控制配置
    max_concurrent_requests: int = Field(default=10, env="MAX_CONCURRENT_REQUESTS")
    max_queue_size: int = Field(default=100, env="MAX_QUEUE_SIZE")
    request_queue_timeout: int = Field(
        default=300, env="REQUEST_QUEUE_TIMEOUT"
    )  # 5分钟

    # 资源管理配置
    memory_threshold_mb: int = Field(default=2048, env="MEMORY_THRESHOLD_MB")  # 2GB
    gc_threshold_percent: float = Field(default=80.0, env="GC_THRESHOLD_PERCENT")
    enable_auto_gc: bool = Field(default=True, env="ENABLE_AUTO_GC")

    # 限流配置
    rate_limit_requests: int = Field(default=100, env="RATE_LIMIT_REQUESTS")
    rate_limit_window_seconds: int = Field(
        default=3600, env="RATE_LIMIT_WINDOW_SECONDS"
    )  # 1小时

    # 异步处理配置
    enable_async_processing: bool = Field(default=True, env="ENABLE_ASYNC_PROCESSING")
    async_worker_count: int = Field(default=4, env="ASYNC_WORKER_COUNT")

    model_config = {
        "env_file": ".env",
        "env_file_encoding": "utf-8",
        "case_sensitive": False,
        "protected_namespaces": ("settings_",),  # 修复model_字段警告
    }


# 创建全局配置实例
settings = Settings()


def get_settings() -> Settings:
    """获取配置实例"""
    return settings


def validate_config() -> bool:
    """验证配置是否完整"""
    try:
        # 检查必需的配置项
        required_fields = ["model_apikey", "model_name", "model_url"]

        for field in required_fields:
            value = getattr(settings, field)
            if not value:
                raise ValueError(f"配置项 {field} 不能为空")

        # 验证文件大小限制
        if settings.max_file_size <= 0:
            raise ValueError("最大文件大小必须大于0")

        # 验证超时时间
        if settings.request_timeout <= 0:
            raise ValueError("请求超时时间必须大于0")

        return True

    except Exception as e:
        print(f"配置验证失败: {e}")
        return False
