# 招标文件合规性检查助手 - 环境配置示例
# 复制此文件为 .env 并根据实际情况修改配置

# =============================================================================
# 基础配置
# =============================================================================
ENVIRONMENT=production
DEBUG=false

# =============================================================================
# AI模型配置
# =============================================================================
# AI模型API密钥（必填）
MODEL_APIKEY=your-api-key-here

# AI模型名称
MODEL_NAME=deepseek-ai/DeepSeek-V3

# AI模型API地址
MODEL_URL=http://localhost:3002/v1

# 模型参数
MODEL_TOP_P=0.5
MODEL_SEED=42
MODEL_TEMPERATURE=0.0

# 上下文和输出限制
MAX_CONTEXT_LENGTH=65536
MAX_OUTPUT_TOKENS=8192

# =============================================================================
# 敏感词检测API配置
# =============================================================================
# 敏感词检测API地址
SENSITIVE_WORD_API_URL=http://*************:8087

# API超时和重试设置
SENSITIVE_WORD_API_TIMEOUT=30
SENSITIVE_WORD_API_RETRIES=3

# =============================================================================
# 文件处理配置
# =============================================================================
# 最大文件大小（字节）- 默认300MB
MAX_FILE_SIZE=314572800

# 支持的文件扩展名
ALLOWED_EXTENSIONS=[".docx", ".pdf"]

# 文件下载超时时间（秒）
FILE_DOWNLOAD_TIMEOUT=60

# =============================================================================
# 日志配置
# =============================================================================
# 日志级别：DEBUG, INFO, WARNING, ERROR, CRITICAL
LOG_LEVEL=INFO

# 日志文件路径
LOG_FILE_PATH=./logs

# 日志保留天数
LOG_RETENTION_DAYS=30

# =============================================================================
# HTTP请求配置
# =============================================================================
# 请求超时时间（秒）
REQUEST_TIMEOUT=300

# 最大重试次数
MAX_RETRIES=3

# =============================================================================
# 性能优化配置
# =============================================================================

# 缓存配置
CACHE_MAX_SIZE_MB=100
CACHE_TTL_SECONDS=3600
ENABLE_DOCUMENT_CACHE=true

# 并发控制配置
MAX_CONCURRENT_REQUESTS=10
MAX_QUEUE_SIZE=100
REQUEST_QUEUE_TIMEOUT=300

# 资源管理配置
MEMORY_THRESHOLD_MB=2048
GC_THRESHOLD_PERCENT=80.0
ENABLE_AUTO_GC=true

# 限流配置
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW_SECONDS=3600

# 异步处理配置
ENABLE_ASYNC_PROCESSING=true
ASYNC_WORKER_COUNT=4

# =============================================================================
# 数据库配置（如果使用）
# =============================================================================
# Redis配置（可选）
# REDIS_URL=redis://localhost:6379/0
# REDIS_PASSWORD=
# REDIS_DB=0

# =============================================================================
# 安全配置
# =============================================================================
# API密钥（如果需要认证）
# API_SECRET_KEY=your-secret-key-here

# CORS配置
# CORS_ORIGINS=["http://localhost:3000", "https://yourdomain.com"]

# =============================================================================
# 监控配置
# =============================================================================
# 启用性能监控
ENABLE_PERFORMANCE_MONITORING=true

# 监控数据保留时间（小时）
MONITORING_RETENTION_HOURS=24

# =============================================================================
# 部署配置
# =============================================================================
# 服务器配置
API_HOST=0.0.0.0
API_PORT=8088

# Gunicorn工作进程数
GUNICORN_WORKERS=4

# 工作进程类型
WORKER_CLASS=uvicorn.workers.UvicornWorker

# 工作进程超时时间
WORKER_TIMEOUT=300

# 最大请求数（重启工作进程）
MAX_REQUESTS=1000
MAX_REQUESTS_JITTER=100

# =============================================================================
# 开发配置（仅开发环境使用）
# =============================================================================
# 开发模式下的配置
# DEV_RELOAD=true
# DEV_LOG_LEVEL=DEBUG

# 测试配置
# TEST_DATABASE_URL=sqlite:///./test.db
# TEST_CACHE_SIZE_MB=10