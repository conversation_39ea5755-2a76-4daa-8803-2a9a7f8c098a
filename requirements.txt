# 核心框架
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# HTTP客户端和网络
requests==2.31.0
httpx==0.25.2

# 文件处理
python-docx==1.1.0
pdfplumber==0.10.3
filetype==1.2.0
markdownify==0.11.6

# AI模型客户端
openai==1.3.7

# 日志和监控
loguru==0.7.2

# 环境变量管理
python-dotenv==1.0.0

# 测试框架
pytest==7.4.3
pytest-asyncio==0.21.1

# 可选依赖（如果可用）
# markitdown  # 需要从GitHub安装或等待PyPI发布

# 开发工具
black==23.11.0
isort==5.12.0
flake8==6.1.0