#!/usr/bin/env python3
"""
简化的AI模型测试脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.ai_model_service import AIModelService
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)

def test_simple_ai_call():
    """测试简单的AI模型调用"""
    print("🔍 测试简单的AI模型调用...")
    
    try:
        # 初始化AI模型服务
        ai_service = AIModelService()
        
        # 简单的测试消息
        messages = [
            {
                "role": "system", 
                "content": "你是一个招标文件审查专家。请严格按照JSON格式返回结果。"
            },
            {
                "role": "user", 
                "content": "请对以下简单文本进行审查，返回JSON格式：项目名称：测试项目"
            }
        ]
        
        print("📤 发送消息到AI模型...")
        response = ai_service.call_model(messages, "test-simple")
        
        print(f"📥 AI模型响应长度: {len(response)} 字符")
        print(f"📄 AI模型响应内容: {response[:500]}...")
        
        # 测试JSON清理
        print("\n🔧 测试JSON清理功能...")
        cleaned = ai_service.clean_json_data(response)
        print(f"🧹 清理后长度: {len(cleaned)} 字符")
        print(f"📄 清理后内容: {cleaned[:500]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_direct_ai_call():
    """测试直接的AI模型调用"""
    print("\n🔍 测试直接的AI模型调用...")
    
    try:
        from openai import OpenAI
        
        # 直接调用AI模型
        client = OpenAI(
            api_key="sk-test",
            base_url="http://localhost:3002/v1"
        )
        
        response = client.chat.completions.create(
            model="gemini-2.5-flash",
            messages=[
                {"role": "system", "content": "你是一个助手，请用JSON格式回答。"},
                {"role": "user", "content": "请返回一个简单的JSON：{\"test\": \"hello\"}"}
            ],
            max_tokens=1000,
            temperature=0.0
        )
        
        content = response.choices[0].message.content
        print(f"📥 直接调用响应长度: {len(content)} 字符")
        print(f"📄 直接调用响应内容: {content}")
        
        return True
        
    except Exception as e:
        print(f"❌ 直接调用失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 开始简化AI模型测试...")
    
    # 测试1：通过服务调用
    success1 = test_simple_ai_call()
    
    # 测试2：直接调用
    success2 = test_direct_ai_call()
    
    print(f"\n📊 测试结果:")
    print(f"   服务调用: {'✅ 成功' if success1 else '❌ 失败'}")
    print(f"   直接调用: {'✅ 成功' if success2 else '❌ 失败'}")
