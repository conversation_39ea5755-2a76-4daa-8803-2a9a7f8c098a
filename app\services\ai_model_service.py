# -*- coding: utf-8 -*-
"""
AI模型服务
基于参考代码analyse_appendix.py的LLM调用模式
"""

import json
import re
import time
from typing import Dict, List, Optional, Any
from openai import OpenAI

from app.core.config import settings
from app.core.logger import log, performance_logger, TimingContext, log_function_call
from app.core.error_logger import error_logger
from app.models.schemas import (
    ProjectInfo,
    CheckResultItem,
    ComplianceCheckResponse,
    ComplianceCheckData,
)
from app.models.enums import QuestionType


class AIModelError(Exception):
    """AI模型调用异常"""

    def __init__(
        self,
        message: str,
        model_name: str = "",
        error_code: str = "",
        original_error: Exception = None,
    ):
        """
        初始化AI模型异常

        Args:
            message: 错误消息
            model_name: 模型名称
            error_code: 错误代码
            original_error: 原始异常
        """
        super().__init__(message)
        self.model_name = model_name
        self.error_code = error_code
        self.original_error = original_error
        self.error_type = "AI_MODEL_ERROR"

    def __str__(self):
        base_msg = super().__str__()
        details = []

        if self.model_name:
            details.append(f"模型: {self.model_name}")
        if self.error_code:
            details.append(f"错误码: {self.error_code}")
        if self.original_error:
            details.append(f"原因: {str(self.original_error)}")

        if details:
            return f"{base_msg} ({', '.join(details)})"
        return base_msg


class AIModelService:
    """AI模型服务类"""

    def __init__(self):
        """初始化AI模型服务"""
        self.client = None
        self._initialize_client()

    def _initialize_client(self):
        """初始化OpenAI客户端"""
        try:
            self.client = OpenAI(
                api_key=settings.model_apikey, base_url=settings.model_url
            )
            log.info(f"AI模型客户端初始化成功: {settings.model_name}")
        except Exception as e:
            error_logger.log_service_error(
                "AIModelService",
                "initialize_client",
                e,
                context={
                    "model_name": settings.model_name,
                    "model_url": settings.model_url,
                    "api_key_configured": bool(settings.model_apikey),
                },
            )
            raise AIModelError(
                f"AI模型客户端初始化失败: {str(e)}",
                model_name=settings.model_name,
                original_error=e,
            )

    @log_function_call
    def build_system_prompt(self, project_info: ProjectInfo) -> str:
        """
        构建系统提示词，根据项目类型调整

        Args:
            project_info: 项目信息

        Returns:
            str: 系统提示词
        """
        # 基础提示词
        base_prompt = """# Role: 招标文件审查与风险管控专家

                        ## Profile
                        - language: 中文
                        - description: 专业审查招标文件，识别合规性、逻辑性、风险管理、规范性、公平性、可操作性问题，为招标人提供整改建议
                        - background: 依据《中华人民共和国招标投标法》《中华人民共和国招标投标法实施条例》，对招标文件进行系统性审查
                        - personality: 严谨细致、专业专注、客观公正
                        - expertise: 工程、服务、货物类型项目招标文件审查，依法招标、非依法招标、政府采购类别项目招标文件审查

                        ## Rules
                        1. 基本原则：
                        - 项目信息核验：文件中的项目名称、编号、招标人信息必须与招标公告完全一致
                        - 投标人资格要求：投标人资格要求必须与招标公告完全一致
                        - 保证金比例控制：投标保证金≤2%项目预算
                        - 保证金支付方式：支付方式要包含多种方式，退还不能出现"无息"字样
                        - 答疑机制审核：答疑时间应提前3日，邮箱应明确且与招标公告一致，回复需包含完整说明
                        - 上下文一致性检查：招标文件上下文的税率、中标候选人人数、投标有效期等必须一致
                        - 评分合规性检查：评标得分相同情况下处理逻辑必须一致，评分办法满分必须是100分，不能超过100分
                        - 必须设置最高投标限价

                        2. 行为准则：
                        - 文件结构检查：目录页码与后文实际章节页码必须严格对应，目录层级要按照顺序并符合逻辑
                        - 文本准确性：招标文件不应存在错字、错词，语句要通顺并符合逻辑
                        - 条款一致性验证：投标人须知前附表内容必须与招标公告、投标须知保持一致

                        3. 限制条件：
                        - 输出限制：问题点不得超过15项
                        - 格式要求：问题描述必须包含原文位置和原文内容，原文位置引用需具体到第X章第X条；原文内容只包含原文内容，原文内容不要有原文位置；问题类型只有匹配度最高的一个，不要有多个
                        - 行业限制：审查标准需符合招标文件所属行业规范"""

        # 根据项目类型添加特定规则
        project_specific_rules = self._get_project_specific_rules(project_info)

        # 输出格式要求
        output_format = """
                        ## OutputFormat
                        请严格按照以下JSON格式返回审查结果，不要包含其他文本：

                        {
                        "checkResultArr": [
                            {
                            "quesType": "问题类型(合规性/逻辑性/风险管理/规范性/公平性/可操作性)",
                            "quesDesc": "问题描述，必须包含原文位置和原文内容，原文位置引用需具体到第X章第X条",
                            "originalArr": ["原文内容1", "原文内容2"],
                            "point": "质量控制要点",
                            "advice": "处理建议，需可操作"
                            }
                        ]
                        }

                        要求：
                        - 使用中文回答
                        - 以JSON格式返回结果，可直接用Python的json.loads解析
                        - 只返回JSON对象，不要包含其他文本
                        - 问题类型只有匹配度最高的一个，不要有多个
                        - 问题描述必须包含完整原文位置和原文内容，原文位置引用需具体到第X章第X条
                        - 原文内容只包含原文内容，原文内容不要有原文位置
                        - 严格根据上述规则进行审查，未发现问题则返回空数组
                        - 问题点不得超过15项
                        """

        return base_prompt + project_specific_rules + output_format

    def _get_project_specific_rules(self, project_info: ProjectInfo) -> str:
        """
        获取项目特定规则

        Args:
            project_info: 项目信息

        Returns:
            str: 项目特定规则
        """
        rules = []

        # 根据采购项目类型添加规则
        if project_info.procurement_project_type.value == "服务类":
            rules.append(
                """
                - 服务类项目特殊要求：
                - 服务期限和服务内容必须明确
                - 服务质量标准和考核指标必须具体
                - 服务人员资质要求必须合理
                - 服务保证金可能按服务年数计算"""
            )

        elif project_info.procurement_project_type.value == "货物类":
            rules.append(
                """
                - 货物类项目特殊要求：
                - 技术规格和参数必须明确
                - 质量标准和验收标准必须具体
                - 售后服务和质保期必须明确
                - 货物交付时间和地点必须确定"""
            )

        elif project_info.procurement_project_type.value == "工程类":
            rules.append(
                """
                - 工程类项目特殊要求：
                - 工程量清单必须完整
                - 技术标准和施工规范必须明确
                - 工期要求和质量标准必须具体
                - 安全文明施工要求必须完备"""
            )

        # 根据项目类别添加规则
        if project_info.project_category.value == "政府采购":
            rules.append(
                """
                - 政府采购特殊要求：
                - 履约保证金≤2.5%控制价
                - 必须符合政府采购法相关规定
                - 评标委员会人数要求更严格"""
            )
        else:
            rules.append(
                """
                - 非政府采购特殊要求：
                - 履约保证金≤10%控制价
                - 按照招标投标法相关规定执行"""
            )

        # 根据招标采购方式添加规则
        method_rules = {
            "公开招标": """
            - 公开招标特殊要求：
                - 文件获取期≥5个工作日（掐头去尾）
                - 文件发布时间至开标时间≥20自然日（掐头去尾）
                - 评标委员会人数须≥5人""",
            "邀请招标": """
                - 邀请招标特殊要求：
                - 邀请函发出时间要求
                - 评标委员会人数须≥5人
                - 邀请对象数量要求""",
            "竞争性磋商": """
                - 竞争性磋商特殊要求：
                - 磋商文件获取期≥5个工作日（掐头去尾）
                - 文件发布时间至磋商时间≥10个自然日
                - 评标委员会人数须≥3人""",
            "竞争性谈判": """
                - 竞争性谈判特殊要求：
                - 谈判文件获取期≥3个工作日（掐头去尾）
                - 评标委员会人数须≥3人""",
            "询价": """
                - 询价特殊要求：
                - 询价文件获取期≥3个工作日（掐头去尾）
                - 评标委员会人数须≥3人""",
        }

        method_rule = method_rules.get(
            project_info.bidding_procurement_method.value, ""
        )
        if method_rule:
            rules.append(method_rule)

        return "\n".join(rules)

    @log_function_call
    def clean_json_data(self, raw_response: str) -> str:
        """
        清理和修复JSON数据
        基于参考代码的clean_json_data功能

        Args:
            raw_response: 原始响应文本

        Returns:
            str: 清理后的JSON字符串
        """
        try:
            # 检查空响应
            if not raw_response or not raw_response.strip():
                log.warning("AI模型返回空响应，返回默认空结果JSON")
                return '{"checkResultArr": []}'

            # 移除可能的markdown代码块标记
            cleaned = raw_response.strip()
            if cleaned.startswith("```json"):
                cleaned = cleaned[7:]
            if cleaned.startswith("```"):
                cleaned = cleaned[3:]
            if cleaned.endswith("```"):
                cleaned = cleaned[:-3]

            # 移除前后空白
            cleaned = cleaned.strip()

            # 再次检查清理后是否为空
            if not cleaned:
                log.warning("清理后响应为空，返回默认空结果JSON")
                return '{"checkResultArr": []}'

            # 移除markdown代码块标记
            cleaned = re.sub(r"```json\s*", "", cleaned, flags=re.IGNORECASE)
            cleaned = re.sub(r"```\s*$", "", cleaned, flags=re.MULTILINE)
            cleaned = re.sub(r"^```\s*", "", cleaned, flags=re.MULTILINE)
            cleaned = re.sub(r"```", "", cleaned)  # 移除所有剩余的```
            cleaned = cleaned.strip()

            # 智能提取JSON对象
            start_idx = cleaned.find("{")
            if start_idx == -1:
                log.warning("响应中未找到JSON对象开始标记，返回默认空结果JSON")
                return '{"checkResultArr": []}'

            # 使用括号匹配来找到正确的JSON结束位置
            brace_count = 0
            end_idx = -1

            for i in range(start_idx, len(cleaned)):
                char = cleaned[i]
                if char == "{":
                    brace_count += 1
                elif char == "}":
                    brace_count -= 1
                    if brace_count == 0:
                        end_idx = i
                        break

            if end_idx == -1:
                log.warning("响应中未找到完整的JSON对象，返回默认空结果JSON")
                return '{"checkResultArr": []}'

            cleaned = cleaned[start_idx : end_idx + 1]

            # 修复常见的JSON格式问题
            # 移除注释
            cleaned = re.sub(r"//.*?\n", "\n", cleaned)
            cleaned = re.sub(r"/\*.*?\*/", "", cleaned, flags=re.DOTALL)

            # 移除控制字符（除了换行符、制表符和回车符）
            # 更严格的控制字符清理，包括Unicode控制字符
            cleaned = re.sub(r"[\x00-\x08\x0B\x0C\x0E-\x1F\x7F-\x9F]", "", cleaned)

            # 移除其他可能的问题字符
            cleaned = re.sub(r"[\u0000-\u001F\u007F-\u009F]", "", cleaned)

            # 移除零宽字符
            cleaned = re.sub(r"[\u200B-\u200D\uFEFF]", "", cleaned)

            # 修复尾随逗号
            cleaned = re.sub(r",(\s*[}\]])", r"\1", cleaned)

            # 修复缺失的逗号（在}或]后面应该有逗号的情况）
            cleaned = re.sub(r"}\s*(?=[{\[])", r"},", cleaned)
            cleaned = re.sub(r"]\s*(?=[{\[])", r"],", cleaned)

            # 修复多余的逗号
            cleaned = re.sub(r",\s*,", r",", cleaned)

            # 修复引号问题
            cleaned = re.sub(r'([{,]\s*)"([^"]*)"(\s*[,}])', r'\1"\2"\3', cleaned)

            # 尝试解析JSON，如果失败则进行更深度的修复
            try:
                json.loads(cleaned)
                return cleaned
            except json.JSONDecodeError as e:
                # 进行更深度的JSON修复
                log.warning(f"第一次JSON解析失败，尝试深度修复: {str(e)}")
                log.warning(f"第一次解析失败的内容: {cleaned[:500]}...")

                # 尝试提取JSON部分（去除可能的前后文本）
                json_match = re.search(r"\{.*\}", cleaned, re.DOTALL)
                if json_match:
                    cleaned = json_match.group(0)
                    log.warning(f"提取JSON后的内容: {cleaned[:500]}...")

                # 尝试修复JSON中的引号问题
                log.warning(f"尝试修复JSON引号问题，错误位置: 第{e.colno}列")

                try:
                    # 使用全面的JSON修复策略
                    fixed = self._comprehensive_json_fix(cleaned)

                    json.loads(fixed)
                    log.warning("全面JSON修复成功")
                    return fixed

                except json.JSONDecodeError as e2:
                    log.warning(f"全面JSON修复失败: {str(e2)}")
                    # 更详细的诊断输出
                    try:
                        pos = getattr(e2, "pos", None)
                        if pos is not None and 0 <= pos < len(cleaned):
                            start = max(0, pos - 80)
                            end = min(len(cleaned), pos + 80)
                            snippet = cleaned[start:end]
                            log.warning(
                                f"JSON修复失败位置上下文(pos={pos}): '{snippet}'"
                            )
                            log.warning(
                                f"失败字符: '{cleaned[pos]}' | ord={ord(cleaned[pos])}"
                            )
                    except Exception:
                        pass
                    log.warning("返回空结果")
                    return '{"checkResultArr": []}'

        except Exception as e:
            log.warning(f"JSON清理失败: {str(e)}, 返回默认空结果JSON")
            log.debug(f"原始响应: {raw_response}")
            return '{"checkResultArr": []}'

    def _fix_json_quotes(self, json_str: str) -> str:
        """
        修复JSON字符串中的引号问题
        """
        try:
            import re

            # 先处理常见的问题字符
            fixed = json_str

            # 替换中文引号为英文引号
            fixed = fixed.replace('"', '"').replace('"', '"')
            fixed = fixed.replace(""", "'").replace(""", "'")

            # 处理URL中的中文括号问题
            fixed = re.sub(r'(https?://[^"]*?)）', r"\1)", fixed)

            # 处理JSON字符串中的未转义引号
            # 这是最关键的修复：在JSON值中转义内部引号
            def escape_quotes_in_values(match):
                key = match.group(1)
                value = match.group(2)
                # 转义value中的引号，但保留开头和结尾的引号
                escaped_value = value.replace('"', '\\"')
                return f'"{key}": "{escaped_value}"'

            # 匹配 "key": "value包含"引号的内容" 模式
            # 使用非贪婪匹配来正确处理嵌套引号
            fixed = re.sub(
                r'"([^"]+)":\s*"([^"]*"[^"]*)"', escape_quotes_in_values, fixed
            )

            # 处理更复杂的情况：多个引号的值
            # 匹配包含多个引号的字符串值
            def fix_complex_quotes(text):
                # 找到所有的 "key": "value" 对
                pattern = r'"([^"]+)":\s*"([^"]*(?:"[^"]*)*)"'

                def replace_func(match):
                    key = match.group(1)
                    value = match.group(2)
                    # 计算引号数量，如果是奇数，说明有未配对的引号
                    quote_count = value.count('"')
                    if quote_count > 0:
                        # 转义所有内部引号
                        escaped_value = value.replace('"', '\\"')
                        return f'"{key}": "{escaped_value}"'
                    return match.group(0)

                return re.sub(pattern, replace_func, text)

            fixed = fix_complex_quotes(fixed)

            return fixed

        except Exception as e:
            log.warning(f"JSON引号修复失败: {str(e)}")
            return json_str

    def _aggressive_json_fix(self, json_str: str) -> str:
        """
        激进的JSON修复方法，专门处理URL和引号问题
        """
        try:
            import re

            # 简单而有效的修复策略
            fixed = json_str

            # 步骤1：修复被截断的URL
            # 将 "（https:" 修复为 "（https://www.xinecai.com/）"
            fixed = re.sub(
                r"（https:(?!\w)",  # 匹配 "（https:" 但后面不是字母数字
                r"（https://www.xinecai.com/）",
                fixed,
            )

            # 步骤2：修复quesDesc字段中的引号问题
            # 使用简单的字符串替换，专门处理已知的问题模式

            # 修复特定的问题模式：提交"，但在"招标公告"
            fixed = re.sub(r'提交"，但在"([^"]*?)"', r"提交\"，但在\"\1\"", fixed)

            # 修复其他常见的引号问题
            # 将字段值中的中文引号转义
            def fix_field_quotes(match):
                field_name = match.group(1)
                field_value = match.group(2)

                # 只转义中文引号，保留JSON结构
                escaped_value = field_value.replace('"', '\\"').replace('"', '\\"')

                return f'"{field_name}": "{escaped_value}"'

            # 匹配字段值中包含中文引号的情况
            field_pattern = r'"(quesDesc|advice|point)":\s*"([^"]*["""][^"]*)"'
            fixed = re.sub(field_pattern, fix_field_quotes, fixed)

            # 步骤3：确保JSON结构完整
            # 如果JSON被截断，尝试补全
            if not fixed.strip().endswith("}"):
                # 检查是否需要补全结构
                if '"checkResultArr":' in fixed:
                    # 计算大括号和方括号的平衡
                    open_braces = fixed.count("{")
                    close_braces = fixed.count("}")
                    open_brackets = fixed.count("[")
                    close_brackets = fixed.count("]")

                    # 补全缺失的结构
                    if open_braces > close_braces:
                        # 需要补全对象结束
                        missing_braces = open_braces - close_braces
                        if missing_braces >= 2:  # 通常需要补全项目对象和根对象
                            fixed += "\n    }\n  ]\n}"
                        elif missing_braces == 1:
                            fixed += "\n}"

                    if open_brackets > close_brackets:
                        # 需要补全数组结束
                        fixed += "\n  ]"

            return fixed

        except Exception as e:
            log.warning(f"激进JSON修复失败: {str(e)}")
            return json_str

    def _comprehensive_json_fix(self, json_str: str) -> str:
        """
        全面的JSON修复方法，智能处理字符串值中的引号问题
        """
        try:
            import re

            fixed = json_str

            # 记录修复前的数据用于调试
            log.debug(f"JSON修复前内容: {fixed[:1000]}...")

            # 步骤1：修复URL截断问题
            fixed = fixed.replace("（https:", "（https://www.xinecai.com/")

            # 预处理：将中文弯引号替换为转义的英文双引号，避免破坏JSON结构
            # 注意：这里直接替换为 \"，因为这些引号大多出现在字符串值内部
            fixed = fixed.replace("“", '\\"').replace("”", '\\"')

            # 步骤2：通用的字符串值引号修复
            # 使用状态机方法，逐字符解析JSON，正确处理字符串值中的引号
            fixed = self._fix_quotes_in_json_strings(fixed)

            # 步骤3：修复可能的格式问题
            # 移除可能的控制字符，但保留换行符
            fixed = re.sub(r"[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\x9f]", "", fixed)

            # 修复可能的格式问题
            fixed = re.sub(r",\s*,", ",", fixed)
            fixed = re.sub(r":\s*,", ': "",', fixed)

            # 确保JSON结构完整
            if not fixed.strip().endswith("}"):
                if '"checkResultArr":' in fixed:
                    # 计算括号平衡
                    open_braces = fixed.count("{")
                    close_braces = fixed.count("}")
                    if open_braces > close_braces:
                        missing = open_braces - close_braces
                        fixed += "}" * missing

            # 记录修复后的数据用于调试
            log.debug(f"JSON修复后内容: {fixed[:1000]}...")

            return fixed

        except Exception as e:
            log.error(f"全面JSON修复失败: {str(e)}")
            log.error(f"修复失败的原始内容: {json_str[:500]}...")
            return json_str

    def _fix_quotes_in_json_strings(self, json_str: str) -> str:
        """
        使用更鲁棒的方法修复JSON字符串值中的未转义引号
        """
        try:
            # 使用正则表达式方法，更可靠
            import re

            # 先处理明显的问题：未转义的双引号
            # 使用正则表达式匹配字符串字段的值
            def fix_string_field(match):
                field_name = match.group(1)
                field_value = match.group(2)

                # 转义字符串值中的双引号
                fixed_value = field_value.replace('"', '\\"')

                # 转义控制字符
                fixed_value = fixed_value.replace("\n", "\\n")
                fixed_value = fixed_value.replace("\r", "\\r")
                fixed_value = fixed_value.replace("\t", "\\t")
                fixed_value = fixed_value.replace("\b", "\\b")
                fixed_value = fixed_value.replace("\f", "\\f")

                return f'"{field_name}": "{fixed_value}"'

            # 匹配字符串字段，使用更精确的正则表达式
            # 这个正则表达式匹配 "fieldName": "value" 格式，其中value可能包含未转义的引号
            pattern = r'"(quesType|quesDesc|point|advice)":\s*"([^"]*(?:"[^"]*)*)"'

            result = json_str

            # 多次应用修复，直到没有更多匹配
            max_iterations = 10
            for iteration in range(max_iterations):
                new_result = re.sub(pattern, fix_string_field, result, flags=re.DOTALL)
                if new_result == result:
                    break  # 没有更多变化
                result = new_result
                log.debug(f"JSON修复迭代 {iteration + 1} 完成")

            # 处理数组中的字符串
            def fix_array_strings(match):
                array_content = match.group(1)
                # 转义数组元素中的引号和控制字符
                fixed_content = array_content.replace('"', '\\"')
                fixed_content = fixed_content.replace("\n", "\\n")
                fixed_content = fixed_content.replace("\r", "\\r")
                fixed_content = fixed_content.replace("\t", "\\t")
                return f'"originalArr": [{fixed_content}]'

            array_pattern = r'"originalArr":\s*\[([^\]]*)\]'
            result = re.sub(array_pattern, fix_array_strings, result, flags=re.DOTALL)

            return result

        except Exception as e:
            log.warning(f"修复JSON字符串引号失败: {str(e)}")
            # 如果正则表达式方法失败，回退到简单替换
            try:
                simple_fix = json_str.replace('"', '\\"').replace('"', '\\"')
                simple_fix = (
                    simple_fix.replace("\n", "\\n")
                    .replace("\r", "\\r")
                    .replace("\t", "\\t")
                )
                return simple_fix
            except Exception:
                return json_str

    def _find_next_non_space_char(self, text: str, start_pos: int) -> str:
        """
        找到下一个非空白字符
        """
        for i in range(start_pos, len(text)):
            char = text[i]
            if not char.isspace():
                return char
        return None

    @log_function_call
    def call_model(self, messages: List[Dict[str, str]], request_id: str = "") -> str:
        """
        调用AI模型（带重试机制）

        Args:
            messages: 消息列表
            request_id: 请求ID

        Returns:
            str: 模型响应
        """
        return self._call_model_with_retry(messages, request_id)

    def _call_model_with_retry(
        self,
        messages: List[Dict[str, str]],
        request_id: str = "",
        max_retries: int = 3,
        base_delay: float = 1.0,
        max_delay: float = 180.0,
        timeout: float = 300.0,
    ) -> str:
        """
        带重试机制的AI模型调用

        Args:
            messages: 消息列表
            request_id: 请求ID
            max_retries: 最大重试次数
            base_delay: 基础延迟时间（秒）
            max_delay: 最大延迟时间（秒）
            timeout: 单次调用超时时间（秒）

        Returns:
            str: 模型响应
        """
        if not self.client:
            raise AIModelError("AI模型客户端未初始化", model_name=settings.model_name)

        last_error = None

        for attempt in range(max_retries + 1):
            try:
                with TimingContext(f"AI模型调用(尝试{attempt+1})", request_id):
                    # 检查上下文长度（只在第一次尝试时检查）
                    if attempt == 0:
                        total_tokens = self._estimate_tokens(messages)
                        if total_tokens > settings.max_context_length:
                            log.warning(
                                f"上下文长度超限: {total_tokens} > {settings.max_context_length}"
                            )
                            # 截断内容
                            messages = self._truncate_messages(messages)

                    log.info(
                        f"调用AI模型: {settings.model_name}, 消息数: {len(messages)}, "
                        f"尝试: {attempt+1}/{max_retries+1} | 请求ID: {request_id}"
                    )

                    # 执行AI模型调用
                    result = self._execute_model_call(messages, timeout, request_id)

                    # 检查空响应
                    if not result or not result.strip():
                        error_msg = f"AI模型返回空响应 | 尝试: {attempt+1} | 请求ID: {request_id}"
                        log.warning(error_msg)

                        if attempt < max_retries:
                            # 空响应也进行重试
                            raise AIModelError(
                                "AI模型返回空响应",
                                model_name=settings.model_name,
                                error_code="EMPTY_RESPONSE",
                            )
                        else:
                            # 最后一次尝试仍然空响应，记录失败并抛出异常
                            performance_logger.log_api_call(
                                request_id, f"OpenAI-{settings.model_name}", 0, False
                            )
                            raise AIModelError(
                                "AI模型返回空响应（重试后仍失败）",
                                model_name=settings.model_name,
                                error_code="EMPTY_RESPONSE_FINAL",
                            )

                    # 成功获取响应
                    performance_logger.log_api_call(
                        request_id,
                        f"OpenAI-{settings.model_name}",
                        0,  # 时间由TimingContext记录
                        True,
                    )

                    log.info(
                        f"AI模型调用成功: 响应长度 {len(result)} 字符, "
                        f"尝试次数: {attempt+1} | 请求ID: {request_id}"
                    )
                    log.warning(f"AI模型原始响应内容: '{result}'")
                    return result

            except AIModelError as e:
                last_error = e

                # 检查是否应该重试
                if not self._should_retry(e, attempt, max_retries):
                    break

                # 计算延迟时间（指数退避）
                delay = min(base_delay * (2**attempt), max_delay)

                log.warning(
                    f"AI模型调用失败，将在 {delay:.1f}秒后重试 | "
                    f"尝试: {attempt+1}/{max_retries+1} | 错误: {str(e)} | 请求ID: {request_id}"
                )

                # 等待后重试
                import time

                time.sleep(delay)

            except Exception as e:
                last_error = AIModelError(
                    f"AI模型调用异常: {str(e)}",
                    model_name=settings.model_name,
                    error_code=getattr(e, "code", "unknown"),
                    original_error=e,
                )

                # 检查是否应该重试
                if not self._should_retry(last_error, attempt, max_retries):
                    break

                # 计算延迟时间（指数退避）
                delay = min(base_delay * (2**attempt), max_delay)

                log.warning(
                    f"AI模型调用异常，将在 {delay:.1f}秒后重试 | "
                    f"尝试: {attempt+1}/{max_retries+1} | 错误: {str(e)} | 请求ID: {request_id}"
                )

                # 等待后重试
                import time

                time.sleep(delay)

        # 所有重试都失败了
        performance_logger.log_api_call(
            request_id, f"OpenAI-{settings.model_name}", 0, False
        )

        error_logger.log_service_error(
            "AIModelService",
            "call_model_with_retry",
            last_error,
            request_id,
            context={
                "max_retries": max_retries,
                "total_attempts": max_retries + 1,
                "model_name": settings.model_name,
                "message_count": len(messages),
                "timeout": timeout,
            },
        )

        if last_error:
            raise last_error
        else:
            raise AIModelError(
                "AI模型调用失败（未知错误）",
                model_name=settings.model_name,
                error_code="UNKNOWN_ERROR",
            )

    def _execute_model_call(
        self, messages: List[Dict[str, str]], timeout: float, request_id: str
    ) -> str:
        """
        执行单次AI模型调用

        Args:
            messages: 消息列表
            timeout: 超时时间
            request_id: 请求ID

        Returns:
            str: 模型响应
        """
        import signal

        def timeout_handler(signum, frame):
            raise TimeoutError(f"AI模型调用超时（{timeout}秒）")

        # 设置超时处理（仅在Unix系统上有效）
        old_handler = None
        try:
            if hasattr(signal, "SIGALRM"):
                old_handler = signal.signal(signal.SIGALRM, timeout_handler)
                signal.alarm(int(timeout))
        except (AttributeError, OSError):
            # Windows系统或其他不支持SIGALRM的系统
            pass

        try:
            response = self.client.chat.completions.create(
                model=settings.model_name,
                messages=messages,
                temperature=settings.model_temperature,
                top_p=settings.model_top_p,
                max_tokens=settings.max_output_tokens,
                seed=settings.model_seed,
            )

            return response.choices[0].message.content

        finally:
            # 清除超时设置
            try:
                if hasattr(signal, "SIGALRM") and old_handler is not None:
                    signal.alarm(0)
                    signal.signal(signal.SIGALRM, old_handler)
            except (AttributeError, OSError):
                pass

    def _should_retry(
        self, error: AIModelError, attempt: int, max_retries: int
    ) -> bool:
        """
        判断是否应该重试

        Args:
            error: 发生的错误
            attempt: 当前尝试次数（从0开始）
            max_retries: 最大重试次数

        Returns:
            bool: 是否应该重试
        """
        # 如果已经达到最大重试次数，不再重试
        if attempt >= max_retries:
            return False

        # 根据错误类型决定是否重试
        error_code = getattr(error, "error_code", "") or ""

        # 可重试的错误类型（全部转换为小写以便匹配）
        retryable_errors = [
            "empty_response",
            "timeout",
            "connection_error",
            "rate_limit",
            "server_error",
            "503",
            "502",
            "500",
            "429",
        ]

        # 检查错误代码或错误消息（都转换为小写）
        # 确保error_code不是None
        error_code_lower = str(error_code).lower() if error_code else ""
        error_message_lower = str(error).lower()

        for retryable in retryable_errors:
            if retryable in error_code_lower or retryable in error_message_lower:
                return True

        # 默认不重试
        return False

    def _estimate_tokens(self, messages: List[Dict[str, str]]) -> int:
        """
        估算token数量
        简单估算：中文字符*1.5 + 英文单词*1.3

        Args:
            messages: 消息列表

        Returns:
            int: 估算的token数量
        """
        total_chars = 0
        for message in messages:
            content = message.get("content", "")
            total_chars += len(content)

        # 简单估算：平均每个字符约1.2个token
        return int(total_chars * 1.2)

    def _truncate_messages(
        self, messages: List[Dict[str, str]]
    ) -> List[Dict[str, str]]:
        """
        截断消息以适应上下文长度限制

        Args:
            messages: 原始消息列表

        Returns:
            List[Dict[str, str]]: 截断后的消息列表
        """
        if len(messages) <= 1:
            return messages

        # 保留系统消息和最后一条用户消息
        system_message = messages[0] if messages[0]["role"] == "system" else None
        user_message = messages[-1] if messages[-1]["role"] == "user" else None

        if not user_message:
            return messages

        # 计算可用的内容长度
        available_length = settings.max_context_length - 1000  # 预留1000个token

        if system_message:
            system_tokens = self._estimate_tokens([system_message])
            available_length -= system_tokens

        # 截断用户消息内容
        user_content = user_message["content"]
        if self._estimate_tokens([user_message]) > available_length:
            # 按字符截断，保留前80%的内容
            truncate_length = int(len(user_content) * 0.8)
            user_content = user_content[:truncate_length] + "\n...(内容已截取)"
            user_message = {"role": "user", "content": user_content}

        result = []
        if system_message:
            result.append(system_message)
        result.append(user_message)

        log.warning(f"消息已截断，原始消息数: {len(messages)}, 截断后: {len(result)}")
        return result

    @log_function_call
    def check_compliance(
        self, content: str, project_info: ProjectInfo, request_id: str = ""
    ) -> ComplianceCheckResponse:
        """
        检查招标文件合规性

        Args:
            content: 文档内容
            project_info: 项目信息
            request_id: 请求ID

        Returns:
            ComplianceCheckResponse: 合规性检查结果
        """
        try:
            # 构建系统提示词
            system_prompt = self.build_system_prompt(project_info)

            # 构建用户消息
            user_prompt = f"""请根据上述规则对以下招标文件进行合规性审查：

                            项目信息：
                            - 采购项目类型：{project_info.procurement_project_type.value}
                            - 项目类别：{project_info.project_category.value}
                            - 招标采购方式：{project_info.bidding_procurement_method.value}

                            招标文件内容：
                            {content}

                            请严格按照系统提示词中的规则进行审查，并按照指定的JSON格式返回结果。"""

            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt},
            ]

            # 调用模型
            raw_response = self.call_model(messages, request_id)

            # 清理JSON数据
            log.info(
                f"开始清理AI响应JSON数据 | 请求ID: {request_id} | 原始长度: {len(raw_response)} 字符"
            )
            log.debug(f"原始AI响应内容: {raw_response[:1000]}...")

            cleaned_response = self.clean_json_data(raw_response)

            log.info(
                f"JSON数据清理完成 | 请求ID: {request_id} | 清理后长度: {len(cleaned_response)} 字符"
            )
            log.debug(f"清理后响应内容: {cleaned_response[:1000]}...")

            # 解析响应
            try:
                log.info(f"开始解析清理后的JSON数据 | 请求ID: {request_id}")
                response_data = json.loads(cleaned_response)
                log.info(
                    f"JSON解析成功 | 请求ID: {request_id} | 数据类型: {type(response_data)}"
                )

                # 验证响应格式
                if not isinstance(response_data, dict):
                    raise ValueError("响应不是有效的JSON对象")

                if "checkResultArr" not in response_data:
                    log.warning("响应中缺少checkResultArr字段，返回空结果")
                    return ComplianceCheckResponse(
                        code=200,
                        message="AI检查完成，未发现问题",
                        data=ComplianceCheckData(checkResultArr=[]),
                    )

                # 转换为CheckResultItem对象
                check_results = []
                log.info(
                    f"开始解析AI返回的检查结果，共 {len(response_data['checkResultArr'])} 个项目"
                )

                for i, item in enumerate(response_data["checkResultArr"]):
                    try:
                        # 记录原始数据用于调试
                        log.debug(f"解析检查结果项 {i+1}: {item}")

                        # 验证问题类型
                        ques_type = item.get("quesType", "规范性")
                        ques_desc = item.get("quesDesc", "")
                        original_arr = item.get("originalArr", [])
                        point = item.get("point", "")
                        advice = item.get("advice", "")

                        # 记录提取的字段值
                        log.debug(
                            f"提取字段值 {i+1}: quesType='{ques_type}', quesDesc='{ques_desc[:50] if ques_desc else 'None'}...', point='{point[:50] if point else 'None'}...', advice='{advice[:50] if advice else 'None'}...', originalArr={len(original_arr) if original_arr else 0}个元素"
                        )

                        # 处理组合类型（如"规范性/可操作性"），取第一个类型
                        if "/" in ques_type:
                            original_type = ques_type
                            ques_type = ques_type.split("/")[0].strip()
                            log.debug(
                                f"检测到组合问题类型 '{original_type}'，使用第一个类型: '{ques_type}'"
                            )

                        # 验证问题类型是否有效
                        valid_types = [e.value for e in QuestionType]
                        if ques_type not in valid_types:
                            log.warning(
                                f"无效的问题类型: '{ques_type}', 使用默认值 '{QuestionType.STANDARDIZATION.value}'"
                            )
                            ques_type = QuestionType.STANDARDIZATION.value

                        check_result = CheckResultItem(
                            quesType=ques_type,
                            quesDesc=ques_desc,
                            originalArr=original_arr,
                            point=point,
                            advice=advice,
                        )

                        # 验证创建的对象
                        log.debug(
                            f"成功创建检查结果项 {i+1}: quesType='{check_result.quesType}', quesDesc='{check_result.quesDesc[:50] if check_result.quesDesc else 'None'}...', point='{check_result.point[:50] if check_result.point else 'None'}...', advice='{check_result.advice[:50] if check_result.advice else 'None'}...', originalArr={len(check_result.originalArr) if check_result.originalArr else 0}个元素"
                        )

                        check_results.append(check_result)
                    except Exception as item_error:
                        log.error(f"解析检查结果项 {i+1} 失败: {str(item_error)}")
                        log.error(f"失败的原始数据: {item}")
                        continue

                log.info(f"合规性检查完成，发现 {len(check_results)} 个问题")
                return ComplianceCheckResponse(
                    code=200,
                    message=f"AI检查完成，发现 {len(check_results)} 个问题",
                    data=ComplianceCheckData(checkResultArr=check_results),
                )

            except json.JSONDecodeError as e:
                log.error(f"JSON解析失败: {str(e)} | 请求ID: {request_id}")
                log.error(f"原始响应长度: {len(raw_response)} 字符")
                log.error(f"清理后响应长度: {len(cleaned_response)} 字符")
                log.debug(f"原始响应内容: {raw_response[:500]}...")
                log.debug(f"清理后响应内容: {cleaned_response[:500]}...")

                # 尝试分析响应内容，提供更有用的错误信息
                if not cleaned_response or not cleaned_response.strip():
                    log.error("AI模型返回空响应，可能是模型配置问题或网络问题")
                elif "error" in str(cleaned_response).lower():
                    log.error("AI模型响应中包含错误信息，可能是API调用失败")
                elif cleaned_response.startswith("<"):
                    log.error("AI模型返回HTML内容而非JSON，可能是API端点错误")
                else:
                    log.error("AI模型返回格式不符合预期的JSON结构")

                return ComplianceCheckResponse(
                    code=200,
                    message="AI检查完成，未发现问题",
                    data=ComplianceCheckData(checkResultArr=[]),
                )

        except AIModelError as e:
            # 记录AI模型错误详情
            log.error(f"AI模型错误 | 请求ID: {request_id} | 错误: {str(e)}")

            # 对于空响应错误，返回空结果而不是抛出异常
            if hasattr(e, "error_code") and e.error_code == "EMPTY_RESPONSE":
                log.warning(f"AI模型空响应，返回空结果 | 请求ID: {request_id}")
                return ComplianceCheckResponse(
                    code=200,
                    message="AI检查完成，未发现问题",
                    data=ComplianceCheckData(checkResultArr=[]),
                )

            # 其他AI模型错误重新抛出
            raise
        except Exception as e:
            log.error(f"合规性检查异常 | 请求ID: {request_id} | 错误: {str(e)}")
            raise AIModelError(
                f"合规性检查失败: {str(e)}",
                model_name=settings.model_name,
                original_error=e,
            )

    def get_model_info(self) -> Dict[str, Any]:
        """
        获取模型信息

        Returns:
            Dict[str, Any]: 模型信息
        """
        return {
            "model_name": settings.model_name,
            "base_url": settings.model_url,
            "max_context_length": settings.max_context_length,
            "max_output_tokens": settings.max_output_tokens,
            "temperature": settings.model_temperature,
            "top_p": settings.model_top_p,
            "seed": settings.model_seed,
            "client_initialized": self.client is not None,
        }


# 创建全局AI模型服务实例
ai_model_service = AIModelService()
