#!/usr/bin/env python3
"""
调试JSON修复问题
"""

import json
import sys
import os

sys.path.append(os.path.dirname(os.path.abspath(__file__)))


def debug_json_issue():
    """调试JSON问题"""

    # 原始有问题的JSON
    problematic_json = """{
  "checkResultArr": [
    {
      "quesType": "合规性/逻辑性/规范性",
      "quesDesc": "招标文件封面项目名称多出"阿萨德"字样，与招标公告及其他章节的项目名称不一致，存在笔误。",
      "originalArr": [
        "安徽理工大学第一附属医院南区污水医废、生活垃圾一体化托管服务项目二次阿萨德"
      ],
      "point": "项目信息核验，确保文件中的项目名称与招标公告完全一致。",
      "advice": "删除招标文件封面项目名称中多余的"阿萨德"字样，使其与招标公告及其他章节的项目名称保持一致。"
    }
  ]
}"""

    print("=== 调试JSON修复问题 ===")
    print(f"原始JSON长度: {len(problematic_json)}")
    print(f"原始JSON: {problematic_json}")
    print()

    # 1. 直接尝试解析
    print("1. 直接解析原始JSON...")
    try:
        parsed = json.loads(problematic_json)
        print("✅ 直接解析成功！")
        print(f"结果: {len(parsed['checkResultArr'])} 个项目")
        return True
    except json.JSONDecodeError as e:
        print(f"❌ 直接解析失败: {e}")
        print(f"错误位置: 第{e.lineno}行第{e.colno}列")
        if hasattr(e, "pos"):
            print(f"错误字符位置: {e.pos}")
            if e.pos < len(problematic_json):
                print(f"错误字符: '{problematic_json[e.pos]}'")
                print(f"错误前后文: '{problematic_json[max(0, e.pos-20):e.pos+20]}'")

    # 2. 手动修复引号问题
    print("\n2. 手动修复引号问题...")
    fixed_json = problematic_json

    # 先替换中文引号
    fixed_json = fixed_json.replace('"', '"')
    fixed_json = fixed_json.replace('"', '"')
    fixed_json = fixed_json.replace('"', '"')
    fixed_json = fixed_json.replace('"', '"')

    # 然后转义字符串值内部的双引号
    # 使用正则表达式找到字符串值并转义其中的引号
    import re

    def escape_quotes_in_string_values(match):
        field_name = match.group(1)
        field_value = match.group(2)
        # 转义字符串值内部的双引号
        escaped_value = field_value.replace('"', '\\"')
        return f'"{field_name}": "{escaped_value}"'

    # 匹配JSON字符串字段的值
    pattern = r'"(quesType|quesDesc|point|advice)":\s*"([^"]*(?:"[^"]*)*)"'
    fixed_json = re.sub(
        pattern, escape_quotes_in_string_values, fixed_json, flags=re.DOTALL
    )

    # 处理数组中的字符串
    def escape_quotes_in_array(match):
        array_content = match.group(1)
        # 转义数组元素中的双引号
        escaped_content = array_content.replace('"', '\\"')
        return f'"originalArr": [{escaped_content}]'

    array_pattern = r'"originalArr":\s*\[([^\]]*)\]'
    fixed_json = re.sub(
        array_pattern, escape_quotes_in_array, fixed_json, flags=re.DOTALL
    )

    print(f"修复后JSON长度: {len(fixed_json)}")
    print(f"修复后JSON: {fixed_json}")

    try:
        parsed = json.loads(fixed_json)
        print("✅ 手动修复后解析成功！")
        print(f"结果: {len(parsed['checkResultArr'])} 个项目")

        item = parsed["checkResultArr"][0]
        print(f"第一个项目:")
        print(f"  quesType: '{item['quesType']}'")
        print(f"  quesDesc: '{item['quesDesc']}'")
        print(f"  point: '{item['point']}'")
        print(f"  advice: '{item['advice']}'")
        print(f"  originalArr: {item['originalArr']}")

        return True
    except json.JSONDecodeError as e:
        print(f"❌ 手动修复后仍然解析失败: {e}")
        print(f"错误位置: 第{e.lineno}行第{e.colno}列")
        if hasattr(e, "pos"):
            print(f"错误字符位置: {e.pos}")
            if e.pos < len(fixed_json):
                print(f"错误字符: '{fixed_json[e.pos]}'")
                print(f"错误前后文: '{fixed_json[max(0, e.pos-20):e.pos+20]}'")
        return False


if __name__ == "__main__":
    success = debug_json_issue()
    if success:
        print("\n✅ JSON修复成功")
    else:
        print("\n❌ JSON修复失败")
    sys.exit(0 if success else 1)
