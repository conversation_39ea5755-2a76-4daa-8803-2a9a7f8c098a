#!/usr/bin/env python3
"""
调试JSON修复问题
"""

import json
import sys
import os

sys.path.append(os.path.dirname(os.path.abspath(__file__)))


def debug_json_issue():
    """调试JSON问题"""

    # 原始有问题的JSON
    problematic_json = """{
  "checkResultArr": [
    {
      "quesType": "合规性/逻辑性/规范性",
      "quesDesc": "招标文件封面项目名称多出"阿萨德"字样，与招标公告及其他章节的项目名称不一致，存在笔误。",
      "originalArr": [
        "安徽理工大学第一附属医院南区污水医废、生活垃圾一体化托管服务项目二次阿萨德"
      ],
      "point": "项目信息核验，确保文件中的项目名称与招标公告完全一致。",
      "advice": "删除招标文件封面项目名称中多余的"阿萨德"字样，使其与招标公告及其他章节的项目名称保持一致。"
    }
  ]
}"""

    print("=== 调试JSON修复问题 ===")
    print(f"原始JSON长度: {len(problematic_json)}")
    print(f"原始JSON: {problematic_json}")
    print()

    # 1. 直接尝试解析
    print("1. 直接解析原始JSON...")
    try:
        parsed = json.loads(problematic_json)
        print("✅ 直接解析成功！")
        print(f"结果: {len(parsed['checkResultArr'])} 个项目")
        return True
    except json.JSONDecodeError as e:
        print(f"❌ 直接解析失败: {e}")
        print(f"错误位置: 第{e.lineno}行第{e.colno}列")
        if hasattr(e, "pos"):
            print(f"错误字符位置: {e.pos}")
            if e.pos < len(problematic_json):
                print(f"错误字符: '{problematic_json[e.pos]}'")
                print(f"错误前后文: '{problematic_json[max(0, e.pos-20):e.pos+20]}'")

    # 2. 手动修复引号问题
    print("\n2. 手动修复引号问题...")
    fixed_json = problematic_json

    # 简单直接的方法：逐个字符处理，只转义字符串值内部的双引号
    result = []
    in_string = False
    in_field_name = False
    i = 0

    while i < len(fixed_json):
        char = fixed_json[i]

        if char == '"' and not in_string:
            # 开始字符串
            result.append(char)
            in_string = True
            # 检查是否是字段名（后面跟着冒号）
            next_quote_pos = fixed_json.find('"', i + 1)
            if next_quote_pos != -1:
                between_quotes = fixed_json[i + 1 : next_quote_pos]
                colon_pos = fixed_json.find(":", next_quote_pos)
                if colon_pos != -1 and colon_pos == next_quote_pos + 1:
                    in_field_name = True
        elif char == '"' and in_string:
            # 结束字符串
            result.append(char)
            in_string = False
            in_field_name = False
        elif char == '"' and in_string and not in_field_name:
            # 在字符串值内部遇到双引号，需要转义
            result.append('\\"')
        else:
            result.append(char)

        i += 1

    fixed_json = "".join(result)

    print(f"修复后JSON长度: {len(fixed_json)}")
    print(f"修复后JSON: {fixed_json}")

    try:
        parsed = json.loads(fixed_json)
        print("✅ 手动修复后解析成功！")
        print(f"结果: {len(parsed['checkResultArr'])} 个项目")

        item = parsed["checkResultArr"][0]
        print(f"第一个项目:")
        print(f"  quesType: '{item['quesType']}'")
        print(f"  quesDesc: '{item['quesDesc']}'")
        print(f"  point: '{item['point']}'")
        print(f"  advice: '{item['advice']}'")
        print(f"  originalArr: {item['originalArr']}")

        return True
    except json.JSONDecodeError as e:
        print(f"❌ 手动修复后仍然解析失败: {e}")
        print(f"错误位置: 第{e.lineno}行第{e.colno}列")
        if hasattr(e, "pos"):
            print(f"错误字符位置: {e.pos}")
            if e.pos < len(fixed_json):
                print(f"错误字符: '{fixed_json[e.pos]}'")
                print(f"错误前后文: '{fixed_json[max(0, e.pos-20):e.pos+20]}'")
        return False


if __name__ == "__main__":
    success = debug_json_issue()
    if success:
        print("\n✅ JSON修复成功")
    else:
        print("\n❌ JSON修复失败")
    sys.exit(0 if success else 1)
