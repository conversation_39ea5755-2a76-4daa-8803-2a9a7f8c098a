# Design Document

## Overview

招标文件合规性检查助手采用微服务架构，基于FastAPI框架构建RESTful API接口。系统集成OpenAI大模型进行智能文档分析，结合外部敏感词检测服务，提供全面的招标文件合规性检查功能。

核心设计理念：
- **模块化设计**：文件处理、模型调用、敏感词检测、结果处理等功能独立模块
- **异步处理**：支持大文件和并发请求的高效处理
- **配置驱动**：通过环境变量实现灵活的参数配置
- **容错机制**：完善的异常处理和降级策略

## Architecture

### System Architecture

```mermaid
graph TB
    A[Client] --> B[FastAPI Gateway]
    B --> C[Request Validator]
    C --> D[File Processor]
    D --> E[Document Parser]
    E --> F[Content Converter]
    F --> G[AI Model Service]
    F --> H[Sensitive Word Service]
    G --> I[Result Processor]
    H --> I
    I --> J[Response Formatter]
    J --> B
    B --> A
    
    K[Config Manager] --> G
    K --> H
    L[Logger] --> B
    L --> D
    L --> G
    L --> H
    L --> I
```

### Service Layer Architecture

```mermaid
graph LR
    A[API Layer] --> B[Business Logic Layer]
    B --> C[Service Layer]
    C --> D[External Integration Layer]
    
    subgraph "API Layer"
        A1[FastAPI Router]
        A2[Request/Response Models]
        A3[Middleware]
    end
    
    subgraph "Business Logic Layer"
        B1[Document Processor]
        B2[Compliance Checker]
        B3[Result Aggregator]
    end
    
    subgraph "Service Layer"
        C1[File Service]
        C2[AI Model Service]
        C3[Sensitive Word Service]
        C4[Config Service]
        C5[Logger Service]
    end
    
    subgraph "External Integration Layer"
        D1[OpenAI API]
        D2[Sensitive Word API]
        D3[File System]
    end
```

## Components and Interfaces

### 1. API Gateway Component

**职责**：处理HTTP请求，路由分发，中间件管理

**接口定义**：
```python
@app.post("/check-compliance")
async def check_compliance(request: ComplianceCheckRequest) -> ComplianceCheckResponse
```

**核心功能**：
- 请求参数验证
- 文件格式检查
- 异常处理和响应格式化
- 请求日志记录

### 2. Document Processor Component

**职责**：文档解析和格式转换

**接口定义**：
```python
class DocumentProcessor:
    async def extract_content(self, file_info: FileInfo) -> str
    async def convert_to_markdown(self, content: str, file_type: str) -> str
```

**核心功能**：
- 支持docx和pdf文件解析
- 内容提取和清理
- 转换为markdown格式
- 文件大小和格式验证

### 3. AI Model Service Component

**职责**：大模型调用和结果处理

**接口定义**：
```python
class AIModelService:
    async def check_compliance(self, content: str, project_info: ProjectInfo) -> ComplianceResult
    def build_system_prompt(self, project_info: ProjectInfo) -> str
```

**核心功能**：
- 动态构建系统提示词
- 调用OpenAI API
- 结果解析和验证
- 上下文长度管理

### 4. Sensitive Word Service Component

**职责**：敏感词检测集成

**接口定义**：
```python
class SensitiveWordService:
    async def detect_sensitive_words(self, content: str, is_government_procurement: bool = None) -> List[SensitiveWord]
    async def health_check(self) -> bool
    async def get_stats(self) -> dict
```

**核心功能**：
- 调用http://*************:8087/detect接口进行敏感词检测
- 支持is_government_procurement参数，根据项目类别自动判断
- 结果格式转换：API响应{序号,敏感词类型,敏感词内容,出现次数} → {type,content,num}
- 健康检查和统计信息获取
- 错误处理和重试机制

### 5. Configuration Manager Component

**职责**：配置管理和环境变量处理

**接口定义**：
```python
class ConfigManager:
    def load_config(self) -> Config
    def get_model_config(self) -> ModelConfig
```

**核心功能**：
- .env文件加载
- 配置验证
- 默认值处理

### 6. Logger Service Component

**职责**：日志记录和管理

**接口定义**：
```python
class LoggerService:
    def log_request(self, request_id: str, request_data: dict)
    def log_error(self, error: Exception, context: dict)
    def log_performance(self, operation: str, duration: float)
```

## Data Models

### Request Models

```python
from enum import Enum
from pydantic import BaseModel, validator

class ProcurementProjectType(str, Enum):
    ENGINEERING = "工程类"
    SERVICE = "服务类"
    GOODS = "货物类"

class ProjectCategory(str, Enum):
    LEGAL_BIDDING = "依法招标"
    NON_LEGAL_BIDDING = "非依法招标"
    GOVERNMENT_PROCUREMENT = "政府采购"

class BiddingProcurementMethod(str, Enum):
    OPEN_BIDDING = "公开招标"
    SINGLE_SOURCE = "单一来源"
    COMPETITIVE_NEGOTIATION = "竞争性磋商"
    COMPETITIVE_NEGOTIATION_INVITATION = "竞争性磋商邀请"
    INVITATION_BIDDING = "邀请招标"
    COMPETITIVE_CONSULTATION = "竞争性谈判"
    OPEN_BIDDING_PRICE = "公开竞价"
    INVITATION_BIDDING_PRICE = "邀请竞价"
    INQUIRY = "询价"
    OTHER = "其他"
    COMPARISON = "比选"

class FileInfo(BaseModel):
    filename: str
    extension: str
    mime_type: str
    size: int
    url: str

class ProjectInfo(BaseModel):
    procurement_project_type: ProcurementProjectType  # 采购项目类型
    project_category: ProjectCategory                 # 项目类别
    bidding_procurement_method: BiddingProcurementMethod # 招标采购方式

class ComplianceCheckRequest(BaseModel):
    bidding_doc: FileInfo
    procurement_project_type: ProcurementProjectType
    project_category: ProjectCategory
    bidding_procurement_method: BiddingProcurementMethod
```

### Response Models

```python
class SensitiveWordItem(BaseModel):
    type: str      # 敏感词类型
    content: str   # 敏感词内容
    num: int       # 出现次数

class CheckResultItem(BaseModel):
    quesType: str           # 问题类型
    quesDesc: str          # 问题描述
    originalArr: List[str] # 原文内容
    point: str             # 质量控制要点
    advice: str            # 处理建议

class ComplianceCheckResponse(BaseModel):
    sensitiveWordsArr: List[SensitiveWordItem]
    checkResultArr: List[CheckResultItem]

# 敏感词API响应模型（内部使用）
class SensitiveWordApiResult(BaseModel):
    序号: int
    敏感词类型: str
    敏感词内容: str
    出现次数: int

class SensitiveWordApiResponse(BaseModel):
    success: bool
    message: str
    total_words: int
    markdown_table: str
    results: List[SensitiveWordApiResult]
```

### Internal Models

```python
class ModelConfig(BaseModel):
    api_key: str
    model_name: str
    base_url: str
    top_p: float
    seed: int
    temperature: float
    max_context_length: int
    max_output_tokens: int

class ProcessingContext(BaseModel):
    request_id: str
    file_content: str
    project_info: ProjectInfo
    start_time: datetime
```

## Error Handling

### Error Categories

1. **输入验证错误**
   - 文件格式不支持
   - 参数缺失或格式错误
   - 文件大小超限

2. **文件处理错误**
   - 文件下载失败
   - 文件解析失败
   - 格式转换错误

3. **外部服务错误**
   - OpenAI API调用失败
   - 敏感词API调用失败
   - 网络连接超时

4. **系统错误**
   - 内存不足
   - 配置加载失败
   - 未知异常

### Error Handling Strategy

```python
class ErrorHandler:
    def handle_validation_error(self, error: ValidationError) -> ErrorResponse
    def handle_file_processing_error(self, error: FileProcessingError) -> ErrorResponse
    def handle_external_service_error(self, error: ExternalServiceError) -> ErrorResponse
    def handle_system_error(self, error: SystemError) -> ErrorResponse
```

**错误处理原则**：
- 分层错误处理，每层负责特定类型的错误
- 错误信息脱敏，不暴露系统内部信息
- 提供友好的用户错误提示
- 完整的错误日志记录

### Fallback Mechanisms

1. **模型调用失败**：返回空检查结果，记录错误日志
2. **敏感词检测失败**：跳过敏感词检查，继续其他处理
3. **文件解析失败**：尝试备用解析方法
4. **网络超时**：自动重试机制（最多3次）

## Testing Strategy

### Unit Testing

**测试范围**：
- 各组件的核心功能
- 数据模型验证
- 工具函数

**测试工具**：pytest, pytest-asyncio, pytest-mock

**测试覆盖率目标**：≥85%

### Integration Testing

**测试范围**：
- API接口端到端测试
- 外部服务集成测试
- 文件处理流程测试

**测试数据**：
- 标准docx/pdf测试文件
- 各种项目类型参数组合
- 异常情况模拟数据

### Performance Testing

**测试指标**：
- 响应时间：≤60秒（大文件）
- 并发处理：支持10个并发请求
- 内存使用：≤2GB峰值

**测试工具**：locust, memory_profiler

### Security Testing

**测试范围**：
- 文件上传安全性
- API参数注入测试
- 敏感信息泄露检查

## Deployment Considerations

### Environment Configuration

```bash
# .env file structure
# AI Model Configuration
MODEL_APIKEY=sk-3WPbrV2cLkTChby7NaNVnGFAKEYw4yqudN1Mvlkd21eBpFAC
MODEL_NAME=deepseek-ai/DeepSeek-V3
MODEL_URL=http://localhost:3002/v1
MODEL_TOP_P=0.5
MODEL_SEED=42
MODEL_TEMPERATURE=0.0
MAX_CONTEXT_LENGTH=65536
MAX_OUTPUT_TOKENS=8192

# Sensitive Word API Configuration
SENSITIVE_WORD_API_URL=http://*************:8087
SENSITIVE_WORD_API_TIMEOUT=30
SENSITIVE_WORD_API_RETRIES=3

# File Processing Configuration
MAX_FILE_SIZE=50MB
SUPPORTED_FORMATS=docx,pdf
FILE_DOWNLOAD_TIMEOUT=60

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE_PATH=./logs/compliance_checker.log
LOG_RETENTION_DAYS=30

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
API_WORKERS=4
```

### Resource Requirements

- **CPU**: 2核心以上
- **内存**: 4GB以上
- **存储**: 10GB以上（日志和临时文件）
- **网络**: 稳定的外网连接（调用外部API）

### Monitoring and Alerting

- **健康检查接口**：/health
- **性能监控**：响应时间、错误率、并发数
- **日志监控**：错误日志、性能日志
- **告警机制**：API调用失败、响应时间超时