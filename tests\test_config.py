# -*- coding: utf-8 -*-
"""
配置模块测试
"""

import os
import pytest
from unittest.mock import patch

from app.core.config import Settings, validate_config


class TestSettings:
    """配置类测试"""

    def test_default_values(self):
        """测试默认值"""
        with patch.dict(os.environ, {"MODEL_APIKEY": "test-key"}):
            settings = Settings()

            assert settings.environment == "development"
            assert settings.debug is True
            assert settings.model_name == "gemini-2.5-flash"
            assert settings.model_url == "http://localhost:3002/v1"
            assert settings.model_top_p == 0.5
            assert settings.model_seed == 42
            assert settings.model_temperature == 0.0
            assert settings.max_context_length == 65536
            assert settings.max_output_tokens == 8192
            assert settings.sensitive_word_api_url == "http://*************:8087"
            assert settings.max_file_size == 300 * 1024 * 1024
            assert settings.allowed_extensions == [".docx", ".pdf"]
            assert settings.log_level == "INFO"
            assert settings.log_file_path == "./logs"
            assert settings.request_timeout == 300
            assert settings.max_retries == 3

    def test_environment_variables(self):
        """测试环境变量覆盖"""
        env_vars = {
            "MODEL_APIKEY": "custom-key",
            "MODEL_NAME": "custom-model",
            "MODEL_URL": "http://custom-url:8080/v1",
            "MODEL_TOP_P": "0.8",
            "MODEL_SEED": "123",
            "MODEL_TEMPERATURE": "0.5",
            "MAX_CONTEXT_LENGTH": "32768",
            "MAX_OUTPUT_TOKENS": "4096",
            "SENSITIVE_WORD_API_URL": "http://custom-sensitive-api:8087",
            "MAX_FILE_SIZE": "104857600",  # 100MB
            "LOG_LEVEL": "DEBUG",
            "LOG_FILE_PATH": "/custom/logs",
            "REQUEST_TIMEOUT": "600",
            "MAX_RETRIES": "5",
        }

        with patch.dict(os.environ, env_vars):
            settings = Settings()

            assert settings.model_apikey == "custom-key"
            assert settings.model_name == "custom-model"
            assert settings.model_url == "http://custom-url:8080/v1"
            assert settings.model_top_p == 0.8
            assert settings.model_seed == 123
            assert settings.model_temperature == 0.5
            assert settings.max_context_length == 32768
            assert settings.max_output_tokens == 4096
            assert settings.sensitive_word_api_url == "http://custom-sensitive-api:8087"
            assert settings.max_file_size == 104857600
            assert settings.log_level == "DEBUG"
            assert settings.log_file_path == "/custom/logs"
            assert settings.request_timeout == 600
            assert settings.max_retries == 5


class TestValidateConfig:
    """配置验证测试"""

    def test_validate_config_success(self):
        """测试配置验证成功"""
        with patch.dict(
            os.environ,
            {
                "MODEL_APIKEY": "test-key",
                "MODEL_NAME": "test-model",
                "MODEL_URL": "http://test-url:8080/v1",
            },
        ):
            result = validate_config()
            assert result is True

    def test_validate_config_missing_apikey(self):
        """测试缺少API密钥"""
        with patch.dict(os.environ, {}, clear=True):
            # 这会导致Pydantic验证失败，因为model_apikey是必需的
            with pytest.raises(Exception):
                Settings()

    def test_validate_config_invalid_file_size(self):
        """测试无效的文件大小"""
        with patch.dict(os.environ, {"MODEL_APIKEY": "test-key", "MAX_FILE_SIZE": "0"}):
            result = validate_config()
            assert result is False

    def test_validate_config_invalid_timeout(self):
        """测试无效的超时时间"""
        with patch.dict(
            os.environ, {"MODEL_APIKEY": "test-key", "REQUEST_TIMEOUT": "-1"}
        ):
            result = validate_config()
            assert result is False
