# 招标文件合规性检查助手 - 部署指南

## 概述

本文档提供招标文件合规性检查助手的完整部署指南，包括环境准备、配置、部署和运维等内容。

## 系统要求

### 硬件要求

- **CPU**: 4核心以上（推荐8核心）
- **内存**: 8GB以上（推荐16GB）
- **存储**: 50GB以上可用空间
- **网络**: 稳定的互联网连接

### 软件要求

- **操作系统**: Linux (Ubuntu 20.04+), Windows 10+, macOS 10.15+
- **Python**: 3.11+
- **Docker**: 20.10+ (可选)
- **Git**: 2.0+

## 环境准备

### 1. Python环境

```bash
# 检查Python版本
python --version

# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Linux/macOS:
source venv/bin/activate
# Windows:
venv\Scripts\activate

# 升级pip
pip install --upgrade pip
```

### 2. 依赖安装

```bash
# 安装项目依赖
pip install -r requirements.txt

# 安装开发依赖（可选）
pip install -r requirements-dev.txt
```

### 3. 环境变量配置

创建 `.env` 文件：

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量
nano .env
```

必需的环境变量：

```env
# AI模型配置
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_BASE_URL=https://api.openai.com/v1
MODEL_NAME=gpt-4

# 敏感词检测服务
SENSITIVE_WORD_API_URL=http://*************:8087

# 服务配置
HOST=0.0.0.0
PORT=8088
ENVIRONMENT=production

# 日志配置
LOG_LEVEL=INFO
LOG_FILE_PATH=logs/app.log

# 性能配置
MAX_FILE_SIZE=314572800  # 300MB
REQUEST_TIMEOUT=300
MAX_RETRIES=3

# 安全配置
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW_SECONDS=3600
```

## 部署方式

### 方式一：直接部署

#### 1. 克隆项目

```bash
git clone <repository_url>
cd jianchazhushou-plus
```

#### 2. 安装依赖

```bash
pip install -r requirements.txt
```

#### 3. 配置环境变量

```bash
cp .env.example .env
# 编辑 .env 文件，填入正确的配置
```

#### 4. 运行服务

```bash
# 开发模式
python main.py

# 生产模式
uvicorn main:app --host 0.0.0.0 --port 8088 --workers 4
```

### 方式二：Docker部署

#### 1. 构建镜像

```bash
# 构建Docker镜像
docker build -t bidding-compliance-checker .

# 或使用docker-compose
docker-compose build
```

#### 2. 运行容器

```bash
# 使用docker run
docker run -d \
  --name compliance-checker \
  -p 8088:8088 \
  --env-file .env \
  -v $(pwd)/logs:/app/logs \
  bidding-compliance-checker

# 或使用docker-compose
docker-compose up -d
```

#### 3. 查看日志

```bash
# 查看容器日志
docker logs compliance-checker

# 实时查看日志
docker logs -f compliance-checker
```

### 方式三：生产环境部署

#### 1. 使用Nginx反向代理

创建Nginx配置文件 `/etc/nginx/sites-available/compliance-checker`：

```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://127.0.0.1:8088;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 增加超时时间
        proxy_connect_timeout 300s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;
        
        # 增加请求体大小限制
        client_max_body_size 300M;
    }
}
```

启用配置：

```bash
sudo ln -s /etc/nginx/sites-available/compliance-checker /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

#### 2. 使用Systemd服务

创建服务文件 `/etc/systemd/system/compliance-checker.service`：

```ini
[Unit]
Description=Bidding Document Compliance Checker
After=network.target

[Service]
Type=exec
User=www-data
Group=www-data
WorkingDirectory=/path/to/jianchazhushou-plus
Environment=PATH=/path/to/jianchazhushou-plus/venv/bin
ExecStart=/path/to/jianchazhushou-plus/venv/bin/uvicorn main:app --host 0.0.0.0 --port 8088 --workers 4
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

启用服务：

```bash
sudo systemctl daemon-reload
sudo systemctl enable compliance-checker
sudo systemctl start compliance-checker
sudo systemctl status compliance-checker
```

## 配置说明

### 核心配置项

| 配置项 | 说明 | 默认值 | 必需 |
|--------|------|--------|------|
| OPENAI_API_KEY | OpenAI API密钥 | - | 是 |
| OPENAI_BASE_URL | OpenAI API基础URL | https://api.openai.com/v1 | 否 |
| MODEL_NAME | 使用的AI模型 | gpt-4 | 否 |
| SENSITIVE_WORD_API_URL | 敏感词检测服务URL | - | 是 |
| HOST | 服务监听地址 | 0.0.0.0 | 否 |
| PORT | 服务监听端口 | 8088 | 否 |
| MAX_FILE_SIZE | 最大文件大小(字节) | 314572800 | 否 |
| REQUEST_TIMEOUT | 请求超时时间(秒) | 300 | 否 |

### 性能优化配置

```env
# 异步处理
ENABLE_ASYNC_PROCESSING=true
ASYNC_WORKER_COUNT=4

# 缓存配置
ENABLE_DOCUMENT_CACHE=true
CACHE_MAX_SIZE_MB=1024

# 内存管理
MEMORY_THRESHOLD_MB=2048
ENABLE_AUTO_GC=true

# 限流配置
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW_SECONDS=3600
```

## 健康检查

### 基础健康检查

```bash
# 检查服务状态
curl http://localhost:8088/health

# 检查详细状态
curl http://localhost:8088/api/v1/service-status
```

### 监控指标

```bash
# 获取处理指标
curl http://localhost:8088/api/v1/processing-metrics

# 获取敏感词服务状态
curl http://localhost:8088/api/v1/sensitive-word-stats
```

## 日志管理

### 日志配置

日志文件位置：
- 应用日志: `logs/app.log`
- 错误日志: `logs/error.log`
- 访问日志: `logs/access.log`

### 日志轮转

使用logrotate配置日志轮转 `/etc/logrotate.d/compliance-checker`：

```
/path/to/jianchazhushou-plus/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
    postrotate
        systemctl reload compliance-checker
    endscript
}
```

### 日志监控

```bash
# 实时查看日志
tail -f logs/app.log

# 查看错误日志
tail -f logs/error.log

# 搜索特定错误
grep "ERROR" logs/app.log
```

## 备份和恢复

### 数据备份

```bash
#!/bin/bash
# backup.sh

BACKUP_DIR="/backup/compliance-checker"
DATE=$(date +%Y%m%d_%H%M%S)

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份配置文件
cp .env $BACKUP_DIR/env_$DATE
cp -r logs $BACKUP_DIR/logs_$DATE

# 备份代码（如果有本地修改）
tar -czf $BACKUP_DIR/code_$DATE.tar.gz --exclude=venv --exclude=__pycache__ .

echo "备份完成: $BACKUP_DIR"
```

### 恢复流程

```bash
# 1. 停止服务
sudo systemctl stop compliance-checker

# 2. 恢复配置
cp /backup/compliance-checker/env_YYYYMMDD_HHMMSS .env

# 3. 恢复代码（如需要）
tar -xzf /backup/compliance-checker/code_YYYYMMDD_HHMMSS.tar.gz

# 4. 重新安装依赖
pip install -r requirements.txt

# 5. 启动服务
sudo systemctl start compliance-checker
```

## 安全配置

### 1. 防火墙配置

```bash
# 允许HTTP端口
sudo ufw allow 80
sudo ufw allow 443

# 允许应用端口（仅内网）
sudo ufw allow from ***********/16 to any port 8088

# 启用防火墙
sudo ufw enable
```

### 2. SSL证书配置

使用Let's Encrypt获取免费SSL证书：

```bash
# 安装certbot
sudo apt install certbot python3-certbot-nginx

# 获取证书
sudo certbot --nginx -d your-domain.com

# 自动续期
sudo crontab -e
# 添加: 0 12 * * * /usr/bin/certbot renew --quiet
```

### 3. 安全头配置

在Nginx配置中添加安全头：

```nginx
add_header X-Frame-Options DENY;
add_header X-Content-Type-Options nosniff;
add_header X-XSS-Protection "1; mode=block";
add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";
add_header Content-Security-Policy "default-src 'self'";
```

## 故障排除

### 常见问题

#### 1. 服务无法启动

```bash
# 检查端口占用
netstat -tlnp | grep 8088

# 检查日志
tail -f logs/app.log

# 检查配置
python -c "from app.core.config import settings; print(settings)"
```

#### 2. AI模型调用失败

```bash
# 测试API连接
curl -H "Authorization: Bearer $OPENAI_API_KEY" \
     -H "Content-Type: application/json" \
     -d '{"model":"gpt-3.5-turbo","messages":[{"role":"user","content":"test"}],"max_tokens":5}' \
     https://api.openai.com/v1/chat/completions
```

#### 3. 敏感词服务连接失败

```bash
# 测试敏感词服务
curl http://*************:8087/health

# 检查网络连接
ping *************
telnet ************* 8087
```

#### 4. 文件处理失败

```bash
# 检查文件权限
ls -la logs/
ls -la temp/

# 检查磁盘空间
df -h

# 检查内存使用
free -h
```

### 性能调优

#### 1. 内存优化

```env
# 调整内存阈值
MEMORY_THRESHOLD_MB=4096

# 启用自动垃圾回收
ENABLE_AUTO_GC=true

# 调整工作进程数
ASYNC_WORKER_COUNT=8
```

#### 2. 并发优化

```bash
# 使用更多工作进程
uvicorn main:app --host 0.0.0.0 --port 8088 --workers 8

# 调整系统限制
ulimit -n 65536
```

#### 3. 缓存优化

```env
# 启用文档缓存
ENABLE_DOCUMENT_CACHE=true
CACHE_MAX_SIZE_MB=2048

# 调整缓存策略
CACHE_TTL_SECONDS=3600
```

## 监控和告警

### 1. 基础监控

创建监控脚本 `scripts/monitor.sh`：

```bash
#!/bin/bash

# 检查服务状态
if ! curl -f http://localhost:8088/health > /dev/null 2>&1; then
    echo "服务健康检查失败" | mail -s "服务告警" <EMAIL>
fi

# 检查磁盘空间
DISK_USAGE=$(df / | tail -1 | awk '{print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 80 ]; then
    echo "磁盘使用率过高: $DISK_USAGE%" | mail -s "磁盘告警" <EMAIL>
fi

# 检查内存使用
MEMORY_USAGE=$(free | grep Mem | awk '{printf("%.0f", $3/$2 * 100.0)}')
if [ $MEMORY_USAGE -gt 90 ]; then
    echo "内存使用率过高: $MEMORY_USAGE%" | mail -s "内存告警" <EMAIL>
fi
```

### 2. 日志监控

使用logwatch或自定义脚本监控错误日志：

```bash
# 检查错误日志
ERROR_COUNT=$(grep -c "ERROR" logs/app.log)
if [ $ERROR_COUNT -gt 10 ]; then
    echo "错误日志过多: $ERROR_COUNT" | mail -s "错误告警" <EMAIL>
fi
```

## 更新和维护

### 1. 版本更新

```bash
# 1. 备份当前版本
./scripts/backup.sh

# 2. 停止服务
sudo systemctl stop compliance-checker

# 3. 更新代码
git pull origin main

# 4. 更新依赖
pip install -r requirements.txt

# 5. 运行测试
python scripts/run_all_tests.py

# 6. 启动服务
sudo systemctl start compliance-checker

# 7. 验证更新
curl http://localhost:8088/health
```

### 2. 定期维护

创建维护脚本 `scripts/maintenance.sh`：

```bash
#!/bin/bash

echo "开始定期维护..."

# 清理临时文件
find temp/ -type f -mtime +7 -delete

# 清理旧日志
find logs/ -name "*.log.*" -mtime +30 -delete

# 清理缓存
rm -rf __pycache__/
find . -name "*.pyc" -delete

# 检查磁盘空间
df -h

echo "维护完成"
```

### 3. 性能监控

定期检查性能指标：

```bash
# 获取处理指标
curl http://localhost:8088/api/v1/processing-metrics

# 检查响应时间
time curl http://localhost:8088/health

# 检查内存使用
ps aux | grep python | grep main.py
```

## 联系支持

如果遇到部署问题，请：

1. 查看日志文件获取详细错误信息
2. 检查配置文件是否正确
3. 确认所有依赖服务正常运行
4. 联系技术支持团队

---

**注意**: 本部署指南基于当前版本编写，请根据实际环境调整配置参数。