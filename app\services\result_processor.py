# -*- coding: utf-8 -*-
"""
结果处理和格式化服务
整合AI模型和敏感词检测结果
"""

from typing import List, Dict, Any, Optional
from datetime import datetime

from app.core.logger import log, performance_logger, TimingContext, log_function_call
from app.core.error_logger import error_logger
from app.models.schemas import (
    ComplianceCheckResponse,
    ComplianceCheckData,
    SensitiveWordItem,
    CheckResultItem,
    ProjectInfo,
)
from app.models.enums import QuestionType


class ResultProcessingError(Exception):
    """结果处理异常"""

    def __init__(
        self,
        message: str,
        stage: str = "",
        data: Any = None,
        original_error: Exception = None,
    ):
        """
        初始化结果处理异常

        Args:
            message: 错误消息
            stage: 处理阶段
            data: 相关数据
            original_error: 原始异常
        """
        super().__init__(message)
        self.stage = stage
        self.data = data
        self.original_error = original_error
        self.error_type = "RESULT_PROCESSING_ERROR"

    def __str__(self):
        base_msg = super().__str__()
        details = []

        if self.stage:
            details.append(f"阶段: {self.stage}")
        if self.original_error:
            details.append(f"原因: {str(self.original_error)}")

        if details:
            return f"{base_msg} ({', '.join(details)})"
        return base_msg


class ResultProcessor:
    """结果处理器"""

    def __init__(self):
        """初始化结果处理器"""
        self.default_response = ComplianceCheckResponse(
            sensitiveWordsArr=[], checkResultArr=[]
        )

    @log_function_call
    def validate_sensitive_words(
        self, sensitive_words: List[SensitiveWordItem]
    ) -> List[SensitiveWordItem]:
        """
        验证和清理敏感词数据

        Args:
            sensitive_words: 敏感词列表

        Returns:
            List[SensitiveWordItem]: 验证后的敏感词列表
        """
        try:
            validated_words = []

            for word in sensitive_words:
                # 验证必要字段
                if not word.content or not word.content.strip():
                    log.warning(f"跳过空内容的敏感词: {word}")
                    continue

                if word.num <= 0:
                    log.warning(f"跳过出现次数无效的敏感词: {word}")
                    continue

                if not word.type or not word.type.strip():
                    log.warning(f"敏感词类型为空，使用默认值: {word}")
                    word.type = "其他"

                # 清理数据
                cleaned_word = SensitiveWordItem(
                    type=word.type.strip(),
                    content=word.content.strip(),
                    num=max(1, word.num),  # 确保至少为1
                )

                validated_words.append(cleaned_word)

            log.info(
                f"敏感词验证完成: 原始{len(sensitive_words)}个，有效{len(validated_words)}个"
            )
            return validated_words

        except Exception as e:
            error_logger.log_data_processing_error(
                "敏感词验证",
                sensitive_words,
                e,
                partial_output=(
                    validated_words if "validated_words" in locals() else None
                ),
            )
            raise ResultProcessingError(
                f"敏感词验证失败: {str(e)}",
                stage="敏感词验证",
                data=sensitive_words,
                original_error=e,
            )

    @log_function_call
    def validate_check_results(
        self, check_results: List[CheckResultItem]
    ) -> List[CheckResultItem]:
        """
        验证和清理检查结果数据

        Args:
            check_results: 检查结果列表

        Returns:
            List[CheckResultItem]: 验证后的检查结果列表
        """
        try:
            validated_results = []

            for i, result in enumerate(check_results):
                # 详细记录每个检查结果的字段状态
                log.debug(
                    f"验证检查结果 {i+1}: quesType='{result.quesType}', quesDesc='{result.quesDesc[:50] if result.quesDesc else 'None'}...', point='{result.point[:50] if result.point else 'None'}...', advice='{result.advice[:50] if result.advice else 'None'}...', originalArr={len(result.originalArr) if result.originalArr else 0}个元素"
                )

                # 验证必要字段
                if not result.quesDesc or not result.quesDesc.strip():
                    log.warning(
                        f"跳过空描述的检查结果 {i+1}: quesType='{result.quesType}', quesDesc='{result.quesDesc}', point='{result.point}', advice='{result.advice}', originalArr={result.originalArr}"
                    )
                    continue

                if not result.point or not result.point.strip():
                    log.warning(
                        f"跳过空质量控制要点的检查结果 {i+1}: quesType='{result.quesType}', quesDesc='{result.quesDesc[:50]}...', point='{result.point}', advice='{result.advice[:50]}...', originalArr={len(result.originalArr) if result.originalArr else 0}个元素"
                    )
                    continue

                if not result.advice or not result.advice.strip():
                    log.warning(
                        f"跳过空处理建议的检查结果 {i+1}: quesType='{result.quesType}', quesDesc='{result.quesDesc[:50]}...', point='{result.point[:50]}...', advice='{result.advice}', originalArr={len(result.originalArr) if result.originalArr else 0}个元素"
                    )
                    continue

                # 验证问题类型
                valid_types = [e.value for e in QuestionType]
                if result.quesType not in valid_types:
                    log.warning(f"无效的问题类型: {result.quesType}，使用默认值")
                    result.quesType = QuestionType.STANDARDIZATION.value

                # 验证原文内容
                if not result.originalArr:
                    log.warning(f"检查结果缺少原文内容: {result}")
                    result.originalArr = ["原文内容未指定"]

                # 清理数据
                cleaned_result = CheckResultItem(
                    quesType=result.quesType,
                    quesDesc=result.quesDesc.strip(),
                    originalArr=[
                        pos.strip() for pos in result.originalArr if pos and pos.strip()
                    ],
                    point=result.point.strip(),
                    advice=result.advice.strip(),
                )

                # 确保原文内容不为空
                if not cleaned_result.originalArr:
                    cleaned_result.originalArr = ["原文内容未指定"]

                validated_results.append(cleaned_result)

            log.info(
                f"检查结果验证完成: 原始{len(check_results)}个，有效{len(validated_results)}个"
            )
            return validated_results

        except Exception as e:
            log.error(f"检查结果验证失败: {str(e)}")
            raise ResultProcessingError(
                f"检查结果验证失败: {str(e)}",
                stage="检查结果验证",
                data=check_results,
                original_error=e,
            )

    @log_function_call
    def deduplicate_sensitive_words(
        self, sensitive_words: List[SensitiveWordItem]
    ) -> List[SensitiveWordItem]:
        """
        去重敏感词，合并相同内容的敏感词

        Args:
            sensitive_words: 敏感词列表

        Returns:
            List[SensitiveWordItem]: 去重后的敏感词列表
        """
        try:
            word_map = {}

            for word in sensitive_words:
                key = (word.type, word.content)

                if key in word_map:
                    # 合并出现次数
                    word_map[key].num += word.num
                else:
                    word_map[key] = SensitiveWordItem(
                        type=word.type, content=word.content, num=word.num
                    )

            deduplicated = list(word_map.values())

            # 按出现次数降序排序
            deduplicated.sort(key=lambda x: x.num, reverse=True)

            log.info(
                f"敏感词去重完成: 原始{len(sensitive_words)}个，去重后{len(deduplicated)}个"
            )
            return deduplicated

        except Exception as e:
            log.error(f"敏感词去重失败: {str(e)}")
            return sensitive_words  # 失败时返回原始数据

    @log_function_call
    def prioritize_check_results(
        self, check_results: List[CheckResultItem]
    ) -> List[CheckResultItem]:
        """
        对检查结果进行优先级排序

        Args:
            check_results: 检查结果列表

        Returns:
            List[CheckResultItem]: 排序后的检查结果列表
        """
        try:
            # 定义问题类型优先级
            priority_map = {
                QuestionType.COMPLIANCE.value: 1,  # 合规性最高
                QuestionType.RISK_MANAGEMENT.value: 2,  # 风险管理次之
                QuestionType.LOGIC.value: 3,  # 逻辑性
                QuestionType.FAIRNESS.value: 4,  # 公平性
                QuestionType.STANDARDIZATION.value: 5,  # 规范性
                QuestionType.OPERABILITY.value: 6,  # 可操作性最低
            }

            # 按优先级排序
            sorted_results = sorted(
                check_results, key=lambda x: priority_map.get(x.quesType, 999)
            )

            log.info(f"检查结果排序完成: {len(sorted_results)}个结果")
            return sorted_results

        except Exception as e:
            log.error(f"检查结果排序失败: {str(e)}")
            return check_results  # 失败时返回原始数据

    @log_function_call
    def limit_results(
        self, check_results: List[CheckResultItem], max_results: int = 15
    ) -> List[CheckResultItem]:
        """
        限制检查结果数量

        Args:
            check_results: 检查结果列表
            max_results: 最大结果数量

        Returns:
            List[CheckResultItem]: 限制后的检查结果列表
        """
        if len(check_results) <= max_results:
            return check_results

        limited_results = check_results[:max_results]
        log.warning(
            f"检查结果数量超限，已限制为{max_results}个（原始{len(check_results)}个）"
        )

        return limited_results

    @log_function_call
    def aggregate_results(
        self,
        sensitive_words: List[SensitiveWordItem],
        check_results: List[CheckResultItem],
        request_id: str = "",
    ) -> ComplianceCheckResponse:
        """
        聚合AI模型和敏感词检测结果（带完整性验证）

        Args:
            sensitive_words: 敏感词列表
            check_results: 检查结果列表
            request_id: 请求ID

        Returns:
            ComplianceCheckResponse: 聚合后的结果
        """
        try:
            with TimingContext("结果聚合", request_id):
                log.info(
                    f"开始结果聚合: 敏感词{len(sensitive_words)}个，检查结果{len(check_results)}个 | ID: {request_id}"
                )

                # 记录输入数据的完整性基线
                input_integrity = self._create_integrity_baseline(
                    sensitive_words, check_results, request_id
                )

                # 验证和清理敏感词
                validated_sensitive_words = self.validate_sensitive_words(
                    sensitive_words
                )
                self._verify_processing_integrity(
                    "敏感词验证", sensitive_words, validated_sensitive_words, request_id
                )

                # 去重敏感词
                deduplicated_sensitive_words = self.deduplicate_sensitive_words(
                    validated_sensitive_words
                )
                self._verify_deduplication_integrity(
                    validated_sensitive_words, deduplicated_sensitive_words, request_id
                )

                # 验证和清理检查结果
                validated_check_results = self.validate_check_results(check_results)
                self._verify_processing_integrity(
                    "检查结果验证", check_results, validated_check_results, request_id
                )

                # 排序检查结果
                prioritized_check_results = self.prioritize_check_results(
                    validated_check_results
                )
                self._verify_processing_integrity(
                    "检查结果排序",
                    validated_check_results,
                    prioritized_check_results,
                    request_id,
                )

                # 限制结果数量
                limited_check_results = self.limit_results(prioritized_check_results)
                if len(limited_check_results) < len(prioritized_check_results):
                    log.warning(
                        f"检查结果被限制: {len(prioritized_check_results)} -> {len(limited_check_results)} | ID: {request_id}"
                    )

                # 创建最终响应
                response = ComplianceCheckResponse(
                    code=200,
                    message="合规性检查完成",
                    data=ComplianceCheckData(
                        sensitiveWordsArr=deduplicated_sensitive_words,
                        checkResultArr=limited_check_results,
                    ),
                )

                # 验证最终输出的完整性
                self._verify_final_integrity(input_integrity, response, request_id)

                log.info(
                    f"结果聚合完成: 敏感词{len(response.data.sensitiveWordsArr)}个，"
                    f"检查结果{len(response.data.checkResultArr)}个 | ID: {request_id}"
                )

                return response

        except ResultProcessingError:
            # 重新抛出结果处理异常
            raise
        except Exception as e:
            error_logger.log_data_processing_error(
                "结果聚合",
                {
                    "sensitive_words": len(sensitive_words),
                    "check_results": len(check_results),
                },
                e,
                request_id,
            )
            raise ResultProcessingError(
                f"结果聚合失败: {str(e)}", stage="结果聚合", original_error=e
            )

    @log_function_call
    def create_empty_response(self, reason: str = "") -> ComplianceCheckResponse:
        """
        创建空响应

        Args:
            reason: 创建空响应的原因

        Returns:
            ComplianceCheckResponse: 空响应
        """
        log.info(f"创建空响应: {reason}")
        return ComplianceCheckResponse(
            code=200,
            message=reason or "处理完成",
            data=ComplianceCheckData(sensitiveWordsArr=[], checkResultArr=[]),
        )

    @log_function_call
    def validate_final_response(
        self, response: ComplianceCheckResponse
    ) -> ComplianceCheckResponse:
        """
        验证最终响应格式

        Args:
            response: 响应对象

        Returns:
            ComplianceCheckResponse: 验证后的响应
        """
        try:
            # 确保字段不为None
            if response.data.sensitiveWordsArr is None:
                response.data.sensitiveWordsArr = []

            if response.data.checkResultArr is None:
                response.data.checkResultArr = []

            # 验证每个敏感词项
            validated_sensitive_words = []
            for word in response.data.sensitiveWordsArr:
                if (
                    isinstance(word, SensitiveWordItem)
                    and word.content
                    and word.num > 0
                ):
                    validated_sensitive_words.append(word)

            # 验证每个检查结果项
            validated_check_results = []
            for result in response.data.checkResultArr:
                if (
                    isinstance(result, CheckResultItem)
                    and result.quesDesc
                    and result.point
                    and result.advice
                ):
                    validated_check_results.append(result)

            # 创建验证后的响应
            validated_response = ComplianceCheckResponse(
                code=response.code,
                message=response.message,
                data=ComplianceCheckData(
                    sensitiveWordsArr=validated_sensitive_words,
                    checkResultArr=validated_check_results,
                ),
            )

            log.info(
                f"响应验证完成: 敏感词{len(validated_response.data.sensitiveWordsArr)}个，"
                f"检查结果{len(validated_response.data.checkResultArr)}个"
            )

            return validated_response

        except Exception as e:
            log.error(f"响应验证失败: {str(e)}")
            return self.create_empty_response("响应验证失败")

    @log_function_call
    def process_with_fallback(
        self,
        sensitive_words: List[SensitiveWordItem],
        check_results: List[CheckResultItem],
        request_id: str = "",
    ) -> ComplianceCheckResponse:
        """
        带降级机制的结果处理

        Args:
            sensitive_words: 敏感词列表
            check_results: 检查结果列表
            request_id: 请求ID

        Returns:
            ComplianceCheckResponse: 处理后的结果
        """
        try:
            # 尝试正常聚合
            response = self.aggregate_results(
                sensitive_words, check_results, request_id
            )

            # 验证最终响应
            validated_response = self.validate_final_response(response)

            return validated_response

        except ResultProcessingError as e:
            log.error(f"结果处理失败，使用降级策略: {str(e)}")

            # 降级策略：尝试部分处理
            try:
                # 只处理敏感词
                if sensitive_words:
                    validated_sensitive_words = self.validate_sensitive_words(
                        sensitive_words
                    )
                    deduplicated_sensitive_words = self.deduplicate_sensitive_words(
                        validated_sensitive_words
                    )
                else:
                    deduplicated_sensitive_words = []

                # 只处理检查结果
                if check_results:
                    validated_check_results = self.validate_check_results(check_results)
                    limited_check_results = self.limit_results(validated_check_results)
                else:
                    limited_check_results = []

                return ComplianceCheckResponse(
                    code=200,
                    message="合规性检查完成",
                    data=ComplianceCheckData(
                        sensitiveWordsArr=deduplicated_sensitive_words,
                        checkResultArr=limited_check_results,
                    ),
                )

            except Exception as fallback_error:
                log.error(f"降级处理也失败: {str(fallback_error)}")
                return self.create_empty_response("所有处理策略都失败")

        except Exception as e:
            log.error(f"结果处理异常: {str(e)}")
            return self.create_empty_response("处理过程中发生异常")

    def _create_integrity_baseline(
        self,
        sensitive_words: List[SensitiveWordItem],
        check_results: List[CheckResultItem],
        request_id: str,
    ) -> Dict[str, Any]:
        """
        创建完整性验证基线

        Args:
            sensitive_words: 敏感词列表
            check_results: 检查结果列表
            request_id: 请求ID

        Returns:
            Dict[str, Any]: 完整性基线数据
        """
        baseline = {
            "request_id": request_id,
            "timestamp": datetime.now().isoformat(),
            "sensitive_words": {
                "count": len(sensitive_words),
                "total_occurrences": sum(
                    word.num for word in sensitive_words if word.num
                ),
                "unique_contents": set(
                    word.content for word in sensitive_words if word.content
                ),
                "types": set(word.type for word in sensitive_words if word.type),
            },
            "check_results": {
                "count": len(check_results),
                "types": set(
                    result.quesType for result in check_results if result.quesType
                ),
                "unique_descriptions": set(
                    result.quesDesc for result in check_results if result.quesDesc
                ),
            },
        }

        log.debug(
            f"创建完整性基线 | ID: {request_id} | 敏感词: {baseline['sensitive_words']['count']}个 | 检查结果: {baseline['check_results']['count']}个"
        )
        return baseline

    def _verify_processing_integrity(
        self, stage: str, input_data: List[Any], output_data: List[Any], request_id: str
    ):
        """
        验证处理阶段的完整性

        Args:
            stage: 处理阶段名称
            input_data: 输入数据
            output_data: 输出数据
            request_id: 请求ID
        """
        input_count = len(input_data)
        output_count = len(output_data)

        if output_count > input_count:
            log.warning(
                f"完整性警告 | {stage} | ID: {request_id} | "
                f"输出数量({output_count}) > 输入数量({input_count})，可能存在重复数据"
            )
        elif output_count < input_count:
            lost_count = input_count - output_count
            loss_rate = (lost_count / input_count) * 100 if input_count > 0 else 0

            if loss_rate > 50:  # 如果丢失超过50%，记录错误
                log.error(
                    f"完整性错误 | {stage} | ID: {request_id} | "
                    f"数据丢失严重: {lost_count}个 ({loss_rate:.1f}%)"
                )
            elif loss_rate > 10:  # 如果丢失超过10%，记录警告
                log.warning(
                    f"完整性警告 | {stage} | ID: {request_id} | "
                    f"数据丢失: {lost_count}个 ({loss_rate:.1f}%)"
                )
            else:
                log.debug(
                    f"完整性检查 | {stage} | ID: {request_id} | "
                    f"轻微数据丢失: {lost_count}个 ({loss_rate:.1f}%)"
                )

    def _verify_deduplication_integrity(
        self,
        input_words: List[SensitiveWordItem],
        output_words: List[SensitiveWordItem],
        request_id: str,
    ):
        """
        验证去重过程的完整性

        Args:
            input_words: 输入敏感词列表
            output_words: 输出敏感词列表
            request_id: 请求ID
        """
        # 计算输入和输出的总出现次数
        input_total_occurrences = sum(word.num for word in input_words if word.num)
        output_total_occurrences = sum(word.num for word in output_words if word.num)

        # 检查内容完整性
        input_contents = set(word.content for word in input_words if word.content)
        output_contents = set(word.content for word in output_words if word.content)

        lost_contents = input_contents - output_contents
        if lost_contents:
            log.warning(
                f"去重完整性警告 | ID: {request_id} | "
                f"丢失敏感词内容: {len(lost_contents)}个 | 内容: {list(lost_contents)[:5]}"
            )

        # 检查出现次数的一致性
        if output_total_occurrences != input_total_occurrences:
            log.info(
                f"去重统计 | ID: {request_id} | "
                f"出现次数变化: {input_total_occurrences} -> {output_total_occurrences}"
            )

    def _verify_final_integrity(
        self,
        baseline: Dict[str, Any],
        response: ComplianceCheckResponse,
        request_id: str,
    ):
        """
        验证最终输出的完整性

        Args:
            baseline: 完整性基线
            response: 最终响应
            request_id: 请求ID
        """
        # 验证敏感词完整性
        final_sensitive_words = response.data.sensitiveWordsArr
        final_contents = set(
            word.content for word in final_sensitive_words if word.content
        )
        baseline_contents = baseline["sensitive_words"]["unique_contents"]

        lost_sensitive_contents = baseline_contents - final_contents
        if lost_sensitive_contents:
            log.error(
                f"最终完整性错误 | ID: {request_id} | "
                f"敏感词内容丢失: {len(lost_sensitive_contents)}个 | "
                f"丢失内容: {list(lost_sensitive_contents)[:3]}"
            )

        # 验证检查结果完整性
        final_check_results = response.data.checkResultArr
        final_descriptions = set(
            result.quesDesc for result in final_check_results if result.quesDesc
        )
        baseline_descriptions = baseline["check_results"]["unique_descriptions"]

        # 检查结果可能因为限制数量而减少，这是正常的
        if len(final_check_results) < baseline["check_results"]["count"]:
            log.info(
                f"检查结果数量限制 | ID: {request_id} | "
                f"{baseline['check_results']['count']} -> {len(final_check_results)}"
            )

        # 记录最终完整性报告
        integrity_report = {
            "request_id": request_id,
            "baseline_sensitive_words": baseline["sensitive_words"]["count"],
            "final_sensitive_words": len(final_sensitive_words),
            "baseline_check_results": baseline["check_results"]["count"],
            "final_check_results": len(final_check_results),
            "sensitive_word_loss_rate": (
                (baseline["sensitive_words"]["count"] - len(final_sensitive_words))
                / max(baseline["sensitive_words"]["count"], 1)
            )
            * 100,
            "check_result_loss_rate": (
                (baseline["check_results"]["count"] - len(final_check_results))
                / max(baseline["check_results"]["count"], 1)
            )
            * 100,
        }

        log.info(
            f"完整性报告 | ID: {request_id} | "
            f"敏感词保留率: {100 - integrity_report['sensitive_word_loss_rate']:.1f}% | "
            f"检查结果保留率: {100 - integrity_report['check_result_loss_rate']:.1f}%"
        )

    def _verify_processing_integrity(
        self, stage_name: str, input_data: Any, output_data: Any, request_id: str = ""
    ) -> Dict[str, Any]:
        """
        验证处理过程的完整性

        Args:
            stage_name: 处理阶段名称
            input_data: 输入数据
            output_data: 输出数据
            request_id: 请求ID

        Returns:
            Dict[str, Any]: 完整性验证结果
        """
        integrity_result = {
            "stage": stage_name,
            "request_id": request_id,
            "input_count": 0,
            "output_count": 0,
            "data_loss_count": 0,
            "data_loss_percentage": 0.0,
            "integrity_status": "unknown",
            "warnings": [],
            "errors": [],
        }

        try:
            # 计算输入和输出数据量
            if hasattr(input_data, "__len__"):
                integrity_result["input_count"] = len(input_data)
            elif input_data is not None:
                integrity_result["input_count"] = 1

            if hasattr(output_data, "__len__"):
                integrity_result["output_count"] = len(output_data)
            elif output_data is not None:
                integrity_result["output_count"] = 1

            # 计算数据丢失
            input_count = integrity_result["input_count"]
            output_count = integrity_result["output_count"]

            if input_count > 0:
                integrity_result["data_loss_count"] = max(0, input_count - output_count)
                integrity_result["data_loss_percentage"] = (
                    integrity_result["data_loss_count"] / input_count * 100
                )

            # 判断完整性状态
            loss_percentage = integrity_result["data_loss_percentage"]

            if loss_percentage == 0:
                integrity_result["integrity_status"] = "perfect"
                log.debug(
                    f"完整性验证 | {stage_name} | ID: {request_id} | "
                    f"状态: 完美 | 输入: {input_count} | 输出: {output_count}"
                )
            elif loss_percentage <= 10:  # 10%以下为轻微丢失
                integrity_result["integrity_status"] = "minor_loss"
                integrity_result["warnings"].append(
                    f"轻微数据丢失: {loss_percentage:.1f}%"
                )
                log.warning(
                    f"完整性验证 | {stage_name} | ID: {request_id} | "
                    f"状态: 轻微丢失 | 输入: {input_count} | 输出: {output_count} | "
                    f"丢失: {integrity_result['data_loss_count']} ({loss_percentage:.1f}%)"
                )
            elif loss_percentage <= 50:  # 50%以下为严重丢失
                integrity_result["integrity_status"] = "major_loss"
                integrity_result["errors"].append(
                    f"严重数据丢失: {loss_percentage:.1f}%"
                )
                log.error(
                    f"完整性验证 | {stage_name} | ID: {request_id} | "
                    f"状态: 严重丢失 | 输入: {input_count} | 输出: {output_count} | "
                    f"丢失: {integrity_result['data_loss_count']} ({loss_percentage:.1f}%)"
                )
            else:  # 50%以上为灾难性丢失
                integrity_result["integrity_status"] = "catastrophic_loss"
                integrity_result["errors"].append(
                    f"灾难性数据丢失: {loss_percentage:.1f}%"
                )
                log.error(
                    f"完整性验证 | {stage_name} | ID: {request_id} | "
                    f"状态: 灾难性丢失 | 输入: {input_count} | 输出: {output_count} | "
                    f"丢失: {integrity_result['data_loss_count']} ({loss_percentage:.1f}%)"
                )

        except Exception as e:
            integrity_result["integrity_status"] = "verification_failed"
            integrity_result["errors"].append(f"完整性验证失败: {str(e)}")
            log.error(
                f"完整性验证异常 | {stage_name} | ID: {request_id} | 错误: {str(e)}"
            )

        return integrity_result

    def _create_integrity_baseline(
        self,
        sensitive_words: List[SensitiveWordItem],
        check_results: List[CheckResultItem],
        request_id: str = "",
    ) -> Dict[str, Any]:
        """
        创建完整性验证基线

        Args:
            sensitive_words: 敏感词列表
            check_results: 检查结果列表
            request_id: 请求ID

        Returns:
            Dict[str, Any]: 完整性基线
        """
        # 分析敏感词数据
        sensitive_word_types = set()
        sensitive_word_contents = set()
        total_occurrences = 0

        for word in sensitive_words:
            sensitive_word_types.add(word.type)
            sensitive_word_contents.add(word.content)
            total_occurrences += word.num

        # 分析检查结果数据
        check_result_types = set()
        check_result_descriptions = set()

        for result in check_results:
            check_result_types.add(result.quesType)
            check_result_descriptions.add(result.quesDesc)

        baseline = {
            "request_id": request_id,
            "timestamp": datetime.now().isoformat(),
            "sensitive_words": {
                "count": len(sensitive_words),
                "total_occurrences": total_occurrences,
                "unique_contents": set(sensitive_word_contents),
                "types": set(sensitive_word_types),
            },
            "check_results": {
                "count": len(check_results),
                "types": set(check_result_types),
                "unique_descriptions": set(check_result_descriptions),
            },
        }

        log.debug(
            f"完整性基线创建 | ID: {request_id} | "
            f"敏感词: {baseline['sensitive_words']['count']}个 | "
            f"检查结果: {baseline['check_results']['count']}个"
        )

        return baseline

    def _verify_deduplication_integrity(
        self,
        input_words: List[SensitiveWordItem],
        output_words: List[SensitiveWordItem],
        request_id: str = "",
    ) -> Dict[str, Any]:
        """
        验证去重过程的完整性

        Args:
            input_words: 输入的敏感词列表
            output_words: 输出的敏感词列表
            request_id: 请求ID

        Returns:
            Dict[str, Any]: 去重完整性验证结果
        """
        integrity_result = {
            "request_id": request_id,
            "input_count": len(input_words),
            "output_count": len(output_words),
            "input_total_occurrences": sum(word.num for word in input_words),
            "output_total_occurrences": sum(word.num for word in output_words),
            "unique_content_input": len(set(word.content for word in input_words)),
            "unique_content_output": len(set(word.content for word in output_words)),
            "integrity_status": "unknown",
            "warnings": [],
            "errors": [],
        }

        try:
            input_total = integrity_result["input_total_occurrences"]
            output_total = integrity_result["output_total_occurrences"]

            # 检查总出现次数是否保持一致
            if input_total == output_total:
                integrity_result["integrity_status"] = "perfect"
                log.info(
                    f"去重完整性验证 | ID: {request_id} | "
                    f"状态: 完美 | 输入: {integrity_result['input_count']}个 | "
                    f"输出: {integrity_result['output_count']}个 | "
                    f"出现次数变化: {input_total} -> {output_total}"
                )
            elif output_total < input_total:
                loss_count = input_total - output_total
                loss_percentage = (
                    (loss_count / input_total * 100) if input_total > 0 else 0
                )

                if loss_percentage <= 5:  # 5%以下为轻微丢失
                    integrity_result["integrity_status"] = "minor_loss"
                    integrity_result["warnings"].append(
                        f"轻微出现次数丢失: {loss_count}次 ({loss_percentage:.1f}%)"
                    )
                    log.warning(
                        f"去重完整性验证 | ID: {request_id} | "
                        f"状态: 轻微丢失 | 出现次数变化: {input_total} -> {output_total} | "
                        f"丢失: {loss_count}次 ({loss_percentage:.1f}%)"
                    )
                else:
                    integrity_result["integrity_status"] = "major_loss"
                    integrity_result["errors"].append(
                        f"严重出现次数丢失: {loss_count}次 ({loss_percentage:.1f}%)"
                    )
                    log.error(
                        f"去重完整性验证 | ID: {request_id} | "
                        f"状态: 严重丢失 | 出现次数变化: {input_total} -> {output_total} | "
                        f"丢失: {loss_count}次 ({loss_percentage:.1f}%)"
                    )
            else:
                # 出现次数增加（理论上不应该发生）
                integrity_result["integrity_status"] = "unexpected_increase"
                integrity_result["warnings"].append(
                    f"意外的出现次数增加: {output_total - input_total}次"
                )
                log.warning(
                    f"去重完整性验证 | ID: {request_id} | "
                    f"状态: 意外增加 | 出现次数变化: {input_total} -> {output_total}"
                )

            # 检查内容完整性
            input_contents = set(word.content for word in input_words)
            output_contents = set(word.content for word in output_words)
            missing_contents = input_contents - output_contents

            if missing_contents:
                integrity_result["warnings"].append(
                    f"内容丢失: {len(missing_contents)}个唯一内容"
                )
                log.warning(
                    f"丢失敏感词内容 | ID: {request_id} | "
                    f"丢失内容: {list(missing_contents)[:5]}..."  # 只显示前5个
                )

        except Exception as e:
            integrity_result["integrity_status"] = "verification_failed"
            integrity_result["errors"].append(f"去重完整性验证失败: {str(e)}")
            log.error(f"去重完整性验证异常 | ID: {request_id} | 错误: {str(e)}")

        return integrity_result

    def aggregate_results_with_integrity_check(
        self,
        sensitive_words: List[SensitiveWordItem],
        check_results: List[CheckResultItem],
        request_id: str = "",
    ) -> ComplianceCheckResponse:
        """
        带完整性检查的结果聚合

        Args:
            sensitive_words: 敏感词列表
            check_results: 检查结果列表
            request_id: 请求ID

        Returns:
            ComplianceCheckResponse: 聚合后的结果
        """
        try:
            with TimingContext("带完整性检查的结果聚合", request_id):
                log.info(
                    f"开始带完整性检查的结果聚合 | ID: {request_id} | "
                    f"敏感词: {len(sensitive_words)}个 | 检查结果: {len(check_results)}个"
                )

                # 1. 创建完整性基线
                baseline = self._create_integrity_baseline(
                    sensitive_words, check_results, request_id
                )

                # 2. 验证和清理敏感词
                validated_sensitive_words = self.validate_sensitive_words(
                    sensitive_words
                )

                # 验证敏感词处理完整性
                sw_integrity = self._verify_processing_integrity(
                    "敏感词验证", sensitive_words, validated_sensitive_words, request_id
                )

                # 3. 去重敏感词
                deduplicated_sensitive_words = self.deduplicate_sensitive_words(
                    validated_sensitive_words
                )

                # 验证去重完整性
                dedup_integrity = self._verify_processing_integrity(
                    "敏感词去重",
                    validated_sensitive_words,
                    deduplicated_sensitive_words,
                    request_id,
                )

                # 4. 验证和清理检查结果
                validated_check_results = self.validate_check_results(check_results)

                # 验证检查结果处理完整性
                cr_integrity = self._verify_processing_integrity(
                    "检查结果验证", check_results, validated_check_results, request_id
                )

                # 5. 排序检查结果
                prioritized_check_results = self.prioritize_check_results(
                    validated_check_results
                )

                # 6. 限制结果数量
                limited_check_results = self.limit_results(prioritized_check_results)

                # 验证限制结果完整性
                limit_integrity = self._verify_processing_integrity(
                    "结果数量限制",
                    prioritized_check_results,
                    limited_check_results,
                    request_id,
                )

                # 7. 创建最终响应
                response = ComplianceCheckResponse(
                    sensitiveWordsArr=deduplicated_sensitive_words,
                    checkResultArr=limited_check_results,
                )

                # 8. 最终完整性验证
                final_integrity = self._verify_processing_integrity(
                    "最终结果聚合",
                    {
                        "sensitive_words": sensitive_words,
                        "check_results": check_results,
                    },
                    {
                        "sensitive_words": response.data.sensitiveWordsArr,
                        "check_results": response.data.checkResultArr,
                    },
                    request_id,
                )

                # 记录完整性验证摘要
                integrity_summary = {
                    "敏感词验证": sw_integrity["integrity_status"],
                    "敏感词去重": dedup_integrity["integrity_status"],
                    "检查结果验证": cr_integrity["integrity_status"],
                    "结果数量限制": limit_integrity["integrity_status"],
                    "最终聚合": final_integrity["integrity_status"],
                }

                log.info(
                    f"结果聚合完整性验证完成 | ID: {request_id} | "
                    f"摘要: {integrity_summary} | "
                    f"最终敏感词: {len(response.data.sensitiveWordsArr)}个 | "
                    f"最终检查结果: {len(response.data.checkResultArr)}个"
                )

                return response

        except ResultProcessingError:
            # 重新抛出结果处理异常
            raise
        except Exception as e:
            raise ResultProcessingError(
                f"带完整性检查的结果聚合失败: {str(e)}",
                stage="完整性检查聚合",
                original_error=e,
            )

    def get_processing_stats(self) -> Dict[str, Any]:
        """
        获取处理统计信息

        Returns:
            Dict[str, Any]: 处理统计信息
        """
        return {
            "max_check_results": 15,
            "supported_question_types": [e.value for e in QuestionType],
            "question_type_priorities": {
                QuestionType.COMPLIANCE.value: 1,
                QuestionType.RISK_MANAGEMENT.value: 2,
                QuestionType.LOGIC.value: 3,
                QuestionType.FAIRNESS.value: 4,
                QuestionType.STANDARDIZATION.value: 5,
                QuestionType.OPERABILITY.value: 6,
            },
            "default_response_format": {"sensitiveWordsArr": [], "checkResultArr": []},
            "integrity_verification": True,
            "data_loss_thresholds": {
                "warning_threshold": 10,  # 10%
                "error_threshold": 50,  # 50%
            },
        }


# 创建全局结果处理器实例
result_processor = ResultProcessor()
