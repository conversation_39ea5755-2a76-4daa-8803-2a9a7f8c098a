# 招标文件合规性检查助手

[![Python Version](https://img.shields.io/badge/python-3.11+-blue.svg)](https://python.org)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.104+-green.svg)](https://fastapi.tiangolo.com)
[![License](https://img.shields.io/badge/license-MIT-blue.svg)](LICENSE)

基于AI大模型的招标文件合规性检查API服务，支持自动检查招标文件的合规性、逻辑性、风险管理、规范性、公平性、可操作性等问题，并集成敏感词检测功能。

## ✨ 主要特性

- 🤖 **AI智能分析**: 基于大语言模型进行深度文档分析
- 📄 **多格式支持**: 支持 docx 和 pdf 格式文件
- 🔍 **敏感词检测**: 集成专业敏感词检测服务
- ⚡ **高性能处理**: 支持异步处理和并发控制
- 🛡️ **安全可靠**: 完善的错误处理和降级机制
- 📊 **监控统计**: 详细的性能监控和处理统计
- 🔧 **易于部署**: 支持 Docker 容器化部署

## 🚀 快速开始

### 环境要求

- Python 3.11+
- 4GB+ RAM
- 稳定的网络连接

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd jianchazhushou-plus
```

2. **创建虚拟环境**
```bash
python -m venv venv
source venv/bin/activate  # Linux/macOS
# 或
venv\Scripts\activate     # Windows
```

3. **安装依赖**
```bash
pip install -r requirements.txt
```

4. **配置环境变量**
```bash
cp .env.example .env
# 编辑 .env 文件，配置必要参数
```

5. **启动服务**
```bash
python main.py
```

服务将在 `http://localhost:8088` 启动。

### Docker 部署

```bash
# 构建镜像
docker build -t compliance-checker .

# 运行容器
docker run -d \
  --name compliance-checker \
  -p 8088:8088 \
  --env-file .env \
  compliance-checker
```

或使用 docker-compose：

```bash
docker-compose up -d
```

## 📖 使用说明

### API 接口

#### 健康检查
```bash
curl http://localhost:8088/health
```

#### 合规性检查
```bash
curl -X POST http://localhost:8088/api/v1/check-compliance \
  -H "Content-Type: application/json" \
  -d '{
    "procurement_project_type": "货物类",
    "project_category": "政府采购",
    "bidding_procurement_method": "公开招标",
    "bidding_doc": {
      "filename": "招标文件.docx",
      "extension": ".docx",
      "mime_type": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      "size": 1048576,
      "url": "https://example.com/files/招标文件.docx"
    }
  }'
```

### 响应格式

```json
{
  "sensitiveWordsArr": [
    {
      "type": "商业敏感",
      "content": "独家供应",
      "num": 1
    }
  ],
  "checkResultArr": [
    {
      "quesType": "合规性",
      "quesDesc": "技术参数设置过于具体，可能限制竞争",
      "originalArr": ["设备必须为某某品牌型号ABC-123"],
      "point": "技术参数应具有通用性，不应指定特定品牌",
      "advice": "建议修改为功能性技术要求，删除品牌限制条款"
    }
  ]
}
```

## 🏗️ 项目结构

```
jianchazhushou-plus/
├── app/                          # 应用主目录
│   ├── api/                      # API路由
│   ├── core/                     # 核心功能
│   │   ├── cache_manager.py      # 缓存管理
│   │   ├── config.py             # 配置管理
│   │   ├── exceptions.py         # 异常处理
│   │   ├── logger.py             # 日志系统
│   │   └── queue_manager.py      # 队列管理
│   ├── middleware/               # 中间件
│   ├── models/                   # 数据模型
│   ├── services/                 # 业务服务
│   │   ├── ai_model_service.py   # AI模型服务
│   │   ├── compliance_service.py # 合规检查服务
│   │   ├── file_processor_v2.py  # 文件处理服务
│   │   └── sensitive_word_service.py # 敏感词服务
│   └── utils/                    # 工具函数
├── docs/                         # 文档
│   ├── API_DOCUMENTATION.md      # API文档
│   └── DEPLOYMENT_GUIDE.md       # 部署指南
├── scripts/                      # 脚本
│   └── run_tests.py              # 测试脚本
├── tests/                        # 测试文件
├── main.py                       # 应用入口
├── requirements.txt              # 依赖列表
└── README.md                     # 项目说明
```

## 🔧 配置说明

主要配置项（.env 文件）：

```env
# AI模型配置
MODEL_APIKEY=your-api-key-here
MODEL_NAME=deepseek-ai/DeepSeek-V3
MODEL_URL=http://localhost:3002/v1

# 敏感词检测API
SENSITIVE_WORD_API_URL=http://*************:8087

# 文件处理
MAX_FILE_SIZE=314572800  # 300MB
ALLOWED_EXTENSIONS=[".docx", ".pdf"]

# 性能优化
CACHE_MAX_SIZE_MB=100
MAX_CONCURRENT_REQUESTS=10
ENABLE_ASYNC_PROCESSING=true

# 限流设置
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW_SECONDS=3600
```

详细配置说明请参考 [部署指南](docs/DEPLOYMENT_GUIDE.md)。

## 🧪 测试

### 运行测试

```bash
# 运行所有测试
python scripts/run_tests.py

# 只运行单元测试
python scripts/run_tests.py --unit-only

# 运行测试并显示详细输出
python scripts/run_tests.py --verbose

# 跳过耗时的测试
python scripts/run_tests.py --skip-slow
```

### 测试覆盖率

```bash
# 生成覆盖率报告
python scripts/run_tests.py --coverage-only
```

目标测试覆盖率：≥85%

## 📊 性能指标

### 基准性能

- **文件处理速度**: 1-5MB/秒（取决于文件复杂度）
- **并发处理能力**: 支持10个并发请求
- **响应时间**: 通常30-60秒（包含AI分析时间）
- **内存使用**: 基础2GB，峰值4GB
- **缓存命中率**: 通常30-50%

### 性能优化特性

- ✅ 内存缓存系统
- ✅ 异步请求队列
- ✅ 智能资源管理
- ✅ 请求限流控制
- ✅ 自动垃圾回收

## 🛡️ 安全特性

- **输入验证**: 严格的参数验证和文件格式检查
- **错误处理**: 完善的异常处理，不暴露内部信息
- **限流保护**: 防止API滥用和DDoS攻击
- **安全头**: 自动添加安全相关的HTTP头
- **依赖扫描**: 定期检查依赖包安全漏洞

## 📈 监控和运维

### 健康检查

```bash
curl http://localhost:8088/health
```

### 服务状态

```bash
curl http://localhost:8088/api/v1/service-status
```

### 处理指标

```bash
curl http://localhost:8088/api/v1/processing-metrics
```

### 日志文件

- `logs/compliance_checker.log`: 主应用日志
- `logs/access.log`: 访问日志
- `logs/error.log`: 错误日志

## 🔄 API 限制

- **请求频率**: 100次/小时（可配置）
- **文件大小**: 最大300MB
- **超时时间**: 300秒
- **支持格式**: .docx, .pdf

## 🐛 故障排除

### 常见问题

1. **服务启动失败**
   - 检查配置文件是否正确
   - 确认端口8088未被占用
   - 验证Python版本≥3.11

2. **AI模型调用失败**
   - 检查MODEL_APIKEY是否正确
   - 确认MODEL_URL可访问
   - 验证网络连接

3. **文件处理失败**
   - 确认文件URL可访问
   - 检查文件格式是否支持
   - 验证文件大小未超限

4. **内存使用过高**
   - 调整缓存大小配置
   - 启用自动垃圾回收
   - 考虑增加系统内存

详细故障排除指南请参考 [部署指南](docs/DEPLOYMENT_GUIDE.md#故障排除)。

## 📚 文档

- [API 文档](docs/API_DOCUMENTATION.md) - 完整的API接口说明
- [部署指南](docs/DEPLOYMENT_GUIDE.md) - 详细的部署和运维指南

## 🤝 贡献

欢迎提交Issue和Pull Request！

### 开发环境设置

```bash
# 安装开发依赖
pip install -r requirements-dev.txt

# 运行代码格式化
black app/

# 运行代码检查
flake8 app/

# 运行安全扫描
bandit -r app/
```

### 提交规范

- 使用清晰的提交信息
- 确保所有测试通过
- 遵循代码风格规范
- 更新相关文档

## 📄 许可证

本项目采用 MIT 许可证 - 详见 [LICENSE](LICENSE) 文件。

## 🙏 致谢

- [FastAPI](https://fastapi.tiangolo.com/) - 现代化的Python Web框架
- [OpenAI](https://openai.com/) - AI模型支持
- [Pydantic](https://pydantic-docs.helpmanual.io/) - 数据验证
- [Uvicorn](https://www.uvicorn.org/) - ASGI服务器

## 📞 支持

如果您在使用过程中遇到问题，请：

1. 查看 [FAQ](docs/API_DOCUMENTATION.md#常见问题)
2. 搜索已有的 [Issues](https://github.com/example/issues)
3. 创建新的 Issue 描述问题
4. 联系技术支持：<EMAIL>

---

**招标文件合规性检查助手** - 让招标文件审查更智能、更高效！