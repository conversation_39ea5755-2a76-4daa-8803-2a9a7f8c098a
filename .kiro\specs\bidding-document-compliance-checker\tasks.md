# Implementation Plan

- [x] 1. 设置项目结构和核心配置

  - 创建FastAPI项目目录结构
  - 实现环境配置管理器，支持.env文件加载
  - 创建基础的数据模型和类型定义，包含枚举类型
  - 实现参数枚举验证器，支持采购项目类型、项目类别、招标采购方式的严格验证
  - 编写参数验证的单元测试
  - _Requirements: 5.1, 6.1, 6.3, 7.1, 7.2, 7.3, 7.4_

- [x] 2. 实现日志系统

  - 基于参考代码log_cfg.py创建日志配置模块，使用loguru库
  - 实现结构化日志记录功能，支持请求追踪和错误处理装饰器
  - 配置日志文件按日期轮转和保留策略
  - 添加性能监控和错误日志记录
  - 编写日志系统的单元测试
  - _Requirements: 5.2, 5.3_

- [x] 3. 实现文件处理核心功能

  - 参考analyse_appendix.py创建文件下载和处理模块，支持健壮的HTTP会话
  - 创建文件格式验证器，限制docx和pdf格式，使用filetype库检测
  - 实现docx文件内容提取，使用python-docx库和markdownify转换
  - 实现pdf文件内容提取，使用pdfplumber库和markdown转换
  - 集成MarkItDown库（如可用）作为备用转换方案
  - 添加文件大小限制和安全检查
  - 编写文件处理模块的单元测试
  - _Requirements: 1.1, 1.2, 1.3, 1.4_

- [x] 4. 实现AI模型服务集成

  - 参考analyse_appendix.py的LLM调用模式创建OpenAI客户端封装
  - 实现动态系统提示词构建功能，根据项目类型调整提示词
  - 实现模型调用和响应处理逻辑，支持重试机制和超时处理
  - 添加上下文长度管理和token限制处理，参考MAX_CONTEXT_LENGTH配置
  - 实现JSON格式验证和clean_json_data清理功能
  - 编写AI模型服务的单元测试和集成测试
  - _Requirements: 2.1, 2.2, 2.3, 2.4, 7.3, 7.4_

- [x] 5. 实现敏感词检测服务集成

  - 创建敏感词检测API客户端，调用<http://172.27.127.43:8087/detect接口>
  - 实现POST请求封装，发送markdown格式的文档内容
  - 添加is_government_procurement参数支持，根据项目类别自动判断
  - 实现API响应解析，提取results数组中的敏感词信息
  - 实现结果格式转换：从API的{序号,敏感词类型,敏感词内容,出现次数}转换为{type,content,num}格式
  - 添加健康检查接口调用(/health)和统计信息获取(/stats)
  - 实现重试机制和超时处理，参考analyse_appendix.py的HTTP模式
  - 编写敏感词服务的单元测试和集成测试，包含API响应mock
  - _Requirements: 3.1, 3.2, 3.3, 3.4_

- [x] 6. 实现结果处理和格式化

  - 创建结果聚合器，整合AI模型和敏感词检测结果
  - 实现输出格式验证和标准化
  - 添加空数据处理和默认值设置
  - 实现结果字段校验和数据清洗
  - 编写结果处理模块的单元测试
  - _Requirements: 4.1, 4.2, 4.3, 4.4_

- [x] 7. 实现异常处理和错误管理

  - 参考log_cfg.py的error_handler装饰器模式创建异常处理器
  - 创建分层异常处理器，支持不同类型错误
  - 实现友好的错误响应格式，避免暴露系统内部信息
  - 添加异常日志记录和上下文信息，使用traceback记录完整错误
  - 实现降级机制和容错处理
  - 编写异常处理的单元测试
  - _Requirements: 5.3, 5.4_

- [x] 8. 实现FastAPI路由和中间件

  - 创建主要的API路由处理器
  - 实现请求参数验证中间件，集成枚举值验证
  - 添加CORS支持和安全头设置
  - 实现请求日志记录中间件
  - 创建健康检查接口
  - 编写API路由的集成测试，包含参数验证测试用例
  - _Requirements: 6.4, 7.1, 7.2, 7.3, 7.4, 8.1, 8.2_

- [x] 9. 集成所有组件并实现主业务流程

  - 创建主要的业务逻辑控制器
  - 集成文件处理、AI模型调用、敏感词检测流程
  - 实现完整的请求处理管道
  - 添加性能监控和指标收集
  - 编写端到端集成测试
  - _Requirements: 1.1-1.4, 2.1-2.4, 3.1-3.4, 4.1-4.4_

- [x] 10. 实现性能优化和并发处理

  - 添加异步处理支持，提升并发性能
  - 实现请求队列和限流机制
  - 优化内存使用和资源管理

  - 添加缓存机制（如适用）

  - 编写性能测试和负载测试

  - _Requirements: 7.1, 7.2_

- [x] 11. 完善测试覆盖和文档

  - 补充单元测试，确保85%以上覆盖率
  - 创建API文档和使用示例
  - 添加测试数据和mock服务
  - 实现自动化测试脚本
  - 编写部署和运维文档
  - _Requirements: 6.2, 6.4_

- [x] 12. 部署准备和生产环境配置

  - 创建Docker配置文件
  - 实现环境变量验证和默认值设置
  - 添加生产环境日志配置
  - 实现监控和告警机制
  - 创建部署脚本和启动配置
  - _Requirements: 5.1, 6.1, 6.3_

## 敏感词API集成详细说明

### API接口信息

- **基础URL**: <http://172.27.127.43:8087>
- **检测接口**: POST /detect
- **健康检查**: GET /health  
- **统计信息**: GET /stats
- **重载敏感词**: POST /reload
- **API文档**: <http://172.27.127.43:8087/docs>

### 请求格式

```json
{
  "content": "# 招标文件\n\n这是要检测的markdown内容...",
  "is_government_procurement": true  // 可选，是否政府采购类
}
```

### 响应格式

```json
{
  "success": true,
  "message": "检测完成", 
  "total_words": 2,
  "markdown_table": "| 序号 | 敏感词类型 | 敏感词内容 | 出现次数 |\n...",
  "results": [
    {
      "序号": 1,
      "敏感词类型": "政治敏感",
      "敏感词内容": "示例词",
      "出现次数": 1
    }
  ]
}
```

### 格式转换规则

API响应的results数组需要转换为目标格式：

- `敏感词类型` → `type`
- `敏感词内容` → `content`
- `出现次数` → `num`
- 忽略`序号`字段

### 项目类别判断

根据输入参数`project_category`自动判断`is_government_procurement`：

- "政府采购" → true
- "依法招标"/"非依法招标" → false

## 其他需要注意的地方

基于参考代码分析，还需要考虑以下几个重要方面：

### 技术实现细节

1. **文件处理优化**：参考analyse_appendix.py的文件处理模式，支持多种文档格式和备用解析方案
2. **HTTP客户端健壮性**：使用create_robust_session()模式，配置重试策略和连接池
3. **内容长度管理**：实现内容截断机制，避免超长文档导致API调用失败
4. **JSON数据清理**：实现clean_json_data功能，确保LLM返回的JSON格式正确

### 性能和稳定性

1. **异步处理**：考虑使用异步IO提升并发处理能力
2. **内存管理**：大文件处理时的内存优化和垃圾回收
3. **缓存机制**：对重复文档的处理结果进行缓存
4. **监控指标**：添加处理时间、成功率、错误率等关键指标

### 安全性考虑

1. **文件安全检查**：防止恶意文件上传和处理
2. **API密钥保护**：确保环境变量中的敏感信息安全
3. **输入验证**：严格验证所有输入参数
4. **输出过滤**：防止敏感信息泄露到日志或响应中

### 运维和部署

1. **健康检查**：实现详细的健康检查接口
2. **配置热更新**：支持不重启服务更新配置
3. **日志轮转**：参考log_cfg.py的日志管理策略
4. **容器化部署**：Docker配置和资源限制

### 测试策略

1. **单元测试**：每个模块的独立测试
2. **集成测试**：端到端流程测试
3. **性能测试**：大文件和高并发场景测试
4. **安全测试**：恶意输入和边界条件测试
