#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
逐步调试合规性检查流程
"""

import requests
import json
import time


def test_ai_model_service():
    """测试AI模型服务连接"""
    print("1. 测试AI模型服务")
    print("-" * 40)

    # 直接测试AI模型服务
    ai_url = "http://localhost:3002/v1/chat/completions"
    api_key = "sk-3WPbrV2cLkTChby7NaNVnGFAKEYw4yqudN1Mvlkd21eBpFAC"

    headers = {"Authorization": f"Bearer {api_key}", "Content-Type": "application/json"}

    data = {
        "model": "gemini-2.5-flash",
        "messages": [{"role": "user", "content": "Hello, this is a test."}],
        "max_tokens": 50,
    }

    try:
        print("测试AI模型服务连接...")
        start_time = time.time()

        response = requests.post(ai_url, headers=headers, json=data, timeout=30)

        elapsed = time.time() - start_time
        print(f"AI模型响应时间: {elapsed:.2f}秒")
        print(f"状态码: {response.status_code}")

        if response.status_code == 200:
            print("✅ AI模型服务正常")
            return True
        else:
            print(f"❌ AI模型服务异常: {response.text}")
            return False

    except requests.exceptions.Timeout:
        print("❌ AI模型服务超时")
        return False
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到AI模型服务")
        return False
    except Exception as e:
        print(f"❌ AI模型服务测试失败: {str(e)}")
        return False


def test_sensitive_word_api():
    """测试敏感词API"""
    print("\n2. 测试敏感词API")
    print("-" * 40)

    api_url = "http://*************:8087/detect"

    data = {
        "content": "这是一个测试文档，包含一些基本内容。",
        "is_government_procurement": True,
    }

    try:
        print("测试敏感词API连接...")
        start_time = time.time()

        response = requests.post(api_url, json=data, timeout=30)

        elapsed = time.time() - start_time
        print(f"敏感词API响应时间: {elapsed:.2f}秒")
        print(f"状态码: {response.status_code}")

        if response.status_code == 200:
            result = response.json()
            print(f"✅ 敏感词API正常，检测到 {result.get('total_words', 0)} 个敏感词")
            return True
        else:
            print(f"❌ 敏感词API异常: {response.text}")
            return False

    except requests.exceptions.Timeout:
        print("❌ 敏感词API超时")
        return False
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到敏感词API")
        return False
    except Exception as e:
        print(f"❌ 敏感词API测试失败: {str(e)}")
        return False


def test_file_download():
    """测试文件下载"""
    print("\n3. 测试文件下载")
    print("-" * 40)

    # 使用一个简单的测试URL
    test_url = "http://httpbin.org/json"

    try:
        print("测试文件下载...")
        start_time = time.time()

        response = requests.get(test_url, timeout=10)

        elapsed = time.time() - start_time
        print(f"文件下载时间: {elapsed:.2f}秒")
        print(f"状态码: {response.status_code}")
        print(f"内容长度: {len(response.content)} 字节")

        if response.status_code == 200:
            print("✅ 文件下载正常")
            return True
        else:
            print(f"❌ 文件下载异常")
            return False

    except Exception as e:
        print(f"❌ 文件下载测试失败: {str(e)}")
        return False


def test_minimal_compliance_request():
    """测试最小化的合规性检查请求"""
    print("\n4. 测试最小化合规性检查")
    print("-" * 40)

    base_url = "http://localhost:8088"

    # 最简单的请求
    minimal_request = {
        "procurement_project_type": "服务类",
        "project_category": "政府采购",
        "bidding_procurement_method": "公开招标",
        "bidding_doc": {
            "filename": "test.docx",
            "extension": ".docx",
            "mime_type": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            "size": 100,  # 很小的文件
            "url": "http://httpbin.org/status/404",  # 会快速失败
        },
    }

    try:
        print("发送最小化合规性检查请求...")
        start_time = time.time()

        response = requests.post(
            f"{base_url}/api/v1/check-compliance",
            json=minimal_request,
            timeout=30,  # 30秒超时
        )

        elapsed = time.time() - start_time
        print(f"处理时间: {elapsed:.2f}秒")
        print(f"状态码: {response.status_code}")

        # 显示响应内容（截断）
        response_text = response.text
        if len(response_text) > 500:
            response_text = response_text[:500] + "..."
        print(f"响应内容: {response_text}")

        if response.status_code in [200, 400]:  # 400也是可以接受的，说明请求被处理了
            print("✅ 合规性检查请求被处理（即使失败也说明流程正常）")
            return True
        else:
            print(f"❌ 合规性检查请求异常")
            return False

    except requests.exceptions.Timeout:
        print("❌ 合规性检查请求超时")
        return False
    except Exception as e:
        print(f"❌ 合规性检查请求失败: {str(e)}")
        return False


def create_debug_endpoint():
    """创建一个调试端点来测试内部组件"""
    print("\n5. 建议添加调试端点")
    print("-" * 40)

    debug_code = '''
# 在 app/api/routes.py 中添加这个调试端点：

@router.get("/debug/components")
async def debug_components():
    """调试组件状态"""
    try:
        # 测试各个组件
        results = {}
        
        # 测试AI模型服务
        try:
            ai_info = ai_model_service.get_model_info()
            results["ai_model"] = {
                "status": "ok" if ai_info.get("client_initialized") else "error",
                "info": ai_info
            }
        except Exception as e:
            results["ai_model"] = {"status": "error", "error": str(e)}
        
        # 测试敏感词服务
        try:
            health = sensitive_word_service.check_health("debug")
            results["sensitive_word"] = {
                "status": "ok" if health else "error",
                "health": health
            }
        except Exception as e:
            results["sensitive_word"] = {"status": "error", "error": str(e)}
        
        # 测试文件处理器
        try:
            stats = file_processor.get_processing_stats()
            results["file_processor"] = {"status": "ok", "stats": stats}
        except Exception as e:
            results["file_processor"] = {"status": "error", "error": str(e)}
        
        return {"debug_results": results}
        
    except Exception as e:
        return {"error": str(e)}
'''

    print("建议在API中添加调试端点：")
    print(debug_code)

    return True


def main():
    """主函数"""
    print("逐步调试合规性检查流程")
    print("=" * 60)

    tests = [
        ("AI模型服务", test_ai_model_service),
        ("敏感词API", test_sensitive_word_api),
        ("文件下载", test_file_download),
        ("最小化合规性检查", test_minimal_compliance_request),
        ("调试端点建议", create_debug_endpoint),
    ]

    results = {}

    for test_name, test_func in tests:
        try:
            result = test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {str(e)}")
            results[test_name] = False

    # 分析结果
    print(f"\n{'='*60}")
    print("问题分析")
    print("=" * 60)

    if not results.get("AI模型服务", False):
        print("🔍 主要问题：AI模型服务不可用")
        print("   解决方案：")
        print("   1. 检查 http://localhost:3002 是否运行")
        print("   2. 验证API密钥是否正确")
        print("   3. 检查模型名称配置")

    if not results.get("敏感词API", False):
        print("🔍 次要问题：敏感词API不可用")
        print("   解决方案：")
        print("   1. 检查网络连接到 *************:8087")
        print("   2. 确认敏感词服务正在运行")

    if results.get("最小化合规性检查", False):
        print("✅ 合规性检查流程基本正常")
    else:
        print("❌ 合规性检查流程存在问题")

    print(f"\n{'='*60}")
    print("建议的解决步骤")
    print("=" * 60)
    print("1. 首先解决AI模型服务连接问题")
    print("2. 确保敏感词API服务可用")
    print("3. 在API中添加调试端点来实时监控组件状态")
    print("4. 使用更小的测试文件进行验证")


if __name__ == "__main__":
    main()
