2025-08-07 09:43:22 | INFO     | app.services.ai_model_service:_initialize_client:74 | AI模型客户端初始化成功: gemini-2.5-flash
2025-08-07 09:43:22 | INFO     | app.services.ai_model_service:_initialize_client:74 | AI模型客户端初始化成功: gemini-2.5-flash
2025-08-07 09:43:22 | WARNING  | app.services.ai_model_service:clean_json_data:267 | AI模型返回空响应，返回默认空结果JSON
2025-08-07 09:43:22 | WARNING  | app.services.ai_model_service:clean_json_data:267 | AI模型返回空响应，返回默认空结果JSON
2025-08-07 09:43:22 | WARNING  | app.services.ai_model_service:clean_json_data:292 | 响应中未找到有效的JSON对象，返回默认空结果JSON
2025-08-07 09:45:04 | INFO     | app.services.ai_model_service:_initialize_client:74 | AI模型客户端初始化成功: gemini-2.5-flash
2025-08-07 09:45:05 | INFO     | app.services.ai_model_service:_initialize_client:74 | AI模型客户端初始化成功: gemini-2.5-flash
2025-08-07 09:45:05 | WARNING  | app.services.ai_model_service:clean_json_data:267 | AI模型返回空响应，返回默认空结果JSON
2025-08-07 09:45:05 | WARNING  | app.services.ai_model_service:clean_json_data:267 | AI模型返回空响应，返回默认空结果JSON
2025-08-07 09:45:05 | WARNING  | app.services.ai_model_service:clean_json_data:292 | 响应中未找到有效的JSON对象，返回默认空结果JSON
2025-08-07 09:47:21 | INFO     | app.services.sensitive_word_service:_convert_to_target_format:351 | 敏感词转换完成: 原始2个，有效2个
2025-08-07 09:47:21 | INFO     | app.services.sensitive_word_service:_convert_to_target_format:351 | 敏感词转换完成: 原始2个，有效2个
2025-08-07 09:47:21 | INFO     | app.services.sensitive_word_service:_convert_to_target_format:351 | 敏感词转换完成: 原始2个，有效2个
2025-08-07 09:47:21 | INFO     | app.services.sensitive_word_service:_convert_to_target_format:307 | 敏感词检测结果为空
2025-08-07 09:47:21 | WARNING  | app.services.sensitive_word_service:_convert_to_target_format:344 | 跳过无效的敏感词结果 1: 内容为空
2025-08-07 09:47:21 | WARNING  | app.services.sensitive_word_service:_convert_to_target_format:344 | 跳过无效的敏感词结果 3: 内容为空
2025-08-07 09:47:21 | WARNING  | app.services.sensitive_word_service:_convert_to_target_format:347 | 转换敏感词结果 4 失败: int() argument must be a string, a bytes-like object or a real number, not 'builtin_function_or_method'
2025-08-07 09:47:21 | INFO     | app.services.sensitive_word_service:_convert_to_target_format:351 | 敏感词转换完成: 原始4个，有效1个
2025-08-07 09:48:18 | INFO     | app.services.sensitive_word_service:_convert_to_target_format:351 | 敏感词转换完成: 原始2个，有效2个
2025-08-07 09:48:18 | INFO     | app.services.sensitive_word_service:_convert_to_target_format:351 | 敏感词转换完成: 原始2个，有效2个
2025-08-07 09:48:18 | INFO     | app.services.sensitive_word_service:_convert_to_target_format:351 | 敏感词转换完成: 原始2个，有效2个
2025-08-07 09:48:18 | INFO     | app.services.sensitive_word_service:_convert_to_target_format:307 | 敏感词检测结果为空
2025-08-07 09:48:18 | WARNING  | app.services.sensitive_word_service:_convert_to_target_format:344 | 跳过无效的敏感词结果 1: 内容为空
2025-08-07 09:48:18 | WARNING  | app.services.sensitive_word_service:_convert_to_target_format:344 | 跳过无效的敏感词结果 3: 内容为空
2025-08-07 09:48:18 | WARNING  | app.services.sensitive_word_service:_convert_to_target_format:347 | 转换敏感词结果 4 失败: int() argument must be a string, a bytes-like object or a real number, not 'builtin_function_or_method'
2025-08-07 09:48:18 | INFO     | app.services.sensitive_word_service:_convert_to_target_format:351 | 敏感词转换完成: 原始4个，有效1个
2025-08-07 09:50:30 | INFO     | app.services.ai_model_service:_initialize_client:74 | AI模型客户端初始化成功: gemini-2.5-flash
2025-08-07 09:50:30 | INFO     | app.services.ai_model_service:_initialize_client:74 | AI模型客户端初始化成功: gemini-2.5-flash
2025-08-07 09:50:30 | WARNING  | app.services.ai_model_service:clean_json_data:267 | AI模型返回空响应，返回默认空结果JSON
2025-08-07 09:50:30 | WARNING  | app.services.ai_model_service:clean_json_data:267 | AI模型返回空响应，返回默认空结果JSON
2025-08-07 09:50:30 | WARNING  | app.services.ai_model_service:clean_json_data:292 | 响应中未找到有效的JSON对象，返回默认空结果JSON
2025-08-07 09:51:26 | INFO     | app.services.ai_model_service:_initialize_client:74 | AI模型客户端初始化成功: gemini-2.5-flash
2025-08-07 09:51:27 | INFO     | app.services.ai_model_service:_initialize_client:74 | AI模型客户端初始化成功: gemini-2.5-flash
2025-08-07 09:52:27 | INFO     | app.services.ai_model_service:_initialize_client:74 | AI模型客户端初始化成功: gemini-2.5-flash
2025-08-07 09:52:28 | INFO     | app.services.ai_model_service:_initialize_client:74 | AI模型客户端初始化成功: gemini-2.5-flash
2025-08-07 09:53:13 | INFO     | app.services.ai_model_service:_initialize_client:74 | AI模型客户端初始化成功: gemini-2.5-flash
2025-08-07 09:53:14 | INFO     | app.services.ai_model_service:_initialize_client:74 | AI模型客户端初始化成功: gemini-2.5-flash
2025-08-07 09:53:41 | INFO     | app.services.ai_model_service:_initialize_client:74 | AI模型客户端初始化成功: gemini-2.5-flash
2025-08-07 09:53:41 | INFO     | app.services.ai_model_service:_initialize_client:74 | AI模型客户端初始化成功: gemini-2.5-flash
2025-08-07 09:54:28 | INFO     | app.services.ai_model_service:_initialize_client:74 | AI模型客户端初始化成功: gemini-2.5-flash
2025-08-07 09:54:29 | INFO     | app.services.ai_model_service:_initialize_client:74 | AI模型客户端初始化成功: gemini-2.5-flash
2025-08-07 09:54:34 | INFO     | main:lifespan:26 | 招标文件合规性检查助手关闭
2025-08-07 09:54:39 | INFO     | app.services.ai_model_service:_initialize_client:74 | AI模型客户端初始化成功: gemini-2.5-flash
2025-08-07 09:54:39 | INFO     | main:lifespan:22 | 招标文件合规性检查助手启动中...
2025-08-07 09:54:39 | INFO     | main:lifespan:23 | 配置信息: 模型=gemini-2.5-flash, 环境=development
2025-08-07 09:54:46 | INFO     | app.services.ai_model_service:_initialize_client:74 | AI模型客户端初始化成功: gemini-2.5-flash
2025-08-07 09:54:47 | INFO     | app.services.ai_model_service:_initialize_client:74 | AI模型客户端初始化成功: gemini-2.5-flash
2025-08-07 09:55:31 | INFO     | main:lifespan:26 | 招标文件合规性检查助手关闭
2025-08-07 09:55:37 | INFO     | app.services.ai_model_service:_initialize_client:74 | AI模型客户端初始化成功: gemini-2.5-flash
2025-08-07 09:55:37 | INFO     | main:lifespan:22 | 招标文件合规性检查助手启动中...
2025-08-07 09:55:37 | INFO     | main:lifespan:23 | 配置信息: 模型=gemini-2.5-flash, 环境=development
2025-08-07 09:56:02 | INFO     | app.services.ai_model_service:_initialize_client:74 | AI模型客户端初始化成功: gemini-2.5-flash
2025-08-07 09:56:03 | INFO     | app.services.ai_model_service:_initialize_client:74 | AI模型客户端初始化成功: gemini-2.5-flash
2025-08-07 09:56:04 | INFO     | app.services.ai_model_service:_initialize_client:74 | AI模型客户端初始化成功: gemini-2.5-flash
2025-08-07 09:56:05 | INFO     | app.services.ai_model_service:_initialize_client:74 | AI模型客户端初始化成功: gemini-2.5-flash
2025-08-07 09:56:05 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | AI模型调用(尝试1) | ID: test-request | 详情: {}
2025-08-07 09:56:05 | INFO     | app.services.ai_model_service:_call_model_with_retry:374 | 调用AI模型: gemini-2.5-flash, 消息数: 1, 尝试: 1/4 | 请求ID: test-request
2025-08-07 09:56:05 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | AI模型调用(尝试1) | ID: test-request | 状态: 失败 | 耗时: 0.001秒
2025-08-07 09:56:05 | ERROR    | app.core.logger:__exit__:251 | 操作 AI模型调用(尝试1) 发生异常: 临时错误 (错误码: timeout)
2025-08-07 09:56:05 | WARNING  | app.services.ai_model_service:_call_model_with_retry:429 | AI模型调用失败，将在 0.1秒后重试 | 尝试: 1/4 | 错误: 临时错误 (错误码: timeout) | 请求ID: test-request
2025-08-07 09:56:05 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | AI模型调用(尝试2) | ID: test-request | 详情: {}
2025-08-07 09:56:05 | INFO     | app.services.ai_model_service:_call_model_with_retry:374 | 调用AI模型: gemini-2.5-flash, 消息数: 1, 尝试: 2/4 | 请求ID: test-request
2025-08-07 09:56:05 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | AI模型调用(尝试2) | ID: test-request | 状态: 失败 | 耗时: 0.001秒
2025-08-07 09:56:05 | ERROR    | app.core.logger:__exit__:251 | 操作 AI模型调用(尝试2) 发生异常: 临时错误 (错误码: timeout)
2025-08-07 09:56:05 | WARNING  | app.services.ai_model_service:_call_model_with_retry:429 | AI模型调用失败，将在 0.2秒后重试 | 尝试: 2/4 | 错误: 临时错误 (错误码: timeout) | 请求ID: test-request
2025-08-07 09:56:05 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | AI模型调用(尝试3) | ID: test-request | 详情: {}
2025-08-07 09:56:05 | INFO     | app.services.ai_model_service:_call_model_with_retry:374 | 调用AI模型: gemini-2.5-flash, 消息数: 1, 尝试: 3/4 | 请求ID: test-request
2025-08-07 09:56:05 | INFO     | app.core.logger:log_api_call:170 | API调用 | OpenAI-gemini-2.5-flash | ID: test-request | 状态: 成功 | 耗时: 0.000秒
2025-08-07 09:56:05 | INFO     | app.services.ai_model_service:_call_model_with_retry:413 | AI模型调用成功: 响应长度 4 字符, 尝试次数: 3 | 请求ID: test-request
2025-08-07 09:56:05 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | AI模型调用(尝试3) | ID: test-request | 状态: 成功 | 耗时: 0.002秒
2025-08-07 09:56:06 | INFO     | app.services.ai_model_service:_initialize_client:74 | AI模型客户端初始化成功: gemini-2.5-flash
2025-08-07 09:56:06 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | AI模型调用(尝试1) | ID: test-request | 详情: {}
2025-08-07 09:56:06 | INFO     | app.services.ai_model_service:_call_model_with_retry:374 | 调用AI模型: gemini-2.5-flash, 消息数: 1, 尝试: 1/3 | 请求ID: test-request
2025-08-07 09:56:06 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | AI模型调用(尝试1) | ID: test-request | 状态: 失败 | 耗时: 0.005秒
2025-08-07 09:56:06 | ERROR    | app.core.logger:__exit__:251 | 操作 AI模型调用(尝试1) 发生异常: 持续错误 (错误码: timeout)
2025-08-07 09:56:06 | WARNING  | app.services.ai_model_service:_call_model_with_retry:429 | AI模型调用失败，将在 0.1秒后重试 | 尝试: 1/3 | 错误: 持续错误 (错误码: timeout) | 请求ID: test-request
2025-08-07 09:56:06 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | AI模型调用(尝试2) | ID: test-request | 详情: {}
2025-08-07 09:56:06 | INFO     | app.services.ai_model_service:_call_model_with_retry:374 | 调用AI模型: gemini-2.5-flash, 消息数: 1, 尝试: 2/3 | 请求ID: test-request
2025-08-07 09:56:06 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | AI模型调用(尝试2) | ID: test-request | 状态: 失败 | 耗时: 0.001秒
2025-08-07 09:56:06 | ERROR    | app.core.logger:__exit__:251 | 操作 AI模型调用(尝试2) 发生异常: 持续错误 (错误码: timeout)
2025-08-07 09:56:06 | WARNING  | app.services.ai_model_service:_call_model_with_retry:429 | AI模型调用失败，将在 0.2秒后重试 | 尝试: 2/3 | 错误: 持续错误 (错误码: timeout) | 请求ID: test-request
2025-08-07 09:56:06 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | AI模型调用(尝试3) | ID: test-request | 详情: {}
2025-08-07 09:56:06 | INFO     | app.services.ai_model_service:_call_model_with_retry:374 | 调用AI模型: gemini-2.5-flash, 消息数: 1, 尝试: 3/3 | 请求ID: test-request
2025-08-07 09:56:06 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | AI模型调用(尝试3) | ID: test-request | 状态: 失败 | 耗时: 0.000秒
2025-08-07 09:56:06 | ERROR    | app.core.logger:__exit__:251 | 操作 AI模型调用(尝试3) 发生异常: 持续错误 (错误码: timeout)
2025-08-07 09:56:06 | INFO     | app.core.logger:log_api_call:170 | API调用 | OpenAI-gemini-2.5-flash | ID: test-request | 状态: 失败 | 耗时: 0.000秒
2025-08-07 09:56:06 | ERROR    | app.services.ai_model_service:_call_model_with_retry:469 | AI模型调用最终失败，已重试 2 次 | 最后错误: 持续错误 (错误码: timeout) | 请求ID: test-request
2025-08-07 09:56:06 | INFO     | app.services.ai_model_service:_initialize_client:74 | AI模型客户端初始化成功: gemini-2.5-flash
2025-08-07 09:56:06 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | AI模型调用(尝试1) | ID: test-request | 详情: {}
2025-08-07 09:56:06 | INFO     | app.services.ai_model_service:_call_model_with_retry:374 | 调用AI模型: gemini-2.5-flash, 消息数: 1, 尝试: 1/3 | 请求ID: test-request
2025-08-07 09:56:06 | WARNING  | app.services.ai_model_service:_call_model_with_retry:385 | AI模型返回空响应 | 尝试: 1 | 请求ID: test-request
2025-08-07 09:56:06 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | AI模型调用(尝试1) | ID: test-request | 状态: 失败 | 耗时: 0.003秒
2025-08-07 09:56:06 | ERROR    | app.core.logger:__exit__:251 | 操作 AI模型调用(尝试1) 发生异常: AI模型返回空响应 (模型: gemini-2.5-flash, 错误码: EMPTY_RESPONSE)
2025-08-07 09:56:06 | WARNING  | app.services.ai_model_service:_call_model_with_retry:429 | AI模型调用失败，将在 0.1秒后重试 | 尝试: 1/3 | 错误: AI模型返回空响应 (模型: gemini-2.5-flash, 错误码: EMPTY_RESPONSE) | 请求ID: test-request
2025-08-07 09:56:06 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | AI模型调用(尝试2) | ID: test-request | 详情: {}
2025-08-07 09:56:06 | INFO     | app.services.ai_model_service:_call_model_with_retry:374 | 调用AI模型: gemini-2.5-flash, 消息数: 1, 尝试: 2/3 | 请求ID: test-request
2025-08-07 09:56:06 | INFO     | app.core.logger:log_api_call:170 | API调用 | OpenAI-gemini-2.5-flash | ID: test-request | 状态: 成功 | 耗时: 0.000秒
2025-08-07 09:56:06 | INFO     | app.services.ai_model_service:_call_model_with_retry:413 | AI模型调用成功: 响应长度 4 字符, 尝试次数: 2 | 请求ID: test-request
2025-08-07 09:56:06 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | AI模型调用(尝试2) | ID: test-request | 状态: 成功 | 耗时: 0.002秒
2025-08-07 10:00:46 | INFO     | app.services.ai_model_service:_initialize_client:74 | AI模型客户端初始化成功: gemini-2.5-flash
2025-08-07 10:02:57 | INFO     | app.services.ai_model_service:_initialize_client:74 | AI模型客户端初始化成功: gemini-2.5-flash
2025-08-07 10:02:57 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 完整合规性检查流水线 | ID: test-timeout | 详情: {}
2025-08-07 10:02:57 | INFO     | app.services.compliance_service:execute_pipeline:305 | 开始执行合规性检查流水线 | ID: test-timeout | 文件: test.docx
2025-08-07 10:02:57 | INFO     | app.services.compliance_service:execute_pipeline:315 | 步骤1: 开始验证服务前置条件 | ID: test-timeout
2025-08-07 10:02:57 | INFO     | app.services.compliance_service:execute_pipeline:317 | 步骤1: 服务前置条件验证完成 | ID: test-timeout | 状态: {'file_processor': True, 'ai_model': True, 'sensitive_word': True, 'result_processor': True}
2025-08-07 10:02:57 | INFO     | app.services.compliance_service:execute_pipeline:323 | 步骤2: 开始获取项目信息 | ID: test-timeout
2025-08-07 10:02:57 | INFO     | app.services.compliance_service:execute_pipeline:325 | 步骤2: 项目信息获取完成 | ID: test-timeout
2025-08-07 10:02:57 | INFO     | app.services.compliance_service:execute_pipeline:329 | 步骤3: 开始文件处理阶段 | ID: test-timeout
2025-08-07 10:02:59 | INFO     | app.services.compliance_service:execute_pipeline:334 | 步骤3: 文件处理完成 | ID: test-timeout | 内容长度: 4
2025-08-07 10:02:59 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 完整合规性检查流水线 | ID: test-timeout | 状态: 失败 | 耗时: 2.004秒
2025-08-07 10:02:59 | ERROR    | app.core.logger:__exit__:251 | 操作 完整合规性检查流水线 发生异常: 合规性检查流水线超时: 2.0秒 > 1.0秒 (阶段: AI合规性检查)
2025-08-07 10:02:59 | ERROR    | app.services.compliance_service:execute_pipeline:412 | 合规性检查流水线超时 | ID: test-timeout | 耗时: 2.005秒 | 错误: 合规性检查流水线超时: 2.0秒 > 1.0秒 (阶段: AI合规性检查)
2025-08-07 10:02:59 | INFO     | app.services.result_processor:create_empty_response:371 | 创建空响应: 处理超时（2.0秒）
2025-08-07 10:02:59 | INFO     | app.services.compliance_service:_add_pipeline_metadata:462 | 流水线元数据 | ID: test-timeout | 处理时间: 2.005秒 | 降级使用: True | 敏感词数量: 0 | 检查结果数量: 0
2025-08-07 10:02:59 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 敏感词服务健康检查 | ID: test-timeout | 详情: {}
2025-08-07 10:02:59 | INFO     | app.services.sensitive_word_service:check_health:121 | 检查敏感词服务健康状态: http://*************:8087/health
2025-08-07 10:03:00 | INFO     | app.services.sensitive_word_service:check_health:131 | 敏感词服务健康状态: 健康
2025-08-07 10:03:00 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 敏感词服务健康检查 | ID: test-timeout | 状态: 成功 | 耗时: 0.050秒
2025-08-07 10:03:00 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 敏感词检测 | ID: test-timeout | 详情: {}
2025-08-07 10:03:00 | INFO     | app.services.sensitive_word_service:detect_sensitive_words:213 | 开始敏感词检测: 内容长度=4, 政府采购=True
2025-08-07 10:03:00 | INFO     | app.services.sensitive_word_service:detect_sensitive_words:222 | 敏感词检测超时设置: 1.0秒 | ID: test-timeout
2025-08-07 10:03:00 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 敏感词检测 | ID: test-timeout | 状态: 失败 | 耗时: 0.001秒
2025-08-07 10:03:00 | ERROR    | app.core.logger:__exit__:251 | 操作 敏感词检测 发生异常: 请求超时
2025-08-07 10:03:00 | INFO     | app.core.logger:log_api_call:170 | API调用 | 敏感词检测API | ID: test-timeout | 状态: 失败 | 耗时: 0.000秒
2025-08-07 10:03:00 | ERROR    | app.core.logger:wrapper:282 | 函数调用失败: detect_sensitive_words | 耗时: 0.002秒 | 错误: 敏感词检测API请求失败: 请求超时 (API: http://*************:8087/detect, 原因: 请求超时)
2025-08-07 10:03:00 | ERROR    | app.services.sensitive_word_service:detect_with_fallback:435 | 敏感词检测失败，使用降级策略: 敏感词检测API请求失败: 请求超时 (API: http://*************:8087/detect, 原因: 请求超时)
2025-08-07 10:03:00 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 完整合规性检查流水线 | ID: test-warning | 详情: {}
2025-08-07 10:03:00 | INFO     | app.services.compliance_service:execute_pipeline:305 | 开始执行合规性检查流水线 | ID: test-warning | 文件: test.docx
2025-08-07 10:03:00 | INFO     | app.services.compliance_service:execute_pipeline:315 | 步骤1: 开始验证服务前置条件 | ID: test-warning
2025-08-07 10:03:00 | INFO     | app.services.compliance_service:execute_pipeline:317 | 步骤1: 服务前置条件验证完成 | ID: test-warning | 状态: {'file_processor': True, 'ai_model': False, 'sensitive_word': False, 'result_processor': True}
2025-08-07 10:03:00 | INFO     | app.services.compliance_service:execute_pipeline:323 | 步骤2: 开始获取项目信息 | ID: test-warning
2025-08-07 10:03:00 | INFO     | app.services.compliance_service:execute_pipeline:325 | 步骤2: 项目信息获取完成 | ID: test-warning
2025-08-07 10:03:00 | INFO     | app.services.compliance_service:execute_pipeline:329 | 步骤3: 开始文件处理阶段 | ID: test-warning
2025-08-07 10:03:00 | INFO     | app.services.compliance_service:execute_pipeline:334 | 步骤3: 文件处理完成 | ID: test-warning | 内容长度: 4
2025-08-07 10:03:00 | WARNING  | app.services.compliance_service:check_timeout:300 | 合规性检查流水线接近超时: 0.9秒 / 1.0秒 (阶段: AI合规性检查) | ID: test-warning
2025-08-07 10:03:00 | INFO     | app.services.compliance_service:execute_pipeline:345 | 步骤4: 开始AI合规性检查阶段 | ID: test-warning
2025-08-07 10:03:00 | WARNING  | app.services.compliance_service:execute_pipeline:365 | AI模型服务不可用，跳过AI检查 | ID: test-warning
2025-08-07 10:03:00 | WARNING  | app.services.compliance_service:check_timeout:300 | 合规性检查流水线接近超时: 0.9秒 / 1.0秒 (阶段: 敏感词检测) | ID: test-warning
2025-08-07 10:03:00 | WARNING  | app.services.compliance_service:execute_pipeline:387 | 敏感词服务不可用，跳过敏感词检测 | ID: test-warning
2025-08-07 10:03:00 | WARNING  | app.services.compliance_service:check_timeout:300 | 合规性检查流水线接近超时: 0.9秒 / 1.0秒 (阶段: 结果聚合) | ID: test-warning
2025-08-07 10:03:00 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 结果聚合阶段 | ID: test-warning | 详情: {}
2025-08-07 10:03:00 | INFO     | app.services.compliance_service:result_aggregation_stage:247 | 开始结果聚合 | ID: test-warning
2025-08-07 10:03:00 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 结果聚合 | ID: test-warning | 详情: {}
2025-08-07 10:03:00 | INFO     | app.services.result_processor:aggregate_results:314 | 开始结果聚合: 敏感词0个，检查结果0个
2025-08-07 10:03:00 | INFO     | app.services.result_processor:validate_sensitive_words:107 | 敏感词验证完成: 原始0个，有效0个
2025-08-07 10:03:00 | INFO     | app.services.result_processor:deduplicate_sensitive_words:225 | 敏感词去重完成: 原始0个，去重后0个
2025-08-07 10:03:00 | INFO     | app.services.result_processor:validate_check_results:179 | 检查结果验证完成: 原始0个，有效0个
2025-08-07 10:03:00 | INFO     | app.services.result_processor:prioritize_check_results:263 | 检查结果排序完成: 0个结果
2025-08-07 10:03:00 | INFO     | app.services.result_processor:aggregate_results:345 | 结果聚合完成: 敏感词0个，检查结果0个
2025-08-07 10:03:00 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 结果聚合 | ID: test-warning | 状态: 成功 | 耗时: 0.000秒
2025-08-07 10:03:00 | INFO     | app.services.result_processor:validate_final_response:422 | 响应验证完成: 敏感词0个，检查结果0个
2025-08-07 10:03:00 | INFO     | app.services.compliance_service:result_aggregation_stage:254 | 结果聚合完成 | ID: test-warning | 敏感词: 0个 | 检查结果: 0个
2025-08-07 10:03:00 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 结果聚合阶段 | ID: test-warning | 状态: 成功 | 耗时: 0.000秒
2025-08-07 10:03:00 | INFO     | app.services.compliance_service:_add_pipeline_metadata:462 | 流水线元数据 | ID: test-warning | 处理时间: 0.910秒 | 降级使用: True | 敏感词数量: 0 | 检查结果数量: 0
2025-08-07 10:03:00 | INFO     | app.services.compliance_service:execute_pipeline:403 | 合规性检查流水线完成 | ID: test-warning | 耗时: 0.910秒 | 降级: True
2025-08-07 10:03:00 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 完整合规性检查流水线 | ID: test-warning | 状态: 成功 | 耗时: 0.910秒
2025-08-07 10:38:32 | INFO     | app.services.ai_model_service:_initialize_client:74 | AI模型客户端初始化成功: gemini-2.5-flash
2025-08-07 10:38:32 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | file_processing | ID: test-1 | 状态: 成功 | 耗时: 2.500秒
2025-08-07 10:38:32 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | ai_model_call | ID: test-1 | 状态: 成功 | 耗时: 15.000秒
2025-08-07 10:38:32 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | sensitive_word | ID: test-1 | 状态: 成功 | 耗时: 3.200秒
2025-08-07 10:38:32 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | result_aggregation | ID: test-1 | 状态: 成功 | 耗时: 0.800秒
2025-08-07 10:38:32 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | total_pipeline | ID: test-1 | 状态: 成功 | 耗时: 21.500秒
2025-08-07 10:38:32 | WARNING  | app.core.logger:record_stage_performance:204 | 性能警告 | 阶段: file_processing | ID: test-2 | 耗时: 35.000秒 > 阈值: 30.0秒
2025-08-07 10:38:32 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | file_processing | ID: test-2 | 状态: 成功 | 耗时: 35.000秒
2025-08-07 10:38:32 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | ai_model_call | ID: test-2 | 状态: 成功 | 耗时: 5.000秒
2025-08-07 10:38:32 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | sensitive_word | ID: test-2 | 状态: 失败 | 耗时: 2.000秒
2025-08-07 10:38:32 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | result_aggregation | ID: test-2 | 状态: 成功 | 耗时: 1.000秒
2025-08-07 10:38:32 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | total_pipeline | ID: test-2 | 状态: 成功 | 耗时: 43.000秒
2025-08-07 10:39:33 | INFO     | app.services.ai_model_service:_initialize_client:74 | AI模型客户端初始化成功: gemini-2.5-flash
2025-08-07 10:39:33 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 完整合规性检查流水线 | ID: test-timeout | 详情: {}
2025-08-07 10:39:33 | INFO     | app.services.compliance_service:execute_pipeline:376 | 开始执行合规性检查流水线 | ID: test-timeout | 文件: test.docx
2025-08-07 10:39:33 | INFO     | app.services.compliance_service:execute_pipeline:386 | 步骤1: 开始验证服务前置条件 | ID: test-timeout
2025-08-07 10:39:33 | INFO     | app.services.compliance_service:execute_pipeline:388 | 步骤1: 服务前置条件验证完成 | ID: test-timeout | 状态: {'file_processor': True, 'ai_model': True, 'sensitive_word': True, 'result_processor': True}
2025-08-07 10:39:33 | INFO     | app.services.compliance_service:execute_pipeline:394 | 步骤2: 开始获取项目信息 | ID: test-timeout
2025-08-07 10:39:33 | INFO     | app.services.compliance_service:execute_pipeline:396 | 步骤2: 项目信息获取完成 | ID: test-timeout
2025-08-07 10:39:33 | INFO     | app.services.compliance_service:execute_pipeline:400 | 步骤3: 开始文件处理阶段 | ID: test-timeout
2025-08-07 10:39:35 | INFO     | app.services.compliance_service:execute_pipeline:405 | 步骤3: 文件处理完成 | ID: test-timeout | 内容长度: 4
2025-08-07 10:39:35 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 完整合规性检查流水线 | ID: test-timeout | 状态: 失败 | 耗时: 2.003秒
2025-08-07 10:39:35 | ERROR    | app.core.logger:__exit__:411 | 操作 完整合规性检查流水线 发生异常: 合规性检查流水线超时: 2.0秒 > 1.0秒 (阶段: AI合规性检查)
2025-08-07 10:39:35 | ERROR    | app.services.compliance_service:execute_pipeline:488 | 合规性检查流水线超时 | ID: test-timeout | 耗时: 2.005秒 | 错误: 合规性检查流水线超时: 2.0秒 > 1.0秒 (阶段: AI合规性检查)
2025-08-07 10:39:35 | INFO     | app.services.result_processor:create_empty_response:371 | 创建空响应: 处理超时（2.0秒）
2025-08-07 10:39:35 | INFO     | app.services.compliance_service:_add_pipeline_metadata:538 | 流水线元数据 | ID: test-timeout | 处理时间: 2.005秒 | 降级使用: True | 敏感词数量: 0 | 检查结果数量: 0
2025-08-07 10:39:35 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 敏感词服务健康检查 | ID: test-timeout | 详情: {}
2025-08-07 10:39:35 | INFO     | app.services.sensitive_word_service:check_health:121 | 检查敏感词服务健康状态: http://*************:8087/health
2025-08-07 10:39:35 | INFO     | app.services.sensitive_word_service:check_health:131 | 敏感词服务健康状态: 健康
2025-08-07 10:39:35 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 敏感词服务健康检查 | ID: test-timeout | 状态: 成功 | 耗时: 0.012秒
2025-08-07 10:39:35 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 敏感词检测 | ID: test-timeout | 详情: {}
2025-08-07 10:39:35 | INFO     | app.services.sensitive_word_service:detect_sensitive_words:213 | 开始敏感词检测: 内容长度=4, 政府采购=True
2025-08-07 10:39:35 | INFO     | app.services.sensitive_word_service:detect_sensitive_words:222 | 敏感词检测超时设置: 1.0秒 | ID: test-timeout
2025-08-07 10:39:35 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 敏感词检测 | ID: test-timeout | 状态: 失败 | 耗时: 0.001秒
2025-08-07 10:39:35 | ERROR    | app.core.logger:__exit__:411 | 操作 敏感词检测 发生异常: 请求超时
2025-08-07 10:39:35 | INFO     | app.core.logger:log_api_call:170 | API调用 | 敏感词检测API | ID: test-timeout | 状态: 失败 | 耗时: 0.000秒
2025-08-07 10:39:35 | ERROR    | app.core.logger:wrapper:442 | 函数调用失败: detect_sensitive_words | 耗时: 0.001秒 | 错误: 敏感词检测API请求失败: 请求超时 (API: http://*************:8087/detect, 原因: 请求超时)
2025-08-07 10:39:35 | ERROR    | app.services.sensitive_word_service:detect_with_fallback:435 | 敏感词检测失败，使用降级策略: 敏感词检测API请求失败: 请求超时 (API: http://*************:8087/detect, 原因: 请求超时)
2025-08-07 10:39:35 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 完整合规性检查流水线 | ID: test-warning | 详情: {}
2025-08-07 10:39:35 | INFO     | app.services.compliance_service:execute_pipeline:376 | 开始执行合规性检查流水线 | ID: test-warning | 文件: test.docx
2025-08-07 10:39:35 | INFO     | app.services.compliance_service:execute_pipeline:386 | 步骤1: 开始验证服务前置条件 | ID: test-warning
2025-08-07 10:39:35 | INFO     | app.services.compliance_service:execute_pipeline:388 | 步骤1: 服务前置条件验证完成 | ID: test-warning | 状态: {'file_processor': True, 'ai_model': False, 'sensitive_word': False, 'result_processor': True}
2025-08-07 10:39:35 | INFO     | app.services.compliance_service:execute_pipeline:394 | 步骤2: 开始获取项目信息 | ID: test-warning
2025-08-07 10:39:35 | INFO     | app.services.compliance_service:execute_pipeline:396 | 步骤2: 项目信息获取完成 | ID: test-warning
2025-08-07 10:39:35 | INFO     | app.services.compliance_service:execute_pipeline:400 | 步骤3: 开始文件处理阶段 | ID: test-warning
2025-08-07 10:39:36 | INFO     | app.services.compliance_service:execute_pipeline:405 | 步骤3: 文件处理完成 | ID: test-warning | 内容长度: 4
2025-08-07 10:39:36 | WARNING  | app.services.compliance_service:check_timeout:371 | 合规性检查流水线接近超时: 0.9秒 / 1.0秒 (阶段: AI合规性检查) | ID: test-warning
2025-08-07 10:39:36 | INFO     | app.services.compliance_service:execute_pipeline:416 | 步骤4: 开始AI合规性检查阶段 | ID: test-warning
2025-08-07 10:39:36 | WARNING  | app.services.compliance_service:execute_pipeline:436 | AI模型服务不可用，跳过AI检查 | ID: test-warning
2025-08-07 10:39:36 | WARNING  | app.services.compliance_service:check_timeout:371 | 合规性检查流水线接近超时: 0.9秒 / 1.0秒 (阶段: 敏感词检测) | ID: test-warning
2025-08-07 10:39:36 | WARNING  | app.services.compliance_service:execute_pipeline:458 | 敏感词服务不可用，跳过敏感词检测 | ID: test-warning
2025-08-07 10:39:36 | WARNING  | app.services.compliance_service:check_timeout:371 | 合规性检查流水线接近超时: 0.9秒 / 1.0秒 (阶段: 结果聚合) | ID: test-warning
2025-08-07 10:39:36 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 结果聚合阶段 | ID: test-warning | 详情: {}
2025-08-07 10:39:36 | INFO     | app.services.compliance_service:result_aggregation_stage:305 | 开始结果聚合 | ID: test-warning
2025-08-07 10:39:36 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 结果聚合 | ID: test-warning | 详情: {}
2025-08-07 10:39:36 | INFO     | app.services.result_processor:aggregate_results:314 | 开始结果聚合: 敏感词0个，检查结果0个
2025-08-07 10:39:36 | INFO     | app.services.result_processor:validate_sensitive_words:107 | 敏感词验证完成: 原始0个，有效0个
2025-08-07 10:39:36 | INFO     | app.services.result_processor:deduplicate_sensitive_words:225 | 敏感词去重完成: 原始0个，去重后0个
2025-08-07 10:39:36 | INFO     | app.services.result_processor:validate_check_results:179 | 检查结果验证完成: 原始0个，有效0个
2025-08-07 10:39:36 | INFO     | app.services.result_processor:prioritize_check_results:263 | 检查结果排序完成: 0个结果
2025-08-07 10:39:36 | INFO     | app.services.result_processor:aggregate_results:345 | 结果聚合完成: 敏感词0个，检查结果0个
2025-08-07 10:39:36 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 结果聚合 | ID: test-warning | 状态: 成功 | 耗时: 0.006秒
2025-08-07 10:39:36 | INFO     | app.services.result_processor:validate_final_response:422 | 响应验证完成: 敏感词0个，检查结果0个
2025-08-07 10:39:36 | INFO     | app.services.compliance_service:result_aggregation_stage:312 | 结果聚合完成 | ID: test-warning | 敏感词: 0个 | 检查结果: 0个
2025-08-07 10:39:36 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | result_aggregation | ID: test-warning | 状态: 成功 | 耗时: 0.008秒
2025-08-07 10:39:36 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 结果聚合阶段 | ID: test-warning | 状态: 成功 | 耗时: 0.008秒
2025-08-07 10:39:36 | INFO     | app.services.compliance_service:_add_pipeline_metadata:538 | 流水线元数据 | ID: test-warning | 处理时间: 0.916秒 | 降级使用: True | 敏感词数量: 0 | 检查结果数量: 0
2025-08-07 10:39:36 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | total_pipeline | ID: test-warning | 状态: 成功 | 耗时: 0.916秒
2025-08-07 10:39:36 | INFO     | app.services.compliance_service:execute_pipeline:479 | 合规性检查流水线完成 | ID: test-warning | 耗时: 0.916秒 | 降级: True
2025-08-07 10:39:36 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 完整合规性检查流水线 | ID: test-warning | 状态: 成功 | 耗时: 0.917秒
2025-08-07 10:42:40 | INFO     | app.services.ai_model_service:_initialize_client:74 | AI模型客户端初始化成功: gemini-2.5-flash
2025-08-07 10:42:40 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | file_processing | ID: test-1 | 状态: 成功 | 耗时: 2.500秒
2025-08-07 10:42:40 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | ai_model_call | ID: test-1 | 状态: 成功 | 耗时: 15.000秒
2025-08-07 10:42:40 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | sensitive_word | ID: test-1 | 状态: 成功 | 耗时: 3.200秒
2025-08-07 10:42:40 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | result_aggregation | ID: test-1 | 状态: 成功 | 耗时: 0.800秒
2025-08-07 10:42:40 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | total_pipeline | ID: test-1 | 状态: 成功 | 耗时: 21.500秒
2025-08-07 10:42:40 | WARNING  | app.core.logger:record_stage_performance:204 | 性能警告 | 阶段: file_processing | ID: test-2 | 耗时: 35.000秒 > 阈值: 30.0秒
2025-08-07 10:42:40 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | file_processing | ID: test-2 | 状态: 成功 | 耗时: 35.000秒
2025-08-07 10:42:40 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | ai_model_call | ID: test-2 | 状态: 成功 | 耗时: 5.000秒
2025-08-07 10:42:40 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | sensitive_word | ID: test-2 | 状态: 失败 | 耗时: 2.000秒
2025-08-07 10:42:40 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | result_aggregation | ID: test-2 | 状态: 成功 | 耗时: 1.000秒
2025-08-07 10:42:40 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | total_pipeline | ID: test-2 | 状态: 成功 | 耗时: 43.000秒
2025-08-07 10:44:52 | INFO     | app.services.ai_model_service:_initialize_client:74 | AI模型客户端初始化成功: gemini-2.5-flash
2025-08-07 10:44:52 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | file_processing | ID: test-1 | 状态: 成功 | 耗时: 2.500秒
2025-08-07 10:44:52 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | ai_model_call | ID: test-1 | 状态: 成功 | 耗时: 15.000秒
2025-08-07 10:44:52 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | sensitive_word | ID: test-1 | 状态: 成功 | 耗时: 3.200秒
2025-08-07 10:44:52 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | result_aggregation | ID: test-1 | 状态: 成功 | 耗时: 0.800秒
2025-08-07 10:44:52 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | total_pipeline | ID: test-1 | 状态: 成功 | 耗时: 21.500秒
2025-08-07 10:44:52 | WARNING  | app.core.logger:record_stage_performance:204 | 性能警告 | 阶段: file_processing | ID: test-2 | 耗时: 35.000秒 > 阈值: 30.0秒
2025-08-07 10:44:52 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | file_processing | ID: test-2 | 状态: 成功 | 耗时: 35.000秒
2025-08-07 10:44:52 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | ai_model_call | ID: test-2 | 状态: 成功 | 耗时: 5.000秒
2025-08-07 10:44:52 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | sensitive_word | ID: test-2 | 状态: 失败 | 耗时: 2.000秒
2025-08-07 10:44:52 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | result_aggregation | ID: test-2 | 状态: 成功 | 耗时: 1.000秒
2025-08-07 10:44:52 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | total_pipeline | ID: test-2 | 状态: 成功 | 耗时: 43.000秒
2025-08-07 10:45:00 | INFO     | main:lifespan:26 | 招标文件合规性检查助手关闭
2025-08-07 10:45:04 | INFO     | app.services.ai_model_service:_initialize_client:74 | AI模型客户端初始化成功: gemini-2.5-flash
2025-08-07 10:45:04 | INFO     | main:lifespan:22 | 招标文件合规性检查助手启动中...
2025-08-07 10:45:04 | INFO     | main:lifespan:23 | 配置信息: 模型=gemini-2.5-flash, 环境=development
2025-08-07 10:45:25 | INFO     | app.services.ai_model_service:_initialize_client:74 | AI模型客户端初始化成功: gemini-2.5-flash
2025-08-07 10:45:25 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | file_processing | ID: test-1 | 状态: 成功 | 耗时: 2.500秒
2025-08-07 10:45:25 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | ai_model_call | ID: test-1 | 状态: 成功 | 耗时: 15.000秒
2025-08-07 10:45:25 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | sensitive_word | ID: test-1 | 状态: 成功 | 耗时: 3.200秒
2025-08-07 10:45:25 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | result_aggregation | ID: test-1 | 状态: 成功 | 耗时: 0.800秒
2025-08-07 10:45:25 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | total_pipeline | ID: test-1 | 状态: 成功 | 耗时: 21.500秒
2025-08-07 10:45:25 | WARNING  | app.core.logger:record_stage_performance:204 | 性能警告 | 阶段: file_processing | ID: test-2 | 耗时: 35.000秒 > 阈值: 30.0秒
2025-08-07 10:45:25 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | file_processing | ID: test-2 | 状态: 成功 | 耗时: 35.000秒
2025-08-07 10:45:25 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | ai_model_call | ID: test-2 | 状态: 成功 | 耗时: 5.000秒
2025-08-07 10:45:25 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | sensitive_word | ID: test-2 | 状态: 失败 | 耗时: 2.000秒
2025-08-07 10:45:25 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | result_aggregation | ID: test-2 | 状态: 成功 | 耗时: 1.000秒
2025-08-07 10:45:25 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | total_pipeline | ID: test-2 | 状态: 成功 | 耗时: 43.000秒
2025-08-07 14:02:44 | INFO     | main:lifespan:26 | 招标文件合规性检查助手关闭
2025-08-07 14:02:48 | INFO     | app.services.ai_model_service:_initialize_client:74 | AI模型客户端初始化成功: gemini-2.5-flash
2025-08-07 14:02:49 | INFO     | main:lifespan:22 | 招标文件合规性检查助手启动中...
2025-08-07 14:02:49 | INFO     | main:lifespan:23 | 配置信息: 模型=gemini-2.5-flash, 环境=development
2025-08-07 14:03:19 | INFO     | app.services.ai_model_service:_initialize_client:74 | AI模型客户端初始化成功: gemini-2.5-flash
2025-08-07 14:03:19 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | file_processing | ID: test-1 | 状态: 成功 | 耗时: 2.500秒
2025-08-07 14:03:19 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | ai_model_call | ID: test-1 | 状态: 成功 | 耗时: 15.000秒
2025-08-07 14:03:19 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | sensitive_word | ID: test-1 | 状态: 成功 | 耗时: 3.200秒
2025-08-07 14:03:19 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | result_aggregation | ID: test-1 | 状态: 成功 | 耗时: 0.800秒
2025-08-07 14:03:19 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | total_pipeline | ID: test-1 | 状态: 成功 | 耗时: 21.500秒
2025-08-07 14:03:19 | WARNING  | app.core.logger:record_stage_performance:204 | 性能警告 | 阶段: file_processing | ID: test-2 | 耗时: 35.000秒 > 阈值: 30.0秒
2025-08-07 14:03:19 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | file_processing | ID: test-2 | 状态: 成功 | 耗时: 35.000秒
2025-08-07 14:03:19 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | ai_model_call | ID: test-2 | 状态: 成功 | 耗时: 5.000秒
2025-08-07 14:03:19 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | sensitive_word | ID: test-2 | 状态: 失败 | 耗时: 2.000秒
2025-08-07 14:03:19 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | result_aggregation | ID: test-2 | 状态: 成功 | 耗时: 1.000秒
2025-08-07 14:03:19 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | total_pipeline | ID: test-2 | 状态: 成功 | 耗时: 43.000秒
2025-08-07 16:47:21 | INFO     | main:lifespan:26 | 招标文件合规性检查助手关闭
2025-08-07 16:47:25 | INFO     | app.services.ai_model_service:_initialize_client:74 | AI模型客户端初始化成功: gemini-2.5-flash
2025-08-07 16:47:26 | INFO     | main:lifespan:22 | 招标文件合规性检查助手启动中...
2025-08-07 16:47:26 | INFO     | main:lifespan:23 | 配置信息: 模型=gemini-2.5-flash, 环境=development
2025-08-07 16:47:33 | INFO     | app.services.ai_model_service:_initialize_client:74 | AI模型客户端初始化成功: gemini-2.5-flash
2025-08-07 16:47:33 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | file_processing | ID: test-1 | 状态: 成功 | 耗时: 2.500秒
2025-08-07 16:47:33 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | ai_model_call | ID: test-1 | 状态: 成功 | 耗时: 15.000秒
2025-08-07 16:47:33 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | sensitive_word | ID: test-1 | 状态: 成功 | 耗时: 3.200秒
2025-08-07 16:47:33 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | result_aggregation | ID: test-1 | 状态: 成功 | 耗时: 0.800秒
2025-08-07 16:47:33 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | total_pipeline | ID: test-1 | 状态: 成功 | 耗时: 21.500秒
2025-08-07 16:47:33 | WARNING  | app.core.logger:record_stage_performance:204 | 性能警告 | 阶段: file_processing | ID: test-2 | 耗时: 35.000秒 > 阈值: 30.0秒
2025-08-07 16:47:33 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | file_processing | ID: test-2 | 状态: 成功 | 耗时: 35.000秒
2025-08-07 16:47:33 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | ai_model_call | ID: test-2 | 状态: 成功 | 耗时: 5.000秒
2025-08-07 16:47:33 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | sensitive_word | ID: test-2 | 状态: 失败 | 耗时: 2.000秒
2025-08-07 16:47:33 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | result_aggregation | ID: test-2 | 状态: 成功 | 耗时: 1.000秒
2025-08-07 16:47:33 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | total_pipeline | ID: test-2 | 状态: 成功 | 耗时: 43.000秒
2025-08-07 16:50:44 | INFO     | app.services.ai_model_service:_initialize_client:74 | AI模型客户端初始化成功: gemini-2.5-flash
2025-08-07 16:50:44 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | file_processing | ID: test-1 | 状态: 成功 | 耗时: 2.500秒
2025-08-07 16:50:44 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | ai_model_call | ID: test-1 | 状态: 成功 | 耗时: 15.000秒
2025-08-07 16:50:44 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | sensitive_word | ID: test-1 | 状态: 成功 | 耗时: 3.200秒
2025-08-07 16:50:44 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | result_aggregation | ID: test-1 | 状态: 成功 | 耗时: 0.800秒
2025-08-07 16:50:44 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | total_pipeline | ID: test-1 | 状态: 成功 | 耗时: 21.500秒
2025-08-07 16:50:44 | WARNING  | app.core.logger:record_stage_performance:204 | 性能警告 | 阶段: file_processing | ID: test-2 | 耗时: 35.000秒 > 阈值: 30.0秒
2025-08-07 16:50:44 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | file_processing | ID: test-2 | 状态: 成功 | 耗时: 35.000秒
2025-08-07 16:50:44 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | ai_model_call | ID: test-2 | 状态: 成功 | 耗时: 5.000秒
2025-08-07 16:50:44 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | sensitive_word | ID: test-2 | 状态: 失败 | 耗时: 2.000秒
2025-08-07 16:50:44 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | result_aggregation | ID: test-2 | 状态: 成功 | 耗时: 1.000秒
2025-08-07 16:50:44 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | total_pipeline | ID: test-2 | 状态: 成功 | 耗时: 43.000秒
2025-08-07 16:50:44 | INFO     | app.services.compliance_service:validate_prerequisites:73 | 开始验证服务前置条件 | ID: 
2025-08-07 16:50:44 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 敏感词服务健康检查 | ID:  | 详情: {}
2025-08-07 16:50:44 | INFO     | app.services.sensitive_word_service:check_health:121 | 检查敏感词服务健康状态: http://*************:8087/health
2025-08-07 16:50:44 | INFO     | app.services.sensitive_word_service:check_health:131 | 敏感词服务健康状态: 健康
2025-08-07 16:50:44 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 敏感词服务健康检查 | ID:  | 状态: 成功 | 耗时: 0.046秒
2025-08-07 16:50:44 | INFO     | app.services.compliance_service:validate_prerequisites:92 | 服务健康检查完成 | ID:  | 状态: {'file_processor': True, 'ai_model': True, 'sensitive_word': True, 'result_processor': True}
2025-08-07 16:50:44 | INFO     | app.core.logger:cleanup_old_metrics:305 | 清理了 2 个过期的性能指标记录
2025-08-07 16:57:18 | INFO     | app.services.ai_model_service:_initialize_client:75 | AI模型客户端初始化成功: gemini-2.5-flash
2025-08-07 17:05:16 | INFO     | app.middleware.logging:dispatch:35 | 请求开始 | ID: f1acfc52-344b-4051-89e8-5948105f8173 | 方法: GET | URL: http://localhost:8088/health | 客户端IP: 127.0.0.1 | User-Agent: python-requests/2.31.0
2025-08-07 17:05:16 | INFO     | app.middleware.logging:dispatch:48 | 请求完成 | ID: f1acfc52-344b-4051-89e8-5948105f8173 | 状态码: 200 | 耗时: 0.002秒
2025-08-07 17:05:18 | INFO     | app.middleware.logging:dispatch:35 | 请求开始 | ID: a0f2b7e9-9d33-4bd0-9a13-e44c0e5266bf | 方法: POST | URL: http://localhost:8088/api/v1/check-compliance | 客户端IP: 127.0.0.1 | User-Agent: python-requests/2.31.0
2025-08-07 17:05:18 | INFO     | app.middleware.validation:_validate_request:75 | 跳过中间件验证 | ID: a0f2b7e9-9d33-4bd0-9a13-e44c0e5266bf | 路径: /api/v1/check-compliance
2025-08-07 17:05:18 | INFO     | app.api.routes:check_compliance:78 | 开始合规性检查 | ID: a0f2b7e9-9d33-4bd0-9a13-e44c0e5266bf | 文件: 南区汽水厂、生活垃圾一体化扫管服务项目二次招标文件（发唱版）.docx
2025-08-07 17:05:18 | INFO     | app.services.compliance_service:check_compliance:636 | 合规性检查服务开始 | ID: a0f2b7e9-9d33-4bd0-9a13-e44c0e5266bf
2025-08-07 17:05:18 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 完整合规性检查流水线 | ID: a0f2b7e9-9d33-4bd0-9a13-e44c0e5266bf | 详情: {}
2025-08-07 17:05:18 | INFO     | app.services.compliance_service:execute_pipeline:376 | 开始执行合规性检查流水线 | ID: a0f2b7e9-9d33-4bd0-9a13-e44c0e5266bf | 文件: 南区汽水厂、生活垃圾一体化扫管服务项目二次招标文件（发唱版）.docx
2025-08-07 17:05:18 | INFO     | app.services.compliance_service:execute_pipeline:386 | 步骤1: 开始验证服务前置条件 | ID: a0f2b7e9-9d33-4bd0-9a13-e44c0e5266bf
2025-08-07 17:05:18 | INFO     | app.services.compliance_service:validate_prerequisites:73 | 开始验证服务前置条件 | ID: a0f2b7e9-9d33-4bd0-9a13-e44c0e5266bf
2025-08-07 17:05:18 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 敏感词服务健康检查 | ID: a0f2b7e9-9d33-4bd0-9a13-e44c0e5266bf | 详情: {}
2025-08-07 17:05:18 | INFO     | app.services.sensitive_word_service:check_health:121 | 检查敏感词服务健康状态: http://*************:8087/health
2025-08-07 17:05:18 | INFO     | app.services.sensitive_word_service:check_health:131 | 敏感词服务健康状态: 健康
2025-08-07 17:05:18 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 敏感词服务健康检查 | ID: a0f2b7e9-9d33-4bd0-9a13-e44c0e5266bf | 状态: 成功 | 耗时: 0.009秒
2025-08-07 17:05:18 | INFO     | app.services.compliance_service:validate_prerequisites:92 | 服务健康检查完成 | ID: a0f2b7e9-9d33-4bd0-9a13-e44c0e5266bf | 状态: {'file_processor': True, 'ai_model': True, 'sensitive_word': True, 'result_processor': True}
2025-08-07 17:05:18 | INFO     | app.services.compliance_service:execute_pipeline:388 | 步骤1: 服务前置条件验证完成 | ID: a0f2b7e9-9d33-4bd0-9a13-e44c0e5266bf | 状态: {'file_processor': True, 'ai_model': True, 'sensitive_word': True, 'result_processor': True}
2025-08-07 17:05:18 | INFO     | app.services.compliance_service:execute_pipeline:394 | 步骤2: 开始获取项目信息 | ID: a0f2b7e9-9d33-4bd0-9a13-e44c0e5266bf
2025-08-07 17:05:18 | INFO     | app.services.compliance_service:execute_pipeline:396 | 步骤2: 项目信息获取完成 | ID: a0f2b7e9-9d33-4bd0-9a13-e44c0e5266bf
2025-08-07 17:05:18 | INFO     | app.services.compliance_service:execute_pipeline:400 | 步骤3: 开始文件处理阶段 | ID: a0f2b7e9-9d33-4bd0-9a13-e44c0e5266bf
2025-08-07 17:05:18 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 文件处理阶段 | ID: a0f2b7e9-9d33-4bd0-9a13-e44c0e5266bf | 详情: {}
2025-08-07 17:05:18 | INFO     | app.services.compliance_service:process_file_stage:114 | 开始文件处理阶段 | ID: a0f2b7e9-9d33-4bd0-9a13-e44c0e5266bf | 文件: 南区汽水厂、生活垃圾一体化扫管服务项目二次招标文件（发唱版）.docx
2025-08-07 17:05:18 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 优化文件处理流程 | ID: a0f2b7e9-9d33-4bd0-9a13-e44c0e5266bf | 详情: {}
2025-08-07 17:05:18 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 文件下载 | ID: a0f2b7e9-9d33-4bd0-9a13-e44c0e5266bf | 详情: {}
2025-08-07 17:05:18 | INFO     | app.services.file_processor_v2:download_file:255 | 开始下载文件: http://localhost:9090/api/v1/download-shared-object/aHR0cDovLzEyNy4wLjAuMTo5MDAwL3poYW9iaWFvd2VuamlhbmppYW5jaGF6aHVzaG91LyVFNSU4RCU5NyVFNSU4QyVCQSVFNiVCMSVBMSVFNiVCMCVCNCVFNSU4QyVCQiVFNSVCQSU5RiVFMyU4MCU4MSVFNyU5NCU5RiVFNiVCNCVCQiVFNSU5RSU4MyVFNSU5QyVCRSVFNCVCOCU4MCVFNCVCRCU5MyVFNSU4QyU5NiVFNiU4OSU5OCVFNyVBRSVBMSVFNiU5QyU4RCVFNSU4QSVBMSVFOSVBMSVCOSVFNyU5QiVBRSVFNCVCQSU4QyVFNiVBQyVBMSVFNiU4QiU5QiVFNiVBMCU4NyVFNiU5NiU4NyVFNCVCQiVCNiVFRiVCQyU4OCVFNSU4RiU5MSVFNSU5NCVBRSVFNyU4OSU4OCVFRiVCQyU4OS5kb2N4P1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9SDNLMVAwMkZBQjJKRUZZSFRUUTAlMkYyMDI1MDgwNiUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNTA4MDZUMTAwNTU4WiZYLUFtei1FeHBpcmVzPTQzMTk5JlgtQW16LVNlY3VyaXR5LVRva2VuPWV5SmhiR2NpT2lKSVV6VXhNaUlzSW5SNWNDSTZJa3BYVkNKOS5leUpoWTJObGMzTkxaWGtpT2lKSU0wc3hVREF5UmtGQ01rcEZSbGxJVkZSUk1DSXNJbVY0Y0NJNk1UYzFORFV4TnpVMU5Dd2ljR0Z5Wlc1MElqb2ljR1Z1WjJOb1lXNW5JbjAuUGdjMXpiSEZlS3diMkw2cFZ5b2VfVVRlNXVmU3l5UjEySFNhZ1dSWkdfRVo3bFVfcEVTNExlWEZMVkFud0doZEw3NVpENjNmcGFCVl81VGcwcnJvVXcmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0JnZlcnNpb25JZD1udWxsJlgtQW16LVNpZ25hdHVyZT1mMzRmNGIzYmJhNjliMzNiZjExZjJmNmFiNWU2ZjYwMmVhNGNjZjc5NzFhOTY5MDkzYTM0MmEwZTQzMmE5MTE3
2025-08-07 17:05:18 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 文件下载 | ID: a0f2b7e9-9d33-4bd0-9a13-e44c0e5266bf | 状态: 失败 | 耗时: 0.021秒
2025-08-07 17:05:18 | ERROR    | app.core.logger:__exit__:421 | 操作 文件下载 发生异常: 403 Client Error: Forbidden for url: http://localhost:9090/api/v1/download-shared-object/aHR0cDovLzEyNy4wLjAuMTo5MDAwL3poYW9iaWFvd2VuamlhbmppYW5jaGF6aHVzaG91LyVFNSU4RCU5NyVFNSU4QyVCQSVFNiVCMSVBMSVFNiVCMCVCNCVFNSU4QyVCQiVFNSVCQSU5RiVFMyU4MCU4MSVFNyU5NCU5RiVFNiVCNCVCQiVFNSU5RSU4MyVFNSU5QyVCRSVFNCVCOCU4MCVFNCVCRCU5MyVFNSU4QyU5NiVFNiU4OSU5OCVFNyVBRSVBMSVFNiU5QyU4RCVFNSU4QSVBMSVFOSVBMSVCOSVFNyU5QiVBRSVFNCVCQSU4QyVFNiVBQyVBMSVFNiU4QiU5QiVFNiVBMCU4NyVFNiU5NiU4NyVFNCVCQiVCNiVFRiVCQyU4OCVFNSU4RiU5MSVFNSU5NCVBRSVFNyU4OSU4OCVFRiVCQyU4OS5kb2N4P1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9SDNLMVAwMkZBQjJKRUZZSFRUUTAlMkYyMDI1MDgwNiUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNTA4MDZUMTAwNTU4WiZYLUFtei1FeHBpcmVzPTQzMTk5JlgtQW16LVNlY3VyaXR5LVRva2VuPWV5SmhiR2NpT2lKSVV6VXhNaUlzSW5SNWNDSTZJa3BYVkNKOS5leUpoWTJObGMzTkxaWGtpT2lKSU0wc3hVREF5UmtGQ01rcEZSbGxJVkZSUk1DSXNJbVY0Y0NJNk1UYzFORFV4TnpVMU5Dd2ljR0Z5Wlc1MElqb2ljR1Z1WjJOb1lXNW5JbjAuUGdjMXpiSEZlS3diMkw2cFZ5b2VfVVRlNXVmU3l5UjEySFNhZ1dSWkdfRVo3bFVfcEVTNExlWEZMVkFud0doZEw3NVpENjNmcGFCVl81VGcwcnJvVXcmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0JnZlcnNpb25JZD1udWxsJlgtQW16LVNpZ25hdHVyZT1mMzRmNGIzYmJhNjliMzNiZjExZjJmNmFiNWU2ZjYwMmVhNGNjZjc5NzFhOTY5MDkzYTM0MmEwZTQzMmE5MTE3
2025-08-07 17:05:18 | ERROR    | app.core.logger:wrapper:452 | 函数调用失败: download_file | 耗时: 0.022秒 | 错误: 文件下载失败: 403 Client Error: Forbidden for url: http://localhost:9090/api/v1/download-shared-object/aHR0cDovLzEyNy4wLjAuMTo5MDAwL3poYW9iaWFvd2VuamlhbmppYW5jaGF6aHVzaG91LyVFNSU4RCU5NyVFNSU4QyVCQSVFNiVCMSVBMSVFNiVCMCVCNCVFNSU4QyVCQiVFNSVCQSU5RiVFMyU4MCU4MSVFNyU5NCU5RiVFNiVCNCVCQiVFNSU5RSU4MyVFNSU5QyVCRSVFNCVCOCU4MCVFNCVCRCU5MyVFNSU4QyU5NiVFNiU4OSU5OCVFNyVBRSVBMSVFNiU5QyU4RCVFNSU4QSVBMSVFOSVBMSVCOSVFNyU5QiVBRSVFNCVCQSU4QyVFNiVBQyVBMSVFNiU4QiU5QiVFNiVBMCU4NyVFNiU5NiU4NyVFNCVCQiVCNiVFRiVCQyU4OCVFNSU4RiU5MSVFNSU5NCVBRSVFNyU4OSU4OCVFRiVCQyU4OS5kb2N4P1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9SDNLMVAwMkZBQjJKRUZZSFRUUTAlMkYyMDI1MDgwNiUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNTA4MDZUMTAwNTU4WiZYLUFtei1FeHBpcmVzPTQzMTk5JlgtQW16LVNlY3VyaXR5LVRva2VuPWV5SmhiR2NpT2lKSVV6VXhNaUlzSW5SNWNDSTZJa3BYVkNKOS5leUpoWTJObGMzTkxaWGtpT2lKSU0wc3hVREF5UmtGQ01rcEZSbGxJVkZSUk1DSXNJbVY0Y0NJNk1UYzFORFV4TnpVMU5Dd2ljR0Z5Wlc1MElqb2ljR1Z1WjJOb1lXNW5JbjAuUGdjMXpiSEZlS3diMkw2cFZ5b2VfVVRlNXVmU3l5UjEySFNhZ1dSWkdfRVo3bFVfcEVTNExlWEZMVkFud0doZEw3NVpENjNmcGFCVl81VGcwcnJvVXcmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0JnZlcnNpb25JZD1udWxsJlgtQW16LVNpZ25hdHVyZT1mMzRmNGIzYmJhNjliMzNiZjExZjJmNmFiNWU2ZjYwMmVhNGNjZjc5NzFhOTY5MDkzYTM0MmEwZTQzMmE5MTE3 (URL: http://localhost:9090/api/v1/download-shared-object/aHR0cDovLzEyNy4wLjAuMTo5MDAwL3poYW9iaWFvd2VuamlhbmppYW5jaGF6aHVzaG91LyVFNSU4RCU5NyVFNSU4QyVCQSVFNiVCMSVBMSVFNiVCMCVCNCVFNSU4QyVCQiVFNSVCQSU5RiVFMyU4MCU4MSVFNyU5NCU5RiVFNiVCNCVCQiVFNSU5RSU4MyVFNSU5QyVCRSVFNCVCOCU4MCVFNCVCRCU5MyVFNSU4QyU5NiVFNiU4OSU5OCVFNyVBRSVBMSVFNiU5QyU4RCVFNSU4QSVBMSVFOSVBMSVCOSVFNyU5QiVBRSVFNCVCQSU4QyVFNiVBQyVBMSVFNiU4QiU5QiVFNiVBMCU4NyVFNiU5NiU4NyVFNCVCQiVCNiVFRiVCQyU4OCVFNSU4RiU5MSVFNSU5NCVBRSVFNyU4OSU4OCVFRiVCQyU4OS5kb2N4P1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9SDNLMVAwMkZBQjJKRUZZSFRUUTAlMkYyMDI1MDgwNiUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNTA4MDZUMTAwNTU4WiZYLUFtei1FeHBpcmVzPTQzMTk5JlgtQW16LVNlY3VyaXR5LVRva2VuPWV5SmhiR2NpT2lKSVV6VXhNaUlzSW5SNWNDSTZJa3BYVkNKOS5leUpoWTJObGMzTkxaWGtpT2lKSU0wc3hVREF5UmtGQ01rcEZSbGxJVkZSUk1DSXNJbVY0Y0NJNk1UYzFORFV4TnpVMU5Dd2ljR0Z5Wlc1MElqb2ljR1Z1WjJOb1lXNW5JbjAuUGdjMXpiSEZlS3diMkw2cFZ5b2VfVVRlNXVmU3l5UjEySFNhZ1dSWkdfRVo3bFVfcEVTNExlWEZMVkFud0doZEw3NVpENjNmcGFCVl81VGcwcnJvVXcmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0JnZlcnNpb25JZD1udWxsJlgtQW16LVNpZ25hdHVyZT1mMzRmNGIzYmJhNjliMzNiZjExZjJmNmFiNWU2ZjYwMmVhNGNjZjc5NzFhOTY5MDkzYTM0MmEwZTQzMmE5MTE3)
2025-08-07 17:05:18 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 优化文件处理流程 | ID: a0f2b7e9-9d33-4bd0-9a13-e44c0e5266bf | 状态: 失败 | 耗时: 0.023秒
2025-08-07 17:05:18 | ERROR    | app.core.logger:__exit__:421 | 操作 优化文件处理流程 发生异常: 文件下载失败: 403 Client Error: Forbidden for url: http://localhost:9090/api/v1/download-shared-object/aHR0cDovLzEyNy4wLjAuMTo5MDAwL3poYW9iaWFvd2VuamlhbmppYW5jaGF6aHVzaG91LyVFNSU4RCU5NyVFNSU4QyVCQSVFNiVCMSVBMSVFNiVCMCVCNCVFNSU4QyVCQiVFNSVCQSU5RiVFMyU4MCU4MSVFNyU5NCU5RiVFNiVCNCVCQiVFNSU5RSU4MyVFNSU5QyVCRSVFNCVCOCU4MCVFNCVCRCU5MyVFNSU4QyU5NiVFNiU4OSU5OCVFNyVBRSVBMSVFNiU5QyU4RCVFNSU4QSVBMSVFOSVBMSVCOSVFNyU5QiVBRSVFNCVCQSU4QyVFNiVBQyVBMSVFNiU4QiU5QiVFNiVBMCU4NyVFNiU5NiU4NyVFNCVCQiVCNiVFRiVCQyU4OCVFNSU4RiU5MSVFNSU5NCVBRSVFNyU4OSU4OCVFRiVCQyU4OS5kb2N4P1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9SDNLMVAwMkZBQjJKRUZZSFRUUTAlMkYyMDI1MDgwNiUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNTA4MDZUMTAwNTU4WiZYLUFtei1FeHBpcmVzPTQzMTk5JlgtQW16LVNlY3VyaXR5LVRva2VuPWV5SmhiR2NpT2lKSVV6VXhNaUlzSW5SNWNDSTZJa3BYVkNKOS5leUpoWTJObGMzTkxaWGtpT2lKSU0wc3hVREF5UmtGQ01rcEZSbGxJVkZSUk1DSXNJbVY0Y0NJNk1UYzFORFV4TnpVMU5Dd2ljR0Z5Wlc1MElqb2ljR1Z1WjJOb1lXNW5JbjAuUGdjMXpiSEZlS3diMkw2cFZ5b2VfVVRlNXVmU3l5UjEySFNhZ1dSWkdfRVo3bFVfcEVTNExlWEZMVkFud0doZEw3NVpENjNmcGFCVl81VGcwcnJvVXcmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0JnZlcnNpb25JZD1udWxsJlgtQW16LVNpZ25hdHVyZT1mMzRmNGIzYmJhNjliMzNiZjExZjJmNmFiNWU2ZjYwMmVhNGNjZjc5NzFhOTY5MDkzYTM0MmEwZTQzMmE5MTE3 (URL: http://localhost:9090/api/v1/download-shared-object/aHR0cDovLzEyNy4wLjAuMTo5MDAwL3poYW9iaWFvd2VuamlhbmppYW5jaGF6aHVzaG91LyVFNSU4RCU5NyVFNSU4QyVCQSVFNiVCMSVBMSVFNiVCMCVCNCVFNSU4QyVCQiVFNSVCQSU5RiVFMyU4MCU4MSVFNyU5NCU5RiVFNiVCNCVCQiVFNSU5RSU4MyVFNSU5QyVCRSVFNCVCOCU4MCVFNCVCRCU5MyVFNSU4QyU5NiVFNiU4OSU5OCVFNyVBRSVBMSVFNiU5QyU4RCVFNSU4QSVBMSVFOSVBMSVCOSVFNyU5QiVBRSVFNCVCQSU4QyVFNiVBQyVBMSVFNiU4QiU5QiVFNiVBMCU4NyVFNiU5NiU4NyVFNCVCQiVCNiVFRiVCQyU4OCVFNSU4RiU5MSVFNSU5NCVBRSVFNyU4OSU4OCVFRiVCQyU4OS5kb2N4P1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9SDNLMVAwMkZBQjJKRUZZSFRUUTAlMkYyMDI1MDgwNiUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNTA4MDZUMTAwNTU4WiZYLUFtei1FeHBpcmVzPTQzMTk5JlgtQW16LVNlY3VyaXR5LVRva2VuPWV5SmhiR2NpT2lKSVV6VXhNaUlzSW5SNWNDSTZJa3BYVkNKOS5leUpoWTJObGMzTkxaWGtpT2lKSU0wc3hVREF5UmtGQ01rcEZSbGxJVkZSUk1DSXNJbVY0Y0NJNk1UYzFORFV4TnpVMU5Dd2ljR0Z5Wlc1MElqb2ljR1Z1WjJOb1lXNW5JbjAuUGdjMXpiSEZlS3diMkw2cFZ5b2VfVVRlNXVmU3l5UjEySFNhZ1dSWkdfRVo3bFVfcEVTNExlWEZMVkFud0doZEw3NVpENjNmcGFCVl81VGcwcnJvVXcmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0JnZlcnNpb25JZD1udWxsJlgtQW16LVNpZ25hdHVyZT1mMzRmNGIzYmJhNjliMzNiZjExZjJmNmFiNWU2ZjYwMmVhNGNjZjc5NzFhOTY5MDkzYTM0MmEwZTQzMmE5MTE3)
2025-08-07 17:05:18 | ERROR    | app.core.logger:wrapper:452 | 函数调用失败: process_file | 耗时: 0.025秒 | 错误: 文件下载失败: 403 Client Error: Forbidden for url: http://localhost:9090/api/v1/download-shared-object/aHR0cDovLzEyNy4wLjAuMTo5MDAwL3poYW9iaWFvd2VuamlhbmppYW5jaGF6aHVzaG91LyVFNSU4RCU5NyVFNSU4QyVCQSVFNiVCMSVBMSVFNiVCMCVCNCVFNSU4QyVCQiVFNSVCQSU5RiVFMyU4MCU4MSVFNyU5NCU5RiVFNiVCNCVCQiVFNSU5RSU4MyVFNSU5QyVCRSVFNCVCOCU4MCVFNCVCRCU5MyVFNSU4QyU5NiVFNiU4OSU5OCVFNyVBRSVBMSVFNiU5QyU4RCVFNSU4QSVBMSVFOSVBMSVCOSVFNyU5QiVBRSVFNCVCQSU4QyVFNiVBQyVBMSVFNiU4QiU5QiVFNiVBMCU4NyVFNiU5NiU4NyVFNCVCQiVCNiVFRiVCQyU4OCVFNSU4RiU5MSVFNSU5NCVBRSVFNyU4OSU4OCVFRiVCQyU4OS5kb2N4P1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9SDNLMVAwMkZBQjJKRUZZSFRUUTAlMkYyMDI1MDgwNiUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNTA4MDZUMTAwNTU4WiZYLUFtei1FeHBpcmVzPTQzMTk5JlgtQW16LVNlY3VyaXR5LVRva2VuPWV5SmhiR2NpT2lKSVV6VXhNaUlzSW5SNWNDSTZJa3BYVkNKOS5leUpoWTJObGMzTkxaWGtpT2lKSU0wc3hVREF5UmtGQ01rcEZSbGxJVkZSUk1DSXNJbVY0Y0NJNk1UYzFORFV4TnpVMU5Dd2ljR0Z5Wlc1MElqb2ljR1Z1WjJOb1lXNW5JbjAuUGdjMXpiSEZlS3diMkw2cFZ5b2VfVVRlNXVmU3l5UjEySFNhZ1dSWkdfRVo3bFVfcEVTNExlWEZMVkFud0doZEw3NVpENjNmcGFCVl81VGcwcnJvVXcmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0JnZlcnNpb25JZD1udWxsJlgtQW16LVNpZ25hdHVyZT1mMzRmNGIzYmJhNjliMzNiZjExZjJmNmFiNWU2ZjYwMmVhNGNjZjc5NzFhOTY5MDkzYTM0MmEwZTQzMmE5MTE3 (URL: http://localhost:9090/api/v1/download-shared-object/aHR0cDovLzEyNy4wLjAuMTo5MDAwL3poYW9iaWFvd2VuamlhbmppYW5jaGF6aHVzaG91LyVFNSU4RCU5NyVFNSU4QyVCQSVFNiVCMSVBMSVFNiVCMCVCNCVFNSU4QyVCQiVFNSVCQSU5RiVFMyU4MCU4MSVFNyU5NCU5RiVFNiVCNCVCQiVFNSU5RSU4MyVFNSU5QyVCRSVFNCVCOCU4MCVFNCVCRCU5MyVFNSU4QyU5NiVFNiU4OSU5OCVFNyVBRSVBMSVFNiU5QyU4RCVFNSU4QSVBMSVFOSVBMSVCOSVFNyU5QiVBRSVFNCVCQSU4QyVFNiVBQyVBMSVFNiU4QiU5QiVFNiVBMCU4NyVFNiU5NiU4NyVFNCVCQiVCNiVFRiVCQyU4OCVFNSU4RiU5MSVFNSU5NCVBRSVFNyU4OSU4OCVFRiVCQyU4OS5kb2N4P1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9SDNLMVAwMkZBQjJKRUZZSFRUUTAlMkYyMDI1MDgwNiUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNTA4MDZUMTAwNTU4WiZYLUFtei1FeHBpcmVzPTQzMTk5JlgtQW16LVNlY3VyaXR5LVRva2VuPWV5SmhiR2NpT2lKSVV6VXhNaUlzSW5SNWNDSTZJa3BYVkNKOS5leUpoWTJObGMzTkxaWGtpT2lKSU0wc3hVREF5UmtGQ01rcEZSbGxJVkZSUk1DSXNJbVY0Y0NJNk1UYzFORFV4TnpVMU5Dd2ljR0Z5Wlc1MElqb2ljR1Z1WjJOb1lXNW5JbjAuUGdjMXpiSEZlS3diMkw2cFZ5b2VfVVRlNXVmU3l5UjEySFNhZ1dSWkdfRVo3bFVfcEVTNExlWEZMVkFud0doZEw3NVpENjNmcGFCVl81VGcwcnJvVXcmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0JnZlcnNpb25JZD1udWxsJlgtQW16LVNpZ25hdHVyZT1mMzRmNGIzYmJhNjliMzNiZjExZjJmNmFiNWU2ZjYwMmVhNGNjZjc5NzFhOTY5MDkzYTM0MmEwZTQzMmE5MTE3)
2025-08-07 17:05:18 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 文件处理阶段 | ID: a0f2b7e9-9d33-4bd0-9a13-e44c0e5266bf | 状态: 失败 | 耗时: 0.026秒
2025-08-07 17:05:18 | ERROR    | app.core.logger:__exit__:421 | 操作 文件处理阶段 发生异常: 文件下载失败: 403 Client Error: Forbidden for url: http://localhost:9090/api/v1/download-shared-object/aHR0cDovLzEyNy4wLjAuMTo5MDAwL3poYW9iaWFvd2VuamlhbmppYW5jaGF6aHVzaG91LyVFNSU4RCU5NyVFNSU4QyVCQSVFNiVCMSVBMSVFNiVCMCVCNCVFNSU4QyVCQiVFNSVCQSU5RiVFMyU4MCU4MSVFNyU5NCU5RiVFNiVCNCVCQiVFNSU5RSU4MyVFNSU5QyVCRSVFNCVCOCU4MCVFNCVCRCU5MyVFNSU4QyU5NiVFNiU4OSU5OCVFNyVBRSVBMSVFNiU5QyU4RCVFNSU4QSVBMSVFOSVBMSVCOSVFNyU5QiVBRSVFNCVCQSU4QyVFNiVBQyVBMSVFNiU4QiU5QiVFNiVBMCU4NyVFNiU5NiU4NyVFNCVCQiVCNiVFRiVCQyU4OCVFNSU4RiU5MSVFNSU5NCVBRSVFNyU4OSU4OCVFRiVCQyU4OS5kb2N4P1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9SDNLMVAwMkZBQjJKRUZZSFRUUTAlMkYyMDI1MDgwNiUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNTA4MDZUMTAwNTU4WiZYLUFtei1FeHBpcmVzPTQzMTk5JlgtQW16LVNlY3VyaXR5LVRva2VuPWV5SmhiR2NpT2lKSVV6VXhNaUlzSW5SNWNDSTZJa3BYVkNKOS5leUpoWTJObGMzTkxaWGtpT2lKSU0wc3hVREF5UmtGQ01rcEZSbGxJVkZSUk1DSXNJbVY0Y0NJNk1UYzFORFV4TnpVMU5Dd2ljR0Z5Wlc1MElqb2ljR1Z1WjJOb1lXNW5JbjAuUGdjMXpiSEZlS3diMkw2cFZ5b2VfVVRlNXVmU3l5UjEySFNhZ1dSWkdfRVo3bFVfcEVTNExlWEZMVkFud0doZEw3NVpENjNmcGFCVl81VGcwcnJvVXcmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0JnZlcnNpb25JZD1udWxsJlgtQW16LVNpZ25hdHVyZT1mMzRmNGIzYmJhNjliMzNiZjExZjJmNmFiNWU2ZjYwMmVhNGNjZjc5NzFhOTY5MDkzYTM0MmEwZTQzMmE5MTE3 (URL: http://localhost:9090/api/v1/download-shared-object/aHR0cDovLzEyNy4wLjAuMTo5MDAwL3poYW9iaWFvd2VuamlhbmppYW5jaGF6aHVzaG91LyVFNSU4RCU5NyVFNSU4QyVCQSVFNiVCMSVBMSVFNiVCMCVCNCVFNSU4QyVCQiVFNSVCQSU5RiVFMyU4MCU4MSVFNyU5NCU5RiVFNiVCNCVCQiVFNSU5RSU4MyVFNSU5QyVCRSVFNCVCOCU4MCVFNCVCRCU5MyVFNSU4QyU5NiVFNiU4OSU5OCVFNyVBRSVBMSVFNiU5QyU4RCVFNSU4QSVBMSVFOSVBMSVCOSVFNyU5QiVBRSVFNCVCQSU4QyVFNiVBQyVBMSVFNiU4QiU5QiVFNiVBMCU4NyVFNiU5NiU4NyVFNCVCQiVCNiVFRiVCQyU4OCVFNSU4RiU5MSVFNSU5NCVBRSVFNyU4OSU4OCVFRiVCQyU4OS5kb2N4P1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9SDNLMVAwMkZBQjJKRUZZSFRUUTAlMkYyMDI1MDgwNiUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNTA4MDZUMTAwNTU4WiZYLUFtei1FeHBpcmVzPTQzMTk5JlgtQW16LVNlY3VyaXR5LVRva2VuPWV5SmhiR2NpT2lKSVV6VXhNaUlzSW5SNWNDSTZJa3BYVkNKOS5leUpoWTJObGMzTkxaWGtpT2lKSU0wc3hVREF5UmtGQ01rcEZSbGxJVkZSUk1DSXNJbVY0Y0NJNk1UYzFORFV4TnpVMU5Dd2ljR0Z5Wlc1MElqb2ljR1Z1WjJOb1lXNW5JbjAuUGdjMXpiSEZlS3diMkw2cFZ5b2VfVVRlNXVmU3l5UjEySFNhZ1dSWkdfRVo3bFVfcEVTNExlWEZMVkFud0doZEw3NVpENjNmcGFCVl81VGcwcnJvVXcmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0JnZlcnNpb25JZD1udWxsJlgtQW16LVNpZ25hdHVyZT1mMzRmNGIzYmJhNjliMzNiZjExZjJmNmFiNWU2ZjYwMmVhNGNjZjc5NzFhOTY5MDkzYTM0MmEwZTQzMmE5MTE3)
2025-08-07 17:05:18 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | file_processing | ID: a0f2b7e9-9d33-4bd0-9a13-e44c0e5266bf | 状态: 失败 | 耗时: 0.027秒
2025-08-07 17:05:18 | ERROR    | app.core.logger:wrapper:452 | 函数调用失败: process_file_stage | 耗时: 0.028秒 | 错误: 文件处理阶段失败: 文件下载失败: 403 Client Error: Forbidden for url: http://localhost:9090/api/v1/download-shared-object/aHR0cDovLzEyNy4wLjAuMTo5MDAwL3poYW9iaWFvd2VuamlhbmppYW5jaGF6aHVzaG91LyVFNSU4RCU5NyVFNSU4QyVCQSVFNiVCMSVBMSVFNiVCMCVCNCVFNSU4QyVCQiVFNSVCQSU5RiVFMyU4MCU4MSVFNyU5NCU5RiVFNiVCNCVCQiVFNSU5RSU4MyVFNSU5QyVCRSVFNCVCOCU4MCVFNCVCRCU5MyVFNSU4QyU5NiVFNiU4OSU5OCVFNyVBRSVBMSVFNiU5QyU4RCVFNSU4QSVBMSVFOSVBMSVCOSVFNyU5QiVBRSVFNCVCQSU4QyVFNiVBQyVBMSVFNiU4QiU5QiVFNiVBMCU4NyVFNiU5NiU4NyVFNCVCQiVCNiVFRiVCQyU4OCVFNSU4RiU5MSVFNSU5NCVBRSVFNyU4OSU4OCVFRiVCQyU4OS5kb2N4P1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9SDNLMVAwMkZBQjJKRUZZSFRUUTAlMkYyMDI1MDgwNiUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNTA4MDZUMTAwNTU4WiZYLUFtei1FeHBpcmVzPTQzMTk5JlgtQW16LVNlY3VyaXR5LVRva2VuPWV5SmhiR2NpT2lKSVV6VXhNaUlzSW5SNWNDSTZJa3BYVkNKOS5leUpoWTJObGMzTkxaWGtpT2lKSU0wc3hVREF5UmtGQ01rcEZSbGxJVkZSUk1DSXNJbVY0Y0NJNk1UYzFORFV4TnpVMU5Dd2ljR0Z5Wlc1MElqb2ljR1Z1WjJOb1lXNW5JbjAuUGdjMXpiSEZlS3diMkw2cFZ5b2VfVVRlNXVmU3l5UjEySFNhZ1dSWkdfRVo3bFVfcEVTNExlWEZMVkFud0doZEw3NVpENjNmcGFCVl81VGcwcnJvVXcmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0JnZlcnNpb25JZD1udWxsJlgtQW16LVNpZ25hdHVyZT1mMzRmNGIzYmJhNjliMzNiZjExZjJmNmFiNWU2ZjYwMmVhNGNjZjc5NzFhOTY5MDkzYTM0MmEwZTQzMmE5MTE3 (URL: http://localhost:9090/api/v1/download-shared-object/aHR0cDovLzEyNy4wLjAuMTo5MDAwL3poYW9iaWFvd2VuamlhbmppYW5jaGF6aHVzaG91LyVFNSU4RCU5NyVFNSU4QyVCQSVFNiVCMSVBMSVFNiVCMCVCNCVFNSU4QyVCQiVFNSVCQSU5RiVFMyU4MCU4MSVFNyU5NCU5RiVFNiVCNCVCQiVFNSU5RSU4MyVFNSU5QyVCRSVFNCVCOCU4MCVFNCVCRCU5MyVFNSU4QyU5NiVFNiU4OSU5OCVFNyVBRSVBMSVFNiU5QyU4RCVFNSU4QSVBMSVFOSVBMSVCOSVFNyU5QiVBRSVFNCVCQSU4QyVFNiVBQyVBMSVFNiU4QiU5QiVFNiVBMCU4NyVFNiU5NiU4NyVFNCVCQiVCNiVFRiVCQyU4OCVFNSU4RiU5MSVFNSU5NCVBRSVFNyU4OSU4OCVFRiVCQyU4OS5kb2N4P1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9SDNLMVAwMkZBQjJKRUZZSFRUUTAlMkYyMDI1MDgwNiUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNTA4MDZUMTAwNTU4WiZYLUFtei1FeHBpcmVzPTQzMTk5JlgtQW16LVNlY3VyaXR5LVRva2VuPWV5SmhiR2NpT2lKSVV6VXhNaUlzSW5SNWNDSTZJa3BYVkNKOS5leUpoWTJObGMzTkxaWGtpT2lKSU0wc3hVREF5UmtGQ01rcEZSbGxJVkZSUk1DSXNJbVY0Y0NJNk1UYzFORFV4TnpVMU5Dd2ljR0Z5Wlc1MElqb2ljR1Z1WjJOb1lXNW5JbjAuUGdjMXpiSEZlS3diMkw2cFZ5b2VfVVRlNXVmU3l5UjEySFNhZ1dSWkdfRVo3bFVfcEVTNExlWEZMVkFud0doZEw3NVpENjNmcGFCVl81VGcwcnJvVXcmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0JnZlcnNpb25JZD1udWxsJlgtQW16LVNpZ25hdHVyZT1mMzRmNGIzYmJhNjliMzNiZjExZjJmNmFiNWU2ZjYwMmVhNGNjZjc5NzFhOTY5MDkzYTM0MmEwZTQzMmE5MTE3)
2025-08-07 17:05:18 | ERROR    | app.services.compliance_service:execute_pipeline:409 | 步骤3: 文件处理失败，无法继续 | ID: a0f2b7e9-9d33-4bd0-9a13-e44c0e5266bf | 错误: 文件处理阶段失败: 文件下载失败: 403 Client Error: Forbidden for url: http://localhost:9090/api/v1/download-shared-object/aHR0cDovLzEyNy4wLjAuMTo5MDAwL3poYW9iaWFvd2VuamlhbmppYW5jaGF6aHVzaG91LyVFNSU4RCU5NyVFNSU4QyVCQSVFNiVCMSVBMSVFNiVCMCVCNCVFNSU4QyVCQiVFNSVCQSU5RiVFMyU4MCU4MSVFNyU5NCU5RiVFNiVCNCVCQiVFNSU5RSU4MyVFNSU5QyVCRSVFNCVCOCU4MCVFNCVCRCU5MyVFNSU4QyU5NiVFNiU4OSU5OCVFNyVBRSVBMSVFNiU5QyU4RCVFNSU4QSVBMSVFOSVBMSVCOSVFNyU5QiVBRSVFNCVCQSU4QyVFNiVBQyVBMSVFNiU4QiU5QiVFNiVBMCU4NyVFNiU5NiU4NyVFNCVCQiVCNiVFRiVCQyU4OCVFNSU4RiU5MSVFNSU5NCVBRSVFNyU4OSU4OCVFRiVCQyU4OS5kb2N4P1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9SDNLMVAwMkZBQjJKRUZZSFRUUTAlMkYyMDI1MDgwNiUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNTA4MDZUMTAwNTU4WiZYLUFtei1FeHBpcmVzPTQzMTk5JlgtQW16LVNlY3VyaXR5LVRva2VuPWV5SmhiR2NpT2lKSVV6VXhNaUlzSW5SNWNDSTZJa3BYVkNKOS5leUpoWTJObGMzTkxaWGtpT2lKSU0wc3hVREF5UmtGQ01rcEZSbGxJVkZSUk1DSXNJbVY0Y0NJNk1UYzFORFV4TnpVMU5Dd2ljR0Z5Wlc1MElqb2ljR1Z1WjJOb1lXNW5JbjAuUGdjMXpiSEZlS3diMkw2cFZ5b2VfVVRlNXVmU3l5UjEySFNhZ1dSWkdfRVo3bFVfcEVTNExlWEZMVkFud0doZEw3NVpENjNmcGFCVl81VGcwcnJvVXcmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0JnZlcnNpb25JZD1udWxsJlgtQW16LVNpZ25hdHVyZT1mMzRmNGIzYmJhNjliMzNiZjExZjJmNmFiNWU2ZjYwMmVhNGNjZjc5NzFhOTY5MDkzYTM0MmEwZTQzMmE5MTE3 (URL: http://localhost:9090/api/v1/download-shared-object/aHR0cDovLzEyNy4wLjAuMTo5MDAwL3poYW9iaWFvd2VuamlhbmppYW5jaGF6aHVzaG91LyVFNSU4RCU5NyVFNSU4QyVCQSVFNiVCMSVBMSVFNiVCMCVCNCVFNSU4QyVCQiVFNSVCQSU5RiVFMyU4MCU4MSVFNyU5NCU5RiVFNiVCNCVCQiVFNSU5RSU4MyVFNSU5QyVCRSVFNCVCOCU4MCVFNCVCRCU5MyVFNSU4QyU5NiVFNiU4OSU5OCVFNyVBRSVBMSVFNiU5QyU4RCVFNSU4QSVBMSVFOSVBMSVCOSVFNyU5QiVBRSVFNCVCQSU4QyVFNiVBQyVBMSVFNiU4QiU5QiVFNiVBMCU4NyVFNiU5NiU4NyVFNCVCQiVCNiVFRiVCQyU4OCVFNSU4RiU5MSVFNSU5NCVBRSVFNyU4OSU4OCVFRiVCQyU4OS5kb2N4P1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9SDNLMVAwMkZBQjJKRUZZSFRUUTAlMkYyMDI1MDgwNiUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNTA4MDZUMTAwNTU4WiZYLUFtei1FeHBpcmVzPTQzMTk5JlgtQW16LVNlY3VyaXR5LVRva2VuPWV5SmhiR2NpT2lKSVV6VXhNaUlzSW5SNWNDSTZJa3BYVkNKOS5leUpoWTJObGMzTkxaWGtpT2lKSU0wc3hVREF5UmtGQ01rcEZSbGxJVkZSUk1DSXNJbVY0Y0NJNk1UYzFORFV4TnpVMU5Dd2ljR0Z5Wlc1MElqb2ljR1Z1WjJOb1lXNW5JbjAuUGdjMXpiSEZlS3diMkw2cFZ5b2VfVVRlNXVmU3l5UjEySFNhZ1dSWkdfRVo3bFVfcEVTNExlWEZMVkFud0doZEw3NVpENjNmcGFCVl81VGcwcnJvVXcmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0JnZlcnNpb25JZD1udWxsJlgtQW16LVNpZ25hdHVyZT1mMzRmNGIzYmJhNjliMzNiZjExZjJmNmFiNWU2ZjYwMmVhNGNjZjc5NzFhOTY5MDkzYTM0MmEwZTQzMmE5MTE3)
2025-08-07 17:05:18 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 完整合规性检查流水线 | ID: a0f2b7e9-9d33-4bd0-9a13-e44c0e5266bf | 状态: 失败 | 耗时: 0.044秒
2025-08-07 17:05:18 | ERROR    | app.core.logger:__exit__:421 | 操作 完整合规性检查流水线 发生异常: 文件处理阶段失败: 文件下载失败: 403 Client Error: Forbidden for url: http://localhost:9090/api/v1/download-shared-object/aHR0cDovLzEyNy4wLjAuMTo5MDAwL3poYW9iaWFvd2VuamlhbmppYW5jaGF6aHVzaG91LyVFNSU4RCU5NyVFNSU4QyVCQSVFNiVCMSVBMSVFNiVCMCVCNCVFNSU4QyVCQiVFNSVCQSU5RiVFMyU4MCU4MSVFNyU5NCU5RiVFNiVCNCVCQiVFNSU5RSU4MyVFNSU5QyVCRSVFNCVCOCU4MCVFNCVCRCU5MyVFNSU4QyU5NiVFNiU4OSU5OCVFNyVBRSVBMSVFNiU5QyU4RCVFNSU4QSVBMSVFOSVBMSVCOSVFNyU5QiVBRSVFNCVCQSU4QyVFNiVBQyVBMSVFNiU4QiU5QiVFNiVBMCU4NyVFNiU5NiU4NyVFNCVCQiVCNiVFRiVCQyU4OCVFNSU4RiU5MSVFNSU5NCVBRSVFNyU4OSU4OCVFRiVCQyU4OS5kb2N4P1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9SDNLMVAwMkZBQjJKRUZZSFRUUTAlMkYyMDI1MDgwNiUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNTA4MDZUMTAwNTU4WiZYLUFtei1FeHBpcmVzPTQzMTk5JlgtQW16LVNlY3VyaXR5LVRva2VuPWV5SmhiR2NpT2lKSVV6VXhNaUlzSW5SNWNDSTZJa3BYVkNKOS5leUpoWTJObGMzTkxaWGtpT2lKSU0wc3hVREF5UmtGQ01rcEZSbGxJVkZSUk1DSXNJbVY0Y0NJNk1UYzFORFV4TnpVMU5Dd2ljR0Z5Wlc1MElqb2ljR1Z1WjJOb1lXNW5JbjAuUGdjMXpiSEZlS3diMkw2cFZ5b2VfVVRlNXVmU3l5UjEySFNhZ1dSWkdfRVo3bFVfcEVTNExlWEZMVkFud0doZEw3NVpENjNmcGFCVl81VGcwcnJvVXcmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0JnZlcnNpb25JZD1udWxsJlgtQW16LVNpZ25hdHVyZT1mMzRmNGIzYmJhNjliMzNiZjExZjJmNmFiNWU2ZjYwMmVhNGNjZjc5NzFhOTY5MDkzYTM0MmEwZTQzMmE5MTE3 (URL: http://localhost:9090/api/v1/download-shared-object/aHR0cDovLzEyNy4wLjAuMTo5MDAwL3poYW9iaWFvd2VuamlhbmppYW5jaGF6aHVzaG91LyVFNSU4RCU5NyVFNSU4QyVCQSVFNiVCMSVBMSVFNiVCMCVCNCVFNSU4QyVCQiVFNSVCQSU5RiVFMyU4MCU4MSVFNyU5NCU5RiVFNiVCNCVCQiVFNSU5RSU4MyVFNSU5QyVCRSVFNCVCOCU4MCVFNCVCRCU5MyVFNSU4QyU5NiVFNiU4OSU5OCVFNyVBRSVBMSVFNiU5QyU4RCVFNSU4QSVBMSVFOSVBMSVCOSVFNyU5QiVBRSVFNCVCQSU4QyVFNiVBQyVBMSVFNiU4QiU5QiVFNiVBMCU4NyVFNiU5NiU4NyVFNCVCQiVCNiVFRiVCQyU4OCVFNSU4RiU5MSVFNSU5NCVBRSVFNyU4OSU4OCVFRiVCQyU4OS5kb2N4P1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9SDNLMVAwMkZBQjJKRUZZSFRUUTAlMkYyMDI1MDgwNiUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNTA4MDZUMTAwNTU4WiZYLUFtei1FeHBpcmVzPTQzMTk5JlgtQW16LVNlY3VyaXR5LVRva2VuPWV5SmhiR2NpT2lKSVV6VXhNaUlzSW5SNWNDSTZJa3BYVkNKOS5leUpoWTJObGMzTkxaWGtpT2lKSU0wc3hVREF5UmtGQ01rcEZSbGxJVkZSUk1DSXNJbVY0Y0NJNk1UYzFORFV4TnpVMU5Dd2ljR0Z5Wlc1MElqb2ljR1Z1WjJOb1lXNW5JbjAuUGdjMXpiSEZlS3diMkw2cFZ5b2VfVVRlNXVmU3l5UjEySFNhZ1dSWkdfRVo3bFVfcEVTNExlWEZMVkFud0doZEw3NVpENjNmcGFCVl81VGcwcnJvVXcmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0JnZlcnNpb25JZD1udWxsJlgtQW16LVNpZ25hdHVyZT1mMzRmNGIzYmJhNjliMzNiZjExZjJmNmFiNWU2ZjYwMmVhNGNjZjc5NzFhOTY5MDkzYTM0MmEwZTQzMmE5MTE3)
2025-08-07 17:05:18 | ERROR    | app.services.compliance_service:execute_pipeline:505 | 合规性检查流水线失败 | ID: a0f2b7e9-9d33-4bd0-9a13-e44c0e5266bf | 耗时: 0.045秒 | 错误: 文件处理阶段失败: 文件下载失败: 403 Client Error: Forbidden for url: http://localhost:9090/api/v1/download-shared-object/aHR0cDovLzEyNy4wLjAuMTo5MDAwL3poYW9iaWFvd2VuamlhbmppYW5jaGF6aHVzaG91LyVFNSU4RCU5NyVFNSU4QyVCQSVFNiVCMSVBMSVFNiVCMCVCNCVFNSU4QyVCQiVFNSVCQSU5RiVFMyU4MCU4MSVFNyU5NCU5RiVFNiVCNCVCQiVFNSU5RSU4MyVFNSU5QyVCRSVFNCVCOCU4MCVFNCVCRCU5MyVFNSU4QyU5NiVFNiU4OSU5OCVFNyVBRSVBMSVFNiU5QyU4RCVFNSU4QSVBMSVFOSVBMSVCOSVFNyU5QiVBRSVFNCVCQSU4QyVFNiVBQyVBMSVFNiU4QiU5QiVFNiVBMCU4NyVFNiU5NiU4NyVFNCVCQiVCNiVFRiVCQyU4OCVFNSU4RiU5MSVFNSU5NCVBRSVFNyU4OSU4OCVFRiVCQyU4OS5kb2N4P1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9SDNLMVAwMkZBQjJKRUZZSFRUUTAlMkYyMDI1MDgwNiUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNTA4MDZUMTAwNTU4WiZYLUFtei1FeHBpcmVzPTQzMTk5JlgtQW16LVNlY3VyaXR5LVRva2VuPWV5SmhiR2NpT2lKSVV6VXhNaUlzSW5SNWNDSTZJa3BYVkNKOS5leUpoWTJObGMzTkxaWGtpT2lKSU0wc3hVREF5UmtGQ01rcEZSbGxJVkZSUk1DSXNJbVY0Y0NJNk1UYzFORFV4TnpVMU5Dd2ljR0Z5Wlc1MElqb2ljR1Z1WjJOb1lXNW5JbjAuUGdjMXpiSEZlS3diMkw2cFZ5b2VfVVRlNXVmU3l5UjEySFNhZ1dSWkdfRVo3bFVfcEVTNExlWEZMVkFud0doZEw3NVpENjNmcGFCVl81VGcwcnJvVXcmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0JnZlcnNpb25JZD1udWxsJlgtQW16LVNpZ25hdHVyZT1mMzRmNGIzYmJhNjliMzNiZjExZjJmNmFiNWU2ZjYwMmVhNGNjZjc5NzFhOTY5MDkzYTM0MmEwZTQzMmE5MTE3 (URL: http://localhost:9090/api/v1/download-shared-object/aHR0cDovLzEyNy4wLjAuMTo5MDAwL3poYW9iaWFvd2VuamlhbmppYW5jaGF6aHVzaG91LyVFNSU4RCU5NyVFNSU4QyVCQSVFNiVCMSVBMSVFNiVCMCVCNCVFNSU4QyVCQiVFNSVCQSU5RiVFMyU4MCU4MSVFNyU5NCU5RiVFNiVCNCVCQiVFNSU5RSU4MyVFNSU5QyVCRSVFNCVCOCU4MCVFNCVCRCU5MyVFNSU4QyU5NiVFNiU4OSU5OCVFNyVBRSVBMSVFNiU5QyU4RCVFNSU4QSVBMSVFOSVBMSVCOSVFNyU5QiVBRSVFNCVCQSU4QyVFNiVBQyVBMSVFNiU4QiU5QiVFNiVBMCU4NyVFNiU5NiU4NyVFNCVCQiVCNiVFRiVCQyU4OCVFNSU4RiU5MSVFNSU5NCVBRSVFNyU4OSU4OCVFRiVCQyU4OS5kb2N4P1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9SDNLMVAwMkZBQjJKRUZZSFRUUTAlMkYyMDI1MDgwNiUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNTA4MDZUMTAwNTU4WiZYLUFtei1FeHBpcmVzPTQzMTk5JlgtQW16LVNlY3VyaXR5LVRva2VuPWV5SmhiR2NpT2lKSVV6VXhNaUlzSW5SNWNDSTZJa3BYVkNKOS5leUpoWTJObGMzTkxaWGtpT2lKSU0wc3hVREF5UmtGQ01rcEZSbGxJVkZSUk1DSXNJbVY0Y0NJNk1UYzFORFV4TnpVMU5Dd2ljR0Z5Wlc1MElqb2ljR1Z1WjJOb1lXNW5JbjAuUGdjMXpiSEZlS3diMkw2cFZ5b2VfVVRlNXVmU3l5UjEySFNhZ1dSWkdfRVo3bFVfcEVTNExlWEZMVkFud0doZEw3NVpENjNmcGFCVl81VGcwcnJvVXcmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0JnZlcnNpb25JZD1udWxsJlgtQW16LVNpZ25hdHVyZT1mMzRmNGIzYmJhNjliMzNiZjExZjJmNmFiNWU2ZjYwMmVhNGNjZjc5NzFhOTY5MDkzYTM0MmEwZTQzMmE5MTE3)
2025-08-07 17:05:18 | ERROR    | app.core.logger:wrapper:452 | 函数调用失败: execute_pipeline | 耗时: 0.046秒 | 错误: 文件处理阶段失败: 文件下载失败: 403 Client Error: Forbidden for url: http://localhost:9090/api/v1/download-shared-object/aHR0cDovLzEyNy4wLjAuMTo5MDAwL3poYW9iaWFvd2VuamlhbmppYW5jaGF6aHVzaG91LyVFNSU4RCU5NyVFNSU4QyVCQSVFNiVCMSVBMSVFNiVCMCVCNCVFNSU4QyVCQiVFNSVCQSU5RiVFMyU4MCU4MSVFNyU5NCU5RiVFNiVCNCVCQiVFNSU5RSU4MyVFNSU5QyVCRSVFNCVCOCU4MCVFNCVCRCU5MyVFNSU4QyU5NiVFNiU4OSU5OCVFNyVBRSVBMSVFNiU5QyU4RCVFNSU4QSVBMSVFOSVBMSVCOSVFNyU5QiVBRSVFNCVCQSU4QyVFNiVBQyVBMSVFNiU4QiU5QiVFNiVBMCU4NyVFNiU5NiU4NyVFNCVCQiVCNiVFRiVCQyU4OCVFNSU4RiU5MSVFNSU5NCVBRSVFNyU4OSU4OCVFRiVCQyU4OS5kb2N4P1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9SDNLMVAwMkZBQjJKRUZZSFRUUTAlMkYyMDI1MDgwNiUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNTA4MDZUMTAwNTU4WiZYLUFtei1FeHBpcmVzPTQzMTk5JlgtQW16LVNlY3VyaXR5LVRva2VuPWV5SmhiR2NpT2lKSVV6VXhNaUlzSW5SNWNDSTZJa3BYVkNKOS5leUpoWTJObGMzTkxaWGtpT2lKSU0wc3hVREF5UmtGQ01rcEZSbGxJVkZSUk1DSXNJbVY0Y0NJNk1UYzFORFV4TnpVMU5Dd2ljR0Z5Wlc1MElqb2ljR1Z1WjJOb1lXNW5JbjAuUGdjMXpiSEZlS3diMkw2cFZ5b2VfVVRlNXVmU3l5UjEySFNhZ1dSWkdfRVo3bFVfcEVTNExlWEZMVkFud0doZEw3NVpENjNmcGFCVl81VGcwcnJvVXcmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0JnZlcnNpb25JZD1udWxsJlgtQW16LVNpZ25hdHVyZT1mMzRmNGIzYmJhNjliMzNiZjExZjJmNmFiNWU2ZjYwMmVhNGNjZjc5NzFhOTY5MDkzYTM0MmEwZTQzMmE5MTE3 (URL: http://localhost:9090/api/v1/download-shared-object/aHR0cDovLzEyNy4wLjAuMTo5MDAwL3poYW9iaWFvd2VuamlhbmppYW5jaGF6aHVzaG91LyVFNSU4RCU5NyVFNSU4QyVCQSVFNiVCMSVBMSVFNiVCMCVCNCVFNSU4QyVCQiVFNSVCQSU5RiVFMyU4MCU4MSVFNyU5NCU5RiVFNiVCNCVCQiVFNSU5RSU4MyVFNSU5QyVCRSVFNCVCOCU4MCVFNCVCRCU5MyVFNSU4QyU5NiVFNiU4OSU5OCVFNyVBRSVBMSVFNiU5QyU4RCVFNSU4QSVBMSVFOSVBMSVCOSVFNyU5QiVBRSVFNCVCQSU4QyVFNiVBQyVBMSVFNiU4QiU5QiVFNiVBMCU4NyVFNiU5NiU4NyVFNCVCQiVCNiVFRiVCQyU4OCVFNSU4RiU5MSVFNSU5NCVBRSVFNyU4OSU4OCVFRiVCQyU4OS5kb2N4P1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9SDNLMVAwMkZBQjJKRUZZSFRUUTAlMkYyMDI1MDgwNiUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNTA4MDZUMTAwNTU4WiZYLUFtei1FeHBpcmVzPTQzMTk5JlgtQW16LVNlY3VyaXR5LVRva2VuPWV5SmhiR2NpT2lKSVV6VXhNaUlzSW5SNWNDSTZJa3BYVkNKOS5leUpoWTJObGMzTkxaWGtpT2lKSU0wc3hVREF5UmtGQ01rcEZSbGxJVkZSUk1DSXNJbVY0Y0NJNk1UYzFORFV4TnpVMU5Dd2ljR0Z5Wlc1MElqb2ljR1Z1WjJOb1lXNW5JbjAuUGdjMXpiSEZlS3diMkw2cFZ5b2VfVVRlNXVmU3l5UjEySFNhZ1dSWkdfRVo3bFVfcEVTNExlWEZMVkFud0doZEw3NVpENjNmcGFCVl81VGcwcnJvVXcmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0JnZlcnNpb25JZD1udWxsJlgtQW16LVNpZ25hdHVyZT1mMzRmNGIzYmJhNjliMzNiZjExZjJmNmFiNWU2ZjYwMmVhNGNjZjc5NzFhOTY5MDkzYTM0MmEwZTQzMmE5MTE3)
2025-08-07 17:05:18 | ERROR    | app.services.compliance_service:check_compliance:645 | 合规性检查服务失败 | ID: a0f2b7e9-9d33-4bd0-9a13-e44c0e5266bf | 错误: 文件处理阶段失败: 文件下载失败: 403 Client Error: Forbidden for url: http://localhost:9090/api/v1/download-shared-object/aHR0cDovLzEyNy4wLjAuMTo5MDAwL3poYW9iaWFvd2VuamlhbmppYW5jaGF6aHVzaG91LyVFNSU4RCU5NyVFNSU4QyVCQSVFNiVCMSVBMSVFNiVCMCVCNCVFNSU4QyVCQiVFNSVCQSU5RiVFMyU4MCU4MSVFNyU5NCU5RiVFNiVCNCVCQiVFNSU5RSU4MyVFNSU5QyVCRSVFNCVCOCU4MCVFNCVCRCU5MyVFNSU4QyU5NiVFNiU4OSU5OCVFNyVBRSVBMSVFNiU5QyU4RCVFNSU4QSVBMSVFOSVBMSVCOSVFNyU5QiVBRSVFNCVCQSU4QyVFNiVBQyVBMSVFNiU4QiU5QiVFNiVBMCU4NyVFNiU5NiU4NyVFNCVCQiVCNiVFRiVCQyU4OCVFNSU4RiU5MSVFNSU5NCVBRSVFNyU4OSU4OCVFRiVCQyU4OS5kb2N4P1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9SDNLMVAwMkZBQjJKRUZZSFRUUTAlMkYyMDI1MDgwNiUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNTA4MDZUMTAwNTU4WiZYLUFtei1FeHBpcmVzPTQzMTk5JlgtQW16LVNlY3VyaXR5LVRva2VuPWV5SmhiR2NpT2lKSVV6VXhNaUlzSW5SNWNDSTZJa3BYVkNKOS5leUpoWTJObGMzTkxaWGtpT2lKSU0wc3hVREF5UmtGQ01rcEZSbGxJVkZSUk1DSXNJbVY0Y0NJNk1UYzFORFV4TnpVMU5Dd2ljR0Z5Wlc1MElqb2ljR1Z1WjJOb1lXNW5JbjAuUGdjMXpiSEZlS3diMkw2cFZ5b2VfVVRlNXVmU3l5UjEySFNhZ1dSWkdfRVo3bFVfcEVTNExlWEZMVkFud0doZEw3NVpENjNmcGFCVl81VGcwcnJvVXcmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0JnZlcnNpb25JZD1udWxsJlgtQW16LVNpZ25hdHVyZT1mMzRmNGIzYmJhNjliMzNiZjExZjJmNmFiNWU2ZjYwMmVhNGNjZjc5NzFhOTY5MDkzYTM0MmEwZTQzMmE5MTE3 (URL: http://localhost:9090/api/v1/download-shared-object/aHR0cDovLzEyNy4wLjAuMTo5MDAwL3poYW9iaWFvd2VuamlhbmppYW5jaGF6aHVzaG91LyVFNSU4RCU5NyVFNSU4QyVCQSVFNiVCMSVBMSVFNiVCMCVCNCVFNSU4QyVCQiVFNSVCQSU5RiVFMyU4MCU4MSVFNyU5NCU5RiVFNiVCNCVCQiVFNSU5RSU4MyVFNSU5QyVCRSVFNCVCOCU4MCVFNCVCRCU5MyVFNSU4QyU5NiVFNiU4OSU5OCVFNyVBRSVBMSVFNiU5QyU4RCVFNSU4QSVBMSVFOSVBMSVCOSVFNyU5QiVBRSVFNCVCQSU4QyVFNiVBQyVBMSVFNiU4QiU5QiVFNiVBMCU4NyVFNiU5NiU4NyVFNCVCQiVCNiVFRiVCQyU4OCVFNSU4RiU5MSVFNSU5NCVBRSVFNyU4OSU4OCVFRiVCQyU4OS5kb2N4P1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9SDNLMVAwMkZBQjJKRUZZSFRUUTAlMkYyMDI1MDgwNiUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNTA4MDZUMTAwNTU4WiZYLUFtei1FeHBpcmVzPTQzMTk5JlgtQW16LVNlY3VyaXR5LVRva2VuPWV5SmhiR2NpT2lKSVV6VXhNaUlzSW5SNWNDSTZJa3BYVkNKOS5leUpoWTJObGMzTkxaWGtpT2lKSU0wc3hVREF5UmtGQ01rcEZSbGxJVkZSUk1DSXNJbVY0Y0NJNk1UYzFORFV4TnpVMU5Dd2ljR0Z5Wlc1MElqb2ljR1Z1WjJOb1lXNW5JbjAuUGdjMXpiSEZlS3diMkw2cFZ5b2VfVVRlNXVmU3l5UjEySFNhZ1dSWkdfRVo3bFVfcEVTNExlWEZMVkFud0doZEw3NVpENjNmcGFCVl81VGcwcnJvVXcmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0JnZlcnNpb25JZD1udWxsJlgtQW16LVNpZ25hdHVyZT1mMzRmNGIzYmJhNjliMzNiZjExZjJmNmFiNWU2ZjYwMmVhNGNjZjc5NzFhOTY5MDkzYTM0MmEwZTQzMmE5MTE3)
2025-08-07 17:05:18 | ERROR    | app.core.logger:wrapper:452 | 函数调用失败: check_compliance | 耗时: 0.049秒 | 错误: 合规性检查失败: 文件处理阶段失败: 文件下载失败: 403 Client Error: Forbidden for url: http://localhost:9090/api/v1/download-shared-object/aHR0cDovLzEyNy4wLjAuMTo5MDAwL3poYW9iaWFvd2VuamlhbmppYW5jaGF6aHVzaG91LyVFNSU4RCU5NyVFNSU4QyVCQSVFNiVCMSVBMSVFNiVCMCVCNCVFNSU4QyVCQiVFNSVCQSU5RiVFMyU4MCU4MSVFNyU5NCU5RiVFNiVCNCVCQiVFNSU5RSU4MyVFNSU5QyVCRSVFNCVCOCU4MCVFNCVCRCU5MyVFNSU4QyU5NiVFNiU4OSU5OCVFNyVBRSVBMSVFNiU5QyU4RCVFNSU4QSVBMSVFOSVBMSVCOSVFNyU5QiVBRSVFNCVCQSU4QyVFNiVBQyVBMSVFNiU4QiU5QiVFNiVBMCU4NyVFNiU5NiU4NyVFNCVCQiVCNiVFRiVCQyU4OCVFNSU4RiU5MSVFNSU5NCVBRSVFNyU4OSU4OCVFRiVCQyU4OS5kb2N4P1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9SDNLMVAwMkZBQjJKRUZZSFRUUTAlMkYyMDI1MDgwNiUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNTA4MDZUMTAwNTU4WiZYLUFtei1FeHBpcmVzPTQzMTk5JlgtQW16LVNlY3VyaXR5LVRva2VuPWV5SmhiR2NpT2lKSVV6VXhNaUlzSW5SNWNDSTZJa3BYVkNKOS5leUpoWTJObGMzTkxaWGtpT2lKSU0wc3hVREF5UmtGQ01rcEZSbGxJVkZSUk1DSXNJbVY0Y0NJNk1UYzFORFV4TnpVMU5Dd2ljR0Z5Wlc1MElqb2ljR1Z1WjJOb1lXNW5JbjAuUGdjMXpiSEZlS3diMkw2cFZ5b2VfVVRlNXVmU3l5UjEySFNhZ1dSWkdfRVo3bFVfcEVTNExlWEZMVkFud0doZEw3NVpENjNmcGFCVl81VGcwcnJvVXcmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0JnZlcnNpb25JZD1udWxsJlgtQW16LVNpZ25hdHVyZT1mMzRmNGIzYmJhNjliMzNiZjExZjJmNmFiNWU2ZjYwMmVhNGNjZjc5NzFhOTY5MDkzYTM0MmEwZTQzMmE5MTE3 (URL: http://localhost:9090/api/v1/download-shared-object/aHR0cDovLzEyNy4wLjAuMTo5MDAwL3poYW9iaWFvd2VuamlhbmppYW5jaGF6aHVzaG91LyVFNSU4RCU5NyVFNSU4QyVCQSVFNiVCMSVBMSVFNiVCMCVCNCVFNSU4QyVCQiVFNSVCQSU5RiVFMyU4MCU4MSVFNyU5NCU5RiVFNiVCNCVCQiVFNSU5RSU4MyVFNSU5QyVCRSVFNCVCOCU4MCVFNCVCRCU5MyVFNSU4QyU5NiVFNiU4OSU5OCVFNyVBRSVBMSVFNiU5QyU4RCVFNSU4QSVBMSVFOSVBMSVCOSVFNyU5QiVBRSVFNCVCQSU4QyVFNiVBQyVBMSVFNiU4QiU5QiVFNiVBMCU4NyVFNiU5NiU4NyVFNCVCQiVCNiVFRiVCQyU4OCVFNSU4RiU5MSVFNSU5NCVBRSVFNyU4OSU4OCVFRiVCQyU4OS5kb2N4P1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9SDNLMVAwMkZBQjJKRUZZSFRUUTAlMkYyMDI1MDgwNiUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNTA4MDZUMTAwNTU4WiZYLUFtei1FeHBpcmVzPTQzMTk5JlgtQW16LVNlY3VyaXR5LVRva2VuPWV5SmhiR2NpT2lKSVV6VXhNaUlzSW5SNWNDSTZJa3BYVkNKOS5leUpoWTJObGMzTkxaWGtpT2lKSU0wc3hVREF5UmtGQ01rcEZSbGxJVkZSUk1DSXNJbVY0Y0NJNk1UYzFORFV4TnpVMU5Dd2ljR0Z5Wlc1MElqb2ljR1Z1WjJOb1lXNW5JbjAuUGdjMXpiSEZlS3diMkw2cFZ5b2VfVVRlNXVmU3l5UjEySFNhZ1dSWkdfRVo3bFVfcEVTNExlWEZMVkFud0doZEw3NVpENjNmcGFCVl81VGcwcnJvVXcmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0JnZlcnNpb25JZD1udWxsJlgtQW16LVNpZ25hdHVyZT1mMzRmNGIzYmJhNjliMzNiZjExZjJmNmFiNWU2ZjYwMmVhNGNjZjc5NzFhOTY5MDkzYTM0MmEwZTQzMmE5MTE3)
2025-08-07 17:05:18 | ERROR    | app.api.routes:check_compliance:102 | 合规性检查失败 | ID: a0f2b7e9-9d33-4bd0-9a13-e44c0e5266bf | 错误: 合规性检查失败: 文件处理阶段失败: 文件下载失败: 403 Client Error: Forbidden for url: http://localhost:9090/api/v1/download-shared-object/aHR0cDovLzEyNy4wLjAuMTo5MDAwL3poYW9iaWFvd2VuamlhbmppYW5jaGF6aHVzaG91LyVFNSU4RCU5NyVFNSU4QyVCQSVFNiVCMSVBMSVFNiVCMCVCNCVFNSU4QyVCQiVFNSVCQSU5RiVFMyU4MCU4MSVFNyU5NCU5RiVFNiVCNCVCQiVFNSU5RSU4MyVFNSU5QyVCRSVFNCVCOCU4MCVFNCVCRCU5MyVFNSU4QyU5NiVFNiU4OSU5OCVFNyVBRSVBMSVFNiU5QyU4RCVFNSU4QSVBMSVFOSVBMSVCOSVFNyU5QiVBRSVFNCVCQSU4QyVFNiVBQyVBMSVFNiU4QiU5QiVFNiVBMCU4NyVFNiU5NiU4NyVFNCVCQiVCNiVFRiVCQyU4OCVFNSU4RiU5MSVFNSU5NCVBRSVFNyU4OSU4OCVFRiVCQyU4OS5kb2N4P1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9SDNLMVAwMkZBQjJKRUZZSFRUUTAlMkYyMDI1MDgwNiUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNTA4MDZUMTAwNTU4WiZYLUFtei1FeHBpcmVzPTQzMTk5JlgtQW16LVNlY3VyaXR5LVRva2VuPWV5SmhiR2NpT2lKSVV6VXhNaUlzSW5SNWNDSTZJa3BYVkNKOS5leUpoWTJObGMzTkxaWGtpT2lKSU0wc3hVREF5UmtGQ01rcEZSbGxJVkZSUk1DSXNJbVY0Y0NJNk1UYzFORFV4TnpVMU5Dd2ljR0Z5Wlc1MElqb2ljR1Z1WjJOb1lXNW5JbjAuUGdjMXpiSEZlS3diMkw2cFZ5b2VfVVRlNXVmU3l5UjEySFNhZ1dSWkdfRVo3bFVfcEVTNExlWEZMVkFud0doZEw3NVpENjNmcGFCVl81VGcwcnJvVXcmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0JnZlcnNpb25JZD1udWxsJlgtQW16LVNpZ25hdHVyZT1mMzRmNGIzYmJhNjliMzNiZjExZjJmNmFiNWU2ZjYwMmVhNGNjZjc5NzFhOTY5MDkzYTM0MmEwZTQzMmE5MTE3 (URL: http://localhost:9090/api/v1/download-shared-object/aHR0cDovLzEyNy4wLjAuMTo5MDAwL3poYW9iaWFvd2VuamlhbmppYW5jaGF6aHVzaG91LyVFNSU4RCU5NyVFNSU4QyVCQSVFNiVCMSVBMSVFNiVCMCVCNCVFNSU4QyVCQiVFNSVCQSU5RiVFMyU4MCU4MSVFNyU5NCU5RiVFNiVCNCVCQiVFNSU5RSU4MyVFNSU5QyVCRSVFNCVCOCU4MCVFNCVCRCU5MyVFNSU4QyU5NiVFNiU4OSU5OCVFNyVBRSVBMSVFNiU5QyU4RCVFNSU4QSVBMSVFOSVBMSVCOSVFNyU5QiVBRSVFNCVCQSU4QyVFNiVBQyVBMSVFNiU4QiU5QiVFNiVBMCU4NyVFNiU5NiU4NyVFNCVCQiVCNiVFRiVCQyU4OCVFNSU4RiU5MSVFNSU5NCVBRSVFNyU4OSU4OCVFRiVCQyU4OS5kb2N4P1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9SDNLMVAwMkZBQjJKRUZZSFRUUTAlMkYyMDI1MDgwNiUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNTA4MDZUMTAwNTU4WiZYLUFtei1FeHBpcmVzPTQzMTk5JlgtQW16LVNlY3VyaXR5LVRva2VuPWV5SmhiR2NpT2lKSVV6VXhNaUlzSW5SNWNDSTZJa3BYVkNKOS5leUpoWTJObGMzTkxaWGtpT2lKSU0wc3hVREF5UmtGQ01rcEZSbGxJVkZSUk1DSXNJbVY0Y0NJNk1UYzFORFV4TnpVMU5Dd2ljR0Z5Wlc1MElqb2ljR1Z1WjJOb1lXNW5JbjAuUGdjMXpiSEZlS3diMkw2cFZ5b2VfVVRlNXVmU3l5UjEySFNhZ1dSWkdfRVo3bFVfcEVTNExlWEZMVkFud0doZEw3NVpENjNmcGFCVl81VGcwcnJvVXcmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0JnZlcnNpb25JZD1udWxsJlgtQW16LVNpZ25hdHVyZT1mMzRmNGIzYmJhNjliMzNiZjExZjJmNmFiNWU2ZjYwMmVhNGNjZjc5NzFhOTY5MDkzYTM0MmEwZTQzMmE5MTE3)
2025-08-07 17:05:18 | ERROR    | app.middleware.validation:dispatch:55 | 请求验证异常 | ID: a0f2b7e9-9d33-4bd0-9a13-e44c0e5266bf | 错误: 合规性检查失败: 文件处理阶段失败: 文件下载失败: 403 Client Error: Forbidden for url: http://localhost:9090/api/v1/download-shared-object/aHR0cDovLzEyNy4wLjAuMTo5MDAwL3poYW9iaWFvd2VuamlhbmppYW5jaGF6aHVzaG91LyVFNSU4RCU5NyVFNSU4QyVCQSVFNiVCMSVBMSVFNiVCMCVCNCVFNSU4QyVCQiVFNSVCQSU5RiVFMyU4MCU4MSVFNyU5NCU5RiVFNiVCNCVCQiVFNSU5RSU4MyVFNSU5QyVCRSVFNCVCOCU4MCVFNCVCRCU5MyVFNSU4QyU5NiVFNiU4OSU5OCVFNyVBRSVBMSVFNiU5QyU4RCVFNSU4QSVBMSVFOSVBMSVCOSVFNyU5QiVBRSVFNCVCQSU4QyVFNiVBQyVBMSVFNiU4QiU5QiVFNiVBMCU4NyVFNiU5NiU4NyVFNCVCQiVCNiVFRiVCQyU4OCVFNSU4RiU5MSVFNSU5NCVBRSVFNyU4OSU4OCVFRiVCQyU4OS5kb2N4P1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9SDNLMVAwMkZBQjJKRUZZSFRUUTAlMkYyMDI1MDgwNiUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNTA4MDZUMTAwNTU4WiZYLUFtei1FeHBpcmVzPTQzMTk5JlgtQW16LVNlY3VyaXR5LVRva2VuPWV5SmhiR2NpT2lKSVV6VXhNaUlzSW5SNWNDSTZJa3BYVkNKOS5leUpoWTJObGMzTkxaWGtpT2lKSU0wc3hVREF5UmtGQ01rcEZSbGxJVkZSUk1DSXNJbVY0Y0NJNk1UYzFORFV4TnpVMU5Dd2ljR0Z5Wlc1MElqb2ljR1Z1WjJOb1lXNW5JbjAuUGdjMXpiSEZlS3diMkw2cFZ5b2VfVVRlNXVmU3l5UjEySFNhZ1dSWkdfRVo3bFVfcEVTNExlWEZMVkFud0doZEw3NVpENjNmcGFCVl81VGcwcnJvVXcmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0JnZlcnNpb25JZD1udWxsJlgtQW16LVNpZ25hdHVyZT1mMzRmNGIzYmJhNjliMzNiZjExZjJmNmFiNWU2ZjYwMmVhNGNjZjc5NzFhOTY5MDkzYTM0MmEwZTQzMmE5MTE3 (URL: http://localhost:9090/api/v1/download-shared-object/aHR0cDovLzEyNy4wLjAuMTo5MDAwL3poYW9iaWFvd2VuamlhbmppYW5jaGF6aHVzaG91LyVFNSU4RCU5NyVFNSU4QyVCQSVFNiVCMSVBMSVFNiVCMCVCNCVFNSU4QyVCQiVFNSVCQSU5RiVFMyU4MCU4MSVFNyU5NCU5RiVFNiVCNCVCQiVFNSU5RSU4MyVFNSU5QyVCRSVFNCVCOCU4MCVFNCVCRCU5MyVFNSU4QyU5NiVFNiU4OSU5OCVFNyVBRSVBMSVFNiU5QyU4RCVFNSU4QSVBMSVFOSVBMSVCOSVFNyU5QiVBRSVFNCVCQSU4QyVFNiVBQyVBMSVFNiU4QiU5QiVFNiVBMCU4NyVFNiU5NiU4NyVFNCVCQiVCNiVFRiVCQyU4OCVFNSU4RiU5MSVFNSU5NCVBRSVFNyU4OSU4OCVFRiVCQyU4OS5kb2N4P1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9SDNLMVAwMkZBQjJKRUZZSFRUUTAlMkYyMDI1MDgwNiUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNTA4MDZUMTAwNTU4WiZYLUFtei1FeHBpcmVzPTQzMTk5JlgtQW16LVNlY3VyaXR5LVRva2VuPWV5SmhiR2NpT2lKSVV6VXhNaUlzSW5SNWNDSTZJa3BYVkNKOS5leUpoWTJObGMzTkxaWGtpT2lKSU0wc3hVREF5UmtGQ01rcEZSbGxJVkZSUk1DSXNJbVY0Y0NJNk1UYzFORFV4TnpVMU5Dd2ljR0Z5Wlc1MElqb2ljR1Z1WjJOb1lXNW5JbjAuUGdjMXpiSEZlS3diMkw2cFZ5b2VfVVRlNXVmU3l5UjEySFNhZ1dSWkdfRVo3bFVfcEVTNExlWEZMVkFud0doZEw3NVpENjNmcGFCVl81VGcwcnJvVXcmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0JnZlcnNpb25JZD1udWxsJlgtQW16LVNpZ25hdHVyZT1mMzRmNGIzYmJhNjliMzNiZjExZjJmNmFiNWU2ZjYwMmVhNGNjZjc5NzFhOTY5MDkzYTM0MmEwZTQzMmE5MTE3)
2025-08-07 17:05:18 | INFO     | app.middleware.logging:dispatch:48 | 请求完成 | ID: a0f2b7e9-9d33-4bd0-9a13-e44c0e5266bf | 状态码: 400 | 耗时: 0.084秒
2025-08-07 19:18:17 | INFO     | app.middleware.logging:dispatch:35 | 请求开始 | ID: 3c650d57-4044-47e8-9eb0-c402ca737a3c | 方法: POST | URL: http://localhost:8088/api/v1/check-compliance | 客户端IP: 127.0.0.1 | User-Agent: Apifox/1.0.0 (https://apifox.com)
2025-08-07 19:18:17 | INFO     | app.middleware.validation:_validate_request:75 | 跳过中间件验证 | ID: 3c650d57-4044-47e8-9eb0-c402ca737a3c | 路径: /api/v1/check-compliance
2025-08-07 19:18:17 | INFO     | app.api.routes:check_compliance:78 | 开始合规性检查 | ID: 3c650d57-4044-47e8-9eb0-c402ca737a3c | 文件: 南区污水医废、生活垃圾一体化托管服务项目二次招标文件（发售版）.docx
2025-08-07 19:18:17 | INFO     | app.services.compliance_service:check_compliance:636 | 合规性检查服务开始 | ID: 3c650d57-4044-47e8-9eb0-c402ca737a3c
2025-08-07 19:18:17 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 完整合规性检查流水线 | ID: 3c650d57-4044-47e8-9eb0-c402ca737a3c | 详情: {}
2025-08-07 19:18:17 | INFO     | app.services.compliance_service:execute_pipeline:376 | 开始执行合规性检查流水线 | ID: 3c650d57-4044-47e8-9eb0-c402ca737a3c | 文件: 南区污水医废、生活垃圾一体化托管服务项目二次招标文件（发售版）.docx
2025-08-07 19:18:17 | INFO     | app.services.compliance_service:execute_pipeline:386 | 步骤1: 开始验证服务前置条件 | ID: 3c650d57-4044-47e8-9eb0-c402ca737a3c
2025-08-07 19:18:17 | INFO     | app.services.compliance_service:validate_prerequisites:73 | 开始验证服务前置条件 | ID: 3c650d57-4044-47e8-9eb0-c402ca737a3c
2025-08-07 19:18:17 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 敏感词服务健康检查 | ID: 3c650d57-4044-47e8-9eb0-c402ca737a3c | 详情: {}
2025-08-07 19:18:17 | INFO     | app.services.sensitive_word_service:check_health:121 | 检查敏感词服务健康状态: http://*************:8087/health
2025-08-07 19:18:17 | INFO     | app.services.sensitive_word_service:check_health:131 | 敏感词服务健康状态: 健康
2025-08-07 19:18:17 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 敏感词服务健康检查 | ID: 3c650d57-4044-47e8-9eb0-c402ca737a3c | 状态: 成功 | 耗时: 0.029秒
2025-08-07 19:18:17 | INFO     | app.services.compliance_service:validate_prerequisites:92 | 服务健康检查完成 | ID: 3c650d57-4044-47e8-9eb0-c402ca737a3c | 状态: {'file_processor': True, 'ai_model': True, 'sensitive_word': True, 'result_processor': True}
2025-08-07 19:18:17 | INFO     | app.services.compliance_service:execute_pipeline:388 | 步骤1: 服务前置条件验证完成 | ID: 3c650d57-4044-47e8-9eb0-c402ca737a3c | 状态: {'file_processor': True, 'ai_model': True, 'sensitive_word': True, 'result_processor': True}
2025-08-07 19:18:17 | INFO     | app.services.compliance_service:execute_pipeline:394 | 步骤2: 开始获取项目信息 | ID: 3c650d57-4044-47e8-9eb0-c402ca737a3c
2025-08-07 19:18:17 | INFO     | app.services.compliance_service:execute_pipeline:396 | 步骤2: 项目信息获取完成 | ID: 3c650d57-4044-47e8-9eb0-c402ca737a3c
2025-08-07 19:18:17 | INFO     | app.services.compliance_service:execute_pipeline:400 | 步骤3: 开始文件处理阶段 | ID: 3c650d57-4044-47e8-9eb0-c402ca737a3c
2025-08-07 19:18:17 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 文件处理阶段 | ID: 3c650d57-4044-47e8-9eb0-c402ca737a3c | 详情: {}
2025-08-07 19:18:17 | INFO     | app.services.compliance_service:process_file_stage:114 | 开始文件处理阶段 | ID: 3c650d57-4044-47e8-9eb0-c402ca737a3c | 文件: 南区污水医废、生活垃圾一体化托管服务项目二次招标文件（发售版）.docx
2025-08-07 19:18:17 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 优化文件处理流程 | ID: 3c650d57-4044-47e8-9eb0-c402ca737a3c | 详情: {}
2025-08-07 19:18:17 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 文件下载 | ID: 3c650d57-4044-47e8-9eb0-c402ca737a3c | 详情: {}
2025-08-07 19:18:17 | INFO     | app.services.file_processor_v2:download_file:255 | 开始下载文件: http://localhost:9090/api/v1/download-shared-object/aHR0cDovLzEyNy4wLjAuMTo5MDAwL3poYW9iaWFvd2VuamlhbmppYW5jaGF6aHVzaG91LyVFNSU4RCU5NyVFNSU4QyVCQSVFNiVCMSVBMSVFNiVCMCVCNCVFNSU4QyVCQiVFNSVCQSU5RiVFMyU4MCU4MSVFNyU5NCU5RiVFNiVCNCVCQiVFNSU5RSU4MyVFNSU5QyVCRSVFNCVCOCU4MCVFNCVCRCU5MyVFNSU4QyU5NiVFNiU4OSU5OCVFNyVBRSVBMSVFNiU5QyU4RCVFNSU4QSVBMSVFOSVBMSVCOSVFNyU5QiVBRSVFNCVCQSU4QyVFNiVBQyVBMSVFNiU4QiU5QiVFNiVBMCU4NyVFNiU5NiU4NyVFNCVCQiVCNiVFRiVCQyU4OCVFNSU4RiU5MSVFNSU5NCVBRSVFNyU4OSU4OCVFRiVCQyU4OS5kb2N4P1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9SDNLMVAwMkZBQjJKRUZZSFRUUTAlMkYyMDI1MDgwNiUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNTA4MDZUMTA0MDEwWiZYLUFtei1FeHBpcmVzPTQzMTk5JlgtQW16LVNlY3VyaXR5LVRva2VuPWV5SmhiR2NpT2lKSVV6VXhNaUlzSW5SNWNDSTZJa3BYVkNKOS5leUpoWTJObGMzTkxaWGtpT2lKSU0wc3hVREF5UmtGQ01rcEZSbGxJVkZSUk1DSXNJbVY0Y0NJNk1UYzFORFV4TnpVMU5Dd2ljR0Z5Wlc1MElqb2ljR1Z1WjJOb1lXNW5JbjAuUGdjMXpiSEZlS3diMkw2cFZ5b2VfVVRlNXVmU3l5UjEySFNhZ1dSWkdfRVo3bFVfcEVTNExlWEZMVkFud0doZEw3NVpENjNmcGFCVl81VGcwcnJvVXcmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0JnZlcnNpb25JZD1udWxsJlgtQW16LVNpZ25hdHVyZT0wNTZjNmIwNDU5ZGZiMDYzMmNkN2QxMThlOGFlNjk5MDVjM2E4M2U4ZDYxMjU5ZGY3MWJjOTI2MzA2MjMyYTAy
2025-08-07 19:18:17 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 文件下载 | ID: 3c650d57-4044-47e8-9eb0-c402ca737a3c | 状态: 失败 | 耗时: 0.019秒
2025-08-07 19:18:17 | ERROR    | app.core.logger:__exit__:421 | 操作 文件下载 发生异常: 403 Client Error: Forbidden for url: http://localhost:9090/api/v1/download-shared-object/aHR0cDovLzEyNy4wLjAuMTo5MDAwL3poYW9iaWFvd2VuamlhbmppYW5jaGF6aHVzaG91LyVFNSU4RCU5NyVFNSU4QyVCQSVFNiVCMSVBMSVFNiVCMCVCNCVFNSU4QyVCQiVFNSVCQSU5RiVFMyU4MCU4MSVFNyU5NCU5RiVFNiVCNCVCQiVFNSU5RSU4MyVFNSU5QyVCRSVFNCVCOCU4MCVFNCVCRCU5MyVFNSU4QyU5NiVFNiU4OSU5OCVFNyVBRSVBMSVFNiU5QyU4RCVFNSU4QSVBMSVFOSVBMSVCOSVFNyU5QiVBRSVFNCVCQSU4QyVFNiVBQyVBMSVFNiU4QiU5QiVFNiVBMCU4NyVFNiU5NiU4NyVFNCVCQiVCNiVFRiVCQyU4OCVFNSU4RiU5MSVFNSU5NCVBRSVFNyU4OSU4OCVFRiVCQyU4OS5kb2N4P1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9SDNLMVAwMkZBQjJKRUZZSFRUUTAlMkYyMDI1MDgwNiUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNTA4MDZUMTA0MDEwWiZYLUFtei1FeHBpcmVzPTQzMTk5JlgtQW16LVNlY3VyaXR5LVRva2VuPWV5SmhiR2NpT2lKSVV6VXhNaUlzSW5SNWNDSTZJa3BYVkNKOS5leUpoWTJObGMzTkxaWGtpT2lKSU0wc3hVREF5UmtGQ01rcEZSbGxJVkZSUk1DSXNJbVY0Y0NJNk1UYzFORFV4TnpVMU5Dd2ljR0Z5Wlc1MElqb2ljR1Z1WjJOb1lXNW5JbjAuUGdjMXpiSEZlS3diMkw2cFZ5b2VfVVRlNXVmU3l5UjEySFNhZ1dSWkdfRVo3bFVfcEVTNExlWEZMVkFud0doZEw3NVpENjNmcGFCVl81VGcwcnJvVXcmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0JnZlcnNpb25JZD1udWxsJlgtQW16LVNpZ25hdHVyZT0wNTZjNmIwNDU5ZGZiMDYzMmNkN2QxMThlOGFlNjk5MDVjM2E4M2U4ZDYxMjU5ZGY3MWJjOTI2MzA2MjMyYTAy
2025-08-07 19:18:17 | ERROR    | app.core.logger:wrapper:452 | 函数调用失败: download_file | 耗时: 0.019秒 | 错误: 文件下载失败: 403 Client Error: Forbidden for url: http://localhost:9090/api/v1/download-shared-object/aHR0cDovLzEyNy4wLjAuMTo5MDAwL3poYW9iaWFvd2VuamlhbmppYW5jaGF6aHVzaG91LyVFNSU4RCU5NyVFNSU4QyVCQSVFNiVCMSVBMSVFNiVCMCVCNCVFNSU4QyVCQiVFNSVCQSU5RiVFMyU4MCU4MSVFNyU5NCU5RiVFNiVCNCVCQiVFNSU5RSU4MyVFNSU5QyVCRSVFNCVCOCU4MCVFNCVCRCU5MyVFNSU4QyU5NiVFNiU4OSU5OCVFNyVBRSVBMSVFNiU5QyU4RCVFNSU4QSVBMSVFOSVBMSVCOSVFNyU5QiVBRSVFNCVCQSU4QyVFNiVBQyVBMSVFNiU4QiU5QiVFNiVBMCU4NyVFNiU5NiU4NyVFNCVCQiVCNiVFRiVCQyU4OCVFNSU4RiU5MSVFNSU5NCVBRSVFNyU4OSU4OCVFRiVCQyU4OS5kb2N4P1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9SDNLMVAwMkZBQjJKRUZZSFRUUTAlMkYyMDI1MDgwNiUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNTA4MDZUMTA0MDEwWiZYLUFtei1FeHBpcmVzPTQzMTk5JlgtQW16LVNlY3VyaXR5LVRva2VuPWV5SmhiR2NpT2lKSVV6VXhNaUlzSW5SNWNDSTZJa3BYVkNKOS5leUpoWTJObGMzTkxaWGtpT2lKSU0wc3hVREF5UmtGQ01rcEZSbGxJVkZSUk1DSXNJbVY0Y0NJNk1UYzFORFV4TnpVMU5Dd2ljR0Z5Wlc1MElqb2ljR1Z1WjJOb1lXNW5JbjAuUGdjMXpiSEZlS3diMkw2cFZ5b2VfVVRlNXVmU3l5UjEySFNhZ1dSWkdfRVo3bFVfcEVTNExlWEZMVkFud0doZEw3NVpENjNmcGFCVl81VGcwcnJvVXcmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0JnZlcnNpb25JZD1udWxsJlgtQW16LVNpZ25hdHVyZT0wNTZjNmIwNDU5ZGZiMDYzMmNkN2QxMThlOGFlNjk5MDVjM2E4M2U4ZDYxMjU5ZGY3MWJjOTI2MzA2MjMyYTAy (URL: http://localhost:9090/api/v1/download-shared-object/aHR0cDovLzEyNy4wLjAuMTo5MDAwL3poYW9iaWFvd2VuamlhbmppYW5jaGF6aHVzaG91LyVFNSU4RCU5NyVFNSU4QyVCQSVFNiVCMSVBMSVFNiVCMCVCNCVFNSU4QyVCQiVFNSVCQSU5RiVFMyU4MCU4MSVFNyU5NCU5RiVFNiVCNCVCQiVFNSU5RSU4MyVFNSU5QyVCRSVFNCVCOCU4MCVFNCVCRCU5MyVFNSU4QyU5NiVFNiU4OSU5OCVFNyVBRSVBMSVFNiU5QyU4RCVFNSU4QSVBMSVFOSVBMSVCOSVFNyU5QiVBRSVFNCVCQSU4QyVFNiVBQyVBMSVFNiU4QiU5QiVFNiVBMCU4NyVFNiU5NiU4NyVFNCVCQiVCNiVFRiVCQyU4OCVFNSU4RiU5MSVFNSU5NCVBRSVFNyU4OSU4OCVFRiVCQyU4OS5kb2N4P1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9SDNLMVAwMkZBQjJKRUZZSFRUUTAlMkYyMDI1MDgwNiUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNTA4MDZUMTA0MDEwWiZYLUFtei1FeHBpcmVzPTQzMTk5JlgtQW16LVNlY3VyaXR5LVRva2VuPWV5SmhiR2NpT2lKSVV6VXhNaUlzSW5SNWNDSTZJa3BYVkNKOS5leUpoWTJObGMzTkxaWGtpT2lKSU0wc3hVREF5UmtGQ01rcEZSbGxJVkZSUk1DSXNJbVY0Y0NJNk1UYzFORFV4TnpVMU5Dd2ljR0Z5Wlc1MElqb2ljR1Z1WjJOb1lXNW5JbjAuUGdjMXpiSEZlS3diMkw2cFZ5b2VfVVRlNXVmU3l5UjEySFNhZ1dSWkdfRVo3bFVfcEVTNExlWEZMVkFud0doZEw3NVpENjNmcGFCVl81VGcwcnJvVXcmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0JnZlcnNpb25JZD1udWxsJlgtQW16LVNpZ25hdHVyZT0wNTZjNmIwNDU5ZGZiMDYzMmNkN2QxMThlOGFlNjk5MDVjM2E4M2U4ZDYxMjU5ZGY3MWJjOTI2MzA2MjMyYTAy)
2025-08-07 19:18:17 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 优化文件处理流程 | ID: 3c650d57-4044-47e8-9eb0-c402ca737a3c | 状态: 失败 | 耗时: 0.019秒
2025-08-07 19:18:17 | ERROR    | app.core.logger:__exit__:421 | 操作 优化文件处理流程 发生异常: 文件下载失败: 403 Client Error: Forbidden for url: http://localhost:9090/api/v1/download-shared-object/aHR0cDovLzEyNy4wLjAuMTo5MDAwL3poYW9iaWFvd2VuamlhbmppYW5jaGF6aHVzaG91LyVFNSU4RCU5NyVFNSU4QyVCQSVFNiVCMSVBMSVFNiVCMCVCNCVFNSU4QyVCQiVFNSVCQSU5RiVFMyU4MCU4MSVFNyU5NCU5RiVFNiVCNCVCQiVFNSU5RSU4MyVFNSU5QyVCRSVFNCVCOCU4MCVFNCVCRCU5MyVFNSU4QyU5NiVFNiU4OSU5OCVFNyVBRSVBMSVFNiU5QyU4RCVFNSU4QSVBMSVFOSVBMSVCOSVFNyU5QiVBRSVFNCVCQSU4QyVFNiVBQyVBMSVFNiU4QiU5QiVFNiVBMCU4NyVFNiU5NiU4NyVFNCVCQiVCNiVFRiVCQyU4OCVFNSU4RiU5MSVFNSU5NCVBRSVFNyU4OSU4OCVFRiVCQyU4OS5kb2N4P1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9SDNLMVAwMkZBQjJKRUZZSFRUUTAlMkYyMDI1MDgwNiUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNTA4MDZUMTA0MDEwWiZYLUFtei1FeHBpcmVzPTQzMTk5JlgtQW16LVNlY3VyaXR5LVRva2VuPWV5SmhiR2NpT2lKSVV6VXhNaUlzSW5SNWNDSTZJa3BYVkNKOS5leUpoWTJObGMzTkxaWGtpT2lKSU0wc3hVREF5UmtGQ01rcEZSbGxJVkZSUk1DSXNJbVY0Y0NJNk1UYzFORFV4TnpVMU5Dd2ljR0Z5Wlc1MElqb2ljR1Z1WjJOb1lXNW5JbjAuUGdjMXpiSEZlS3diMkw2cFZ5b2VfVVRlNXVmU3l5UjEySFNhZ1dSWkdfRVo3bFVfcEVTNExlWEZMVkFud0doZEw3NVpENjNmcGFCVl81VGcwcnJvVXcmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0JnZlcnNpb25JZD1udWxsJlgtQW16LVNpZ25hdHVyZT0wNTZjNmIwNDU5ZGZiMDYzMmNkN2QxMThlOGFlNjk5MDVjM2E4M2U4ZDYxMjU5ZGY3MWJjOTI2MzA2MjMyYTAy (URL: http://localhost:9090/api/v1/download-shared-object/aHR0cDovLzEyNy4wLjAuMTo5MDAwL3poYW9iaWFvd2VuamlhbmppYW5jaGF6aHVzaG91LyVFNSU4RCU5NyVFNSU4QyVCQSVFNiVCMSVBMSVFNiVCMCVCNCVFNSU4QyVCQiVFNSVCQSU5RiVFMyU4MCU4MSVFNyU5NCU5RiVFNiVCNCVCQiVFNSU5RSU4MyVFNSU5QyVCRSVFNCVCOCU4MCVFNCVCRCU5MyVFNSU4QyU5NiVFNiU4OSU5OCVFNyVBRSVBMSVFNiU5QyU4RCVFNSU4QSVBMSVFOSVBMSVCOSVFNyU5QiVBRSVFNCVCQSU4QyVFNiVBQyVBMSVFNiU4QiU5QiVFNiVBMCU4NyVFNiU5NiU4NyVFNCVCQiVCNiVFRiVCQyU4OCVFNSU4RiU5MSVFNSU5NCVBRSVFNyU4OSU4OCVFRiVCQyU4OS5kb2N4P1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9SDNLMVAwMkZBQjJKRUZZSFRUUTAlMkYyMDI1MDgwNiUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNTA4MDZUMTA0MDEwWiZYLUFtei1FeHBpcmVzPTQzMTk5JlgtQW16LVNlY3VyaXR5LVRva2VuPWV5SmhiR2NpT2lKSVV6VXhNaUlzSW5SNWNDSTZJa3BYVkNKOS5leUpoWTJObGMzTkxaWGtpT2lKSU0wc3hVREF5UmtGQ01rcEZSbGxJVkZSUk1DSXNJbVY0Y0NJNk1UYzFORFV4TnpVMU5Dd2ljR0Z5Wlc1MElqb2ljR1Z1WjJOb1lXNW5JbjAuUGdjMXpiSEZlS3diMkw2cFZ5b2VfVVRlNXVmU3l5UjEySFNhZ1dSWkdfRVo3bFVfcEVTNExlWEZMVkFud0doZEw3NVpENjNmcGFCVl81VGcwcnJvVXcmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0JnZlcnNpb25JZD1udWxsJlgtQW16LVNpZ25hdHVyZT0wNTZjNmIwNDU5ZGZiMDYzMmNkN2QxMThlOGFlNjk5MDVjM2E4M2U4ZDYxMjU5ZGY3MWJjOTI2MzA2MjMyYTAy)
2025-08-07 19:18:17 | ERROR    | app.core.logger:wrapper:452 | 函数调用失败: process_file | 耗时: 0.019秒 | 错误: 文件下载失败: 403 Client Error: Forbidden for url: http://localhost:9090/api/v1/download-shared-object/aHR0cDovLzEyNy4wLjAuMTo5MDAwL3poYW9iaWFvd2VuamlhbmppYW5jaGF6aHVzaG91LyVFNSU4RCU5NyVFNSU4QyVCQSVFNiVCMSVBMSVFNiVCMCVCNCVFNSU4QyVCQiVFNSVCQSU5RiVFMyU4MCU4MSVFNyU5NCU5RiVFNiVCNCVCQiVFNSU5RSU4MyVFNSU5QyVCRSVFNCVCOCU4MCVFNCVCRCU5MyVFNSU4QyU5NiVFNiU4OSU5OCVFNyVBRSVBMSVFNiU5QyU4RCVFNSU4QSVBMSVFOSVBMSVCOSVFNyU5QiVBRSVFNCVCQSU4QyVFNiVBQyVBMSVFNiU4QiU5QiVFNiVBMCU4NyVFNiU5NiU4NyVFNCVCQiVCNiVFRiVCQyU4OCVFNSU4RiU5MSVFNSU5NCVBRSVFNyU4OSU4OCVFRiVCQyU4OS5kb2N4P1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9SDNLMVAwMkZBQjJKRUZZSFRUUTAlMkYyMDI1MDgwNiUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNTA4MDZUMTA0MDEwWiZYLUFtei1FeHBpcmVzPTQzMTk5JlgtQW16LVNlY3VyaXR5LVRva2VuPWV5SmhiR2NpT2lKSVV6VXhNaUlzSW5SNWNDSTZJa3BYVkNKOS5leUpoWTJObGMzTkxaWGtpT2lKSU0wc3hVREF5UmtGQ01rcEZSbGxJVkZSUk1DSXNJbVY0Y0NJNk1UYzFORFV4TnpVMU5Dd2ljR0Z5Wlc1MElqb2ljR1Z1WjJOb1lXNW5JbjAuUGdjMXpiSEZlS3diMkw2cFZ5b2VfVVRlNXVmU3l5UjEySFNhZ1dSWkdfRVo3bFVfcEVTNExlWEZMVkFud0doZEw3NVpENjNmcGFCVl81VGcwcnJvVXcmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0JnZlcnNpb25JZD1udWxsJlgtQW16LVNpZ25hdHVyZT0wNTZjNmIwNDU5ZGZiMDYzMmNkN2QxMThlOGFlNjk5MDVjM2E4M2U4ZDYxMjU5ZGY3MWJjOTI2MzA2MjMyYTAy (URL: http://localhost:9090/api/v1/download-shared-object/aHR0cDovLzEyNy4wLjAuMTo5MDAwL3poYW9iaWFvd2VuamlhbmppYW5jaGF6aHVzaG91LyVFNSU4RCU5NyVFNSU4QyVCQSVFNiVCMSVBMSVFNiVCMCVCNCVFNSU4QyVCQiVFNSVCQSU5RiVFMyU4MCU4MSVFNyU5NCU5RiVFNiVCNCVCQiVFNSU5RSU4MyVFNSU5QyVCRSVFNCVCOCU4MCVFNCVCRCU5MyVFNSU4QyU5NiVFNiU4OSU5OCVFNyVBRSVBMSVFNiU5QyU4RCVFNSU4QSVBMSVFOSVBMSVCOSVFNyU5QiVBRSVFNCVCQSU4QyVFNiVBQyVBMSVFNiU4QiU5QiVFNiVBMCU4NyVFNiU5NiU4NyVFNCVCQiVCNiVFRiVCQyU4OCVFNSU4RiU5MSVFNSU5NCVBRSVFNyU4OSU4OCVFRiVCQyU4OS5kb2N4P1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9SDNLMVAwMkZBQjJKRUZZSFRUUTAlMkYyMDI1MDgwNiUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNTA4MDZUMTA0MDEwWiZYLUFtei1FeHBpcmVzPTQzMTk5JlgtQW16LVNlY3VyaXR5LVRva2VuPWV5SmhiR2NpT2lKSVV6VXhNaUlzSW5SNWNDSTZJa3BYVkNKOS5leUpoWTJObGMzTkxaWGtpT2lKSU0wc3hVREF5UmtGQ01rcEZSbGxJVkZSUk1DSXNJbVY0Y0NJNk1UYzFORFV4TnpVMU5Dd2ljR0Z5Wlc1MElqb2ljR1Z1WjJOb1lXNW5JbjAuUGdjMXpiSEZlS3diMkw2cFZ5b2VfVVRlNXVmU3l5UjEySFNhZ1dSWkdfRVo3bFVfcEVTNExlWEZMVkFud0doZEw3NVpENjNmcGFCVl81VGcwcnJvVXcmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0JnZlcnNpb25JZD1udWxsJlgtQW16LVNpZ25hdHVyZT0wNTZjNmIwNDU5ZGZiMDYzMmNkN2QxMThlOGFlNjk5MDVjM2E4M2U4ZDYxMjU5ZGY3MWJjOTI2MzA2MjMyYTAy)
2025-08-07 19:18:17 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 文件处理阶段 | ID: 3c650d57-4044-47e8-9eb0-c402ca737a3c | 状态: 失败 | 耗时: 0.025秒
2025-08-07 19:18:17 | ERROR    | app.core.logger:__exit__:421 | 操作 文件处理阶段 发生异常: 文件下载失败: 403 Client Error: Forbidden for url: http://localhost:9090/api/v1/download-shared-object/aHR0cDovLzEyNy4wLjAuMTo5MDAwL3poYW9iaWFvd2VuamlhbmppYW5jaGF6aHVzaG91LyVFNSU4RCU5NyVFNSU4QyVCQSVFNiVCMSVBMSVFNiVCMCVCNCVFNSU4QyVCQiVFNSVCQSU5RiVFMyU4MCU4MSVFNyU5NCU5RiVFNiVCNCVCQiVFNSU5RSU4MyVFNSU5QyVCRSVFNCVCOCU4MCVFNCVCRCU5MyVFNSU4QyU5NiVFNiU4OSU5OCVFNyVBRSVBMSVFNiU5QyU4RCVFNSU4QSVBMSVFOSVBMSVCOSVFNyU5QiVBRSVFNCVCQSU4QyVFNiVBQyVBMSVFNiU4QiU5QiVFNiVBMCU4NyVFNiU5NiU4NyVFNCVCQiVCNiVFRiVCQyU4OCVFNSU4RiU5MSVFNSU5NCVBRSVFNyU4OSU4OCVFRiVCQyU4OS5kb2N4P1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9SDNLMVAwMkZBQjJKRUZZSFRUUTAlMkYyMDI1MDgwNiUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNTA4MDZUMTA0MDEwWiZYLUFtei1FeHBpcmVzPTQzMTk5JlgtQW16LVNlY3VyaXR5LVRva2VuPWV5SmhiR2NpT2lKSVV6VXhNaUlzSW5SNWNDSTZJa3BYVkNKOS5leUpoWTJObGMzTkxaWGtpT2lKSU0wc3hVREF5UmtGQ01rcEZSbGxJVkZSUk1DSXNJbVY0Y0NJNk1UYzFORFV4TnpVMU5Dd2ljR0Z5Wlc1MElqb2ljR1Z1WjJOb1lXNW5JbjAuUGdjMXpiSEZlS3diMkw2cFZ5b2VfVVRlNXVmU3l5UjEySFNhZ1dSWkdfRVo3bFVfcEVTNExlWEZMVkFud0doZEw3NVpENjNmcGFCVl81VGcwcnJvVXcmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0JnZlcnNpb25JZD1udWxsJlgtQW16LVNpZ25hdHVyZT0wNTZjNmIwNDU5ZGZiMDYzMmNkN2QxMThlOGFlNjk5MDVjM2E4M2U4ZDYxMjU5ZGY3MWJjOTI2MzA2MjMyYTAy (URL: http://localhost:9090/api/v1/download-shared-object/aHR0cDovLzEyNy4wLjAuMTo5MDAwL3poYW9iaWFvd2VuamlhbmppYW5jaGF6aHVzaG91LyVFNSU4RCU5NyVFNSU4QyVCQSVFNiVCMSVBMSVFNiVCMCVCNCVFNSU4QyVCQiVFNSVCQSU5RiVFMyU4MCU4MSVFNyU5NCU5RiVFNiVCNCVCQiVFNSU5RSU4MyVFNSU5QyVCRSVFNCVCOCU4MCVFNCVCRCU5MyVFNSU4QyU5NiVFNiU4OSU5OCVFNyVBRSVBMSVFNiU5QyU4RCVFNSU4QSVBMSVFOSVBMSVCOSVFNyU5QiVBRSVFNCVCQSU4QyVFNiVBQyVBMSVFNiU4QiU5QiVFNiVBMCU4NyVFNiU5NiU4NyVFNCVCQiVCNiVFRiVCQyU4OCVFNSU4RiU5MSVFNSU5NCVBRSVFNyU4OSU4OCVFRiVCQyU4OS5kb2N4P1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9SDNLMVAwMkZBQjJKRUZZSFRUUTAlMkYyMDI1MDgwNiUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNTA4MDZUMTA0MDEwWiZYLUFtei1FeHBpcmVzPTQzMTk5JlgtQW16LVNlY3VyaXR5LVRva2VuPWV5SmhiR2NpT2lKSVV6VXhNaUlzSW5SNWNDSTZJa3BYVkNKOS5leUpoWTJObGMzTkxaWGtpT2lKSU0wc3hVREF5UmtGQ01rcEZSbGxJVkZSUk1DSXNJbVY0Y0NJNk1UYzFORFV4TnpVMU5Dd2ljR0Z5Wlc1MElqb2ljR1Z1WjJOb1lXNW5JbjAuUGdjMXpiSEZlS3diMkw2cFZ5b2VfVVRlNXVmU3l5UjEySFNhZ1dSWkdfRVo3bFVfcEVTNExlWEZMVkFud0doZEw3NVpENjNmcGFCVl81VGcwcnJvVXcmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0JnZlcnNpb25JZD1udWxsJlgtQW16LVNpZ25hdHVyZT0wNTZjNmIwNDU5ZGZiMDYzMmNkN2QxMThlOGFlNjk5MDVjM2E4M2U4ZDYxMjU5ZGY3MWJjOTI2MzA2MjMyYTAy)
2025-08-07 19:18:17 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | file_processing | ID: 3c650d57-4044-47e8-9eb0-c402ca737a3c | 状态: 失败 | 耗时: 0.026秒
2025-08-07 19:18:17 | ERROR    | app.core.logger:wrapper:452 | 函数调用失败: process_file_stage | 耗时: 0.026秒 | 错误: 文件处理阶段失败: 文件下载失败: 403 Client Error: Forbidden for url: http://localhost:9090/api/v1/download-shared-object/aHR0cDovLzEyNy4wLjAuMTo5MDAwL3poYW9iaWFvd2VuamlhbmppYW5jaGF6aHVzaG91LyVFNSU4RCU5NyVFNSU4QyVCQSVFNiVCMSVBMSVFNiVCMCVCNCVFNSU4QyVCQiVFNSVCQSU5RiVFMyU4MCU4MSVFNyU5NCU5RiVFNiVCNCVCQiVFNSU5RSU4MyVFNSU5QyVCRSVFNCVCOCU4MCVFNCVCRCU5MyVFNSU4QyU5NiVFNiU4OSU5OCVFNyVBRSVBMSVFNiU5QyU4RCVFNSU4QSVBMSVFOSVBMSVCOSVFNyU5QiVBRSVFNCVCQSU4QyVFNiVBQyVBMSVFNiU4QiU5QiVFNiVBMCU4NyVFNiU5NiU4NyVFNCVCQiVCNiVFRiVCQyU4OCVFNSU4RiU5MSVFNSU5NCVBRSVFNyU4OSU4OCVFRiVCQyU4OS5kb2N4P1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9SDNLMVAwMkZBQjJKRUZZSFRUUTAlMkYyMDI1MDgwNiUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNTA4MDZUMTA0MDEwWiZYLUFtei1FeHBpcmVzPTQzMTk5JlgtQW16LVNlY3VyaXR5LVRva2VuPWV5SmhiR2NpT2lKSVV6VXhNaUlzSW5SNWNDSTZJa3BYVkNKOS5leUpoWTJObGMzTkxaWGtpT2lKSU0wc3hVREF5UmtGQ01rcEZSbGxJVkZSUk1DSXNJbVY0Y0NJNk1UYzFORFV4TnpVMU5Dd2ljR0Z5Wlc1MElqb2ljR1Z1WjJOb1lXNW5JbjAuUGdjMXpiSEZlS3diMkw2cFZ5b2VfVVRlNXVmU3l5UjEySFNhZ1dSWkdfRVo3bFVfcEVTNExlWEZMVkFud0doZEw3NVpENjNmcGFCVl81VGcwcnJvVXcmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0JnZlcnNpb25JZD1udWxsJlgtQW16LVNpZ25hdHVyZT0wNTZjNmIwNDU5ZGZiMDYzMmNkN2QxMThlOGFlNjk5MDVjM2E4M2U4ZDYxMjU5ZGY3MWJjOTI2MzA2MjMyYTAy (URL: http://localhost:9090/api/v1/download-shared-object/aHR0cDovLzEyNy4wLjAuMTo5MDAwL3poYW9iaWFvd2VuamlhbmppYW5jaGF6aHVzaG91LyVFNSU4RCU5NyVFNSU4QyVCQSVFNiVCMSVBMSVFNiVCMCVCNCVFNSU4QyVCQiVFNSVCQSU5RiVFMyU4MCU4MSVFNyU5NCU5RiVFNiVCNCVCQiVFNSU5RSU4MyVFNSU5QyVCRSVFNCVCOCU4MCVFNCVCRCU5MyVFNSU4QyU5NiVFNiU4OSU5OCVFNyVBRSVBMSVFNiU5QyU4RCVFNSU4QSVBMSVFOSVBMSVCOSVFNyU5QiVBRSVFNCVCQSU4QyVFNiVBQyVBMSVFNiU4QiU5QiVFNiVBMCU4NyVFNiU5NiU4NyVFNCVCQiVCNiVFRiVCQyU4OCVFNSU4RiU5MSVFNSU5NCVBRSVFNyU4OSU4OCVFRiVCQyU4OS5kb2N4P1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9SDNLMVAwMkZBQjJKRUZZSFRUUTAlMkYyMDI1MDgwNiUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNTA4MDZUMTA0MDEwWiZYLUFtei1FeHBpcmVzPTQzMTk5JlgtQW16LVNlY3VyaXR5LVRva2VuPWV5SmhiR2NpT2lKSVV6VXhNaUlzSW5SNWNDSTZJa3BYVkNKOS5leUpoWTJObGMzTkxaWGtpT2lKSU0wc3hVREF5UmtGQ01rcEZSbGxJVkZSUk1DSXNJbVY0Y0NJNk1UYzFORFV4TnpVMU5Dd2ljR0Z5Wlc1MElqb2ljR1Z1WjJOb1lXNW5JbjAuUGdjMXpiSEZlS3diMkw2cFZ5b2VfVVRlNXVmU3l5UjEySFNhZ1dSWkdfRVo3bFVfcEVTNExlWEZMVkFud0doZEw3NVpENjNmcGFCVl81VGcwcnJvVXcmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0JnZlcnNpb25JZD1udWxsJlgtQW16LVNpZ25hdHVyZT0wNTZjNmIwNDU5ZGZiMDYzMmNkN2QxMThlOGFlNjk5MDVjM2E4M2U4ZDYxMjU5ZGY3MWJjOTI2MzA2MjMyYTAy)
2025-08-07 19:18:17 | ERROR    | app.services.compliance_service:execute_pipeline:409 | 步骤3: 文件处理失败，无法继续 | ID: 3c650d57-4044-47e8-9eb0-c402ca737a3c | 错误: 文件处理阶段失败: 文件下载失败: 403 Client Error: Forbidden for url: http://localhost:9090/api/v1/download-shared-object/aHR0cDovLzEyNy4wLjAuMTo5MDAwL3poYW9iaWFvd2VuamlhbmppYW5jaGF6aHVzaG91LyVFNSU4RCU5NyVFNSU4QyVCQSVFNiVCMSVBMSVFNiVCMCVCNCVFNSU4QyVCQiVFNSVCQSU5RiVFMyU4MCU4MSVFNyU5NCU5RiVFNiVCNCVCQiVFNSU5RSU4MyVFNSU5QyVCRSVFNCVCOCU4MCVFNCVCRCU5MyVFNSU4QyU5NiVFNiU4OSU5OCVFNyVBRSVBMSVFNiU5QyU4RCVFNSU4QSVBMSVFOSVBMSVCOSVFNyU5QiVBRSVFNCVCQSU4QyVFNiVBQyVBMSVFNiU4QiU5QiVFNiVBMCU4NyVFNiU5NiU4NyVFNCVCQiVCNiVFRiVCQyU4OCVFNSU4RiU5MSVFNSU5NCVBRSVFNyU4OSU4OCVFRiVCQyU4OS5kb2N4P1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9SDNLMVAwMkZBQjJKRUZZSFRUUTAlMkYyMDI1MDgwNiUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNTA4MDZUMTA0MDEwWiZYLUFtei1FeHBpcmVzPTQzMTk5JlgtQW16LVNlY3VyaXR5LVRva2VuPWV5SmhiR2NpT2lKSVV6VXhNaUlzSW5SNWNDSTZJa3BYVkNKOS5leUpoWTJObGMzTkxaWGtpT2lKSU0wc3hVREF5UmtGQ01rcEZSbGxJVkZSUk1DSXNJbVY0Y0NJNk1UYzFORFV4TnpVMU5Dd2ljR0Z5Wlc1MElqb2ljR1Z1WjJOb1lXNW5JbjAuUGdjMXpiSEZlS3diMkw2cFZ5b2VfVVRlNXVmU3l5UjEySFNhZ1dSWkdfRVo3bFVfcEVTNExlWEZMVkFud0doZEw3NVpENjNmcGFCVl81VGcwcnJvVXcmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0JnZlcnNpb25JZD1udWxsJlgtQW16LVNpZ25hdHVyZT0wNTZjNmIwNDU5ZGZiMDYzMmNkN2QxMThlOGFlNjk5MDVjM2E4M2U4ZDYxMjU5ZGY3MWJjOTI2MzA2MjMyYTAy (URL: http://localhost:9090/api/v1/download-shared-object/aHR0cDovLzEyNy4wLjAuMTo5MDAwL3poYW9iaWFvd2VuamlhbmppYW5jaGF6aHVzaG91LyVFNSU4RCU5NyVFNSU4QyVCQSVFNiVCMSVBMSVFNiVCMCVCNCVFNSU4QyVCQiVFNSVCQSU5RiVFMyU4MCU4MSVFNyU5NCU5RiVFNiVCNCVCQiVFNSU5RSU4MyVFNSU5QyVCRSVFNCVCOCU4MCVFNCVCRCU5MyVFNSU4QyU5NiVFNiU4OSU5OCVFNyVBRSVBMSVFNiU5QyU4RCVFNSU4QSVBMSVFOSVBMSVCOSVFNyU5QiVBRSVFNCVCQSU4QyVFNiVBQyVBMSVFNiU4QiU5QiVFNiVBMCU4NyVFNiU5NiU4NyVFNCVCQiVCNiVFRiVCQyU4OCVFNSU4RiU5MSVFNSU5NCVBRSVFNyU4OSU4OCVFRiVCQyU4OS5kb2N4P1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9SDNLMVAwMkZBQjJKRUZZSFRUUTAlMkYyMDI1MDgwNiUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNTA4MDZUMTA0MDEwWiZYLUFtei1FeHBpcmVzPTQzMTk5JlgtQW16LVNlY3VyaXR5LVRva2VuPWV5SmhiR2NpT2lKSVV6VXhNaUlzSW5SNWNDSTZJa3BYVkNKOS5leUpoWTJObGMzTkxaWGtpT2lKSU0wc3hVREF5UmtGQ01rcEZSbGxJVkZSUk1DSXNJbVY0Y0NJNk1UYzFORFV4TnpVMU5Dd2ljR0Z5Wlc1MElqb2ljR1Z1WjJOb1lXNW5JbjAuUGdjMXpiSEZlS3diMkw2cFZ5b2VfVVRlNXVmU3l5UjEySFNhZ1dSWkdfRVo3bFVfcEVTNExlWEZMVkFud0doZEw3NVpENjNmcGFCVl81VGcwcnJvVXcmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0JnZlcnNpb25JZD1udWxsJlgtQW16LVNpZ25hdHVyZT0wNTZjNmIwNDU5ZGZiMDYzMmNkN2QxMThlOGFlNjk5MDVjM2E4M2U4ZDYxMjU5ZGY3MWJjOTI2MzA2MjMyYTAy)
2025-08-07 19:18:17 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 完整合规性检查流水线 | ID: 3c650d57-4044-47e8-9eb0-c402ca737a3c | 状态: 失败 | 耗时: 0.061秒
2025-08-07 19:18:17 | ERROR    | app.core.logger:__exit__:421 | 操作 完整合规性检查流水线 发生异常: 文件处理阶段失败: 文件下载失败: 403 Client Error: Forbidden for url: http://localhost:9090/api/v1/download-shared-object/aHR0cDovLzEyNy4wLjAuMTo5MDAwL3poYW9iaWFvd2VuamlhbmppYW5jaGF6aHVzaG91LyVFNSU4RCU5NyVFNSU4QyVCQSVFNiVCMSVBMSVFNiVCMCVCNCVFNSU4QyVCQiVFNSVCQSU5RiVFMyU4MCU4MSVFNyU5NCU5RiVFNiVCNCVCQiVFNSU5RSU4MyVFNSU5QyVCRSVFNCVCOCU4MCVFNCVCRCU5MyVFNSU4QyU5NiVFNiU4OSU5OCVFNyVBRSVBMSVFNiU5QyU4RCVFNSU4QSVBMSVFOSVBMSVCOSVFNyU5QiVBRSVFNCVCQSU4QyVFNiVBQyVBMSVFNiU4QiU5QiVFNiVBMCU4NyVFNiU5NiU4NyVFNCVCQiVCNiVFRiVCQyU4OCVFNSU4RiU5MSVFNSU5NCVBRSVFNyU4OSU4OCVFRiVCQyU4OS5kb2N4P1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9SDNLMVAwMkZBQjJKRUZZSFRUUTAlMkYyMDI1MDgwNiUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNTA4MDZUMTA0MDEwWiZYLUFtei1FeHBpcmVzPTQzMTk5JlgtQW16LVNlY3VyaXR5LVRva2VuPWV5SmhiR2NpT2lKSVV6VXhNaUlzSW5SNWNDSTZJa3BYVkNKOS5leUpoWTJObGMzTkxaWGtpT2lKSU0wc3hVREF5UmtGQ01rcEZSbGxJVkZSUk1DSXNJbVY0Y0NJNk1UYzFORFV4TnpVMU5Dd2ljR0Z5Wlc1MElqb2ljR1Z1WjJOb1lXNW5JbjAuUGdjMXpiSEZlS3diMkw2cFZ5b2VfVVRlNXVmU3l5UjEySFNhZ1dSWkdfRVo3bFVfcEVTNExlWEZMVkFud0doZEw3NVpENjNmcGFCVl81VGcwcnJvVXcmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0JnZlcnNpb25JZD1udWxsJlgtQW16LVNpZ25hdHVyZT0wNTZjNmIwNDU5ZGZiMDYzMmNkN2QxMThlOGFlNjk5MDVjM2E4M2U4ZDYxMjU5ZGY3MWJjOTI2MzA2MjMyYTAy (URL: http://localhost:9090/api/v1/download-shared-object/aHR0cDovLzEyNy4wLjAuMTo5MDAwL3poYW9iaWFvd2VuamlhbmppYW5jaGF6aHVzaG91LyVFNSU4RCU5NyVFNSU4QyVCQSVFNiVCMSVBMSVFNiVCMCVCNCVFNSU4QyVCQiVFNSVCQSU5RiVFMyU4MCU4MSVFNyU5NCU5RiVFNiVCNCVCQiVFNSU5RSU4MyVFNSU5QyVCRSVFNCVCOCU4MCVFNCVCRCU5MyVFNSU4QyU5NiVFNiU4OSU5OCVFNyVBRSVBMSVFNiU5QyU4RCVFNSU4QSVBMSVFOSVBMSVCOSVFNyU5QiVBRSVFNCVCQSU4QyVFNiVBQyVBMSVFNiU4QiU5QiVFNiVBMCU4NyVFNiU5NiU4NyVFNCVCQiVCNiVFRiVCQyU4OCVFNSU4RiU5MSVFNSU5NCVBRSVFNyU4OSU4OCVFRiVCQyU4OS5kb2N4P1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9SDNLMVAwMkZBQjJKRUZZSFRUUTAlMkYyMDI1MDgwNiUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNTA4MDZUMTA0MDEwWiZYLUFtei1FeHBpcmVzPTQzMTk5JlgtQW16LVNlY3VyaXR5LVRva2VuPWV5SmhiR2NpT2lKSVV6VXhNaUlzSW5SNWNDSTZJa3BYVkNKOS5leUpoWTJObGMzTkxaWGtpT2lKSU0wc3hVREF5UmtGQ01rcEZSbGxJVkZSUk1DSXNJbVY0Y0NJNk1UYzFORFV4TnpVMU5Dd2ljR0Z5Wlc1MElqb2ljR1Z1WjJOb1lXNW5JbjAuUGdjMXpiSEZlS3diMkw2cFZ5b2VfVVRlNXVmU3l5UjEySFNhZ1dSWkdfRVo3bFVfcEVTNExlWEZMVkFud0doZEw3NVpENjNmcGFCVl81VGcwcnJvVXcmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0JnZlcnNpb25JZD1udWxsJlgtQW16LVNpZ25hdHVyZT0wNTZjNmIwNDU5ZGZiMDYzMmNkN2QxMThlOGFlNjk5MDVjM2E4M2U4ZDYxMjU5ZGY3MWJjOTI2MzA2MjMyYTAy)
2025-08-07 19:18:17 | ERROR    | app.services.compliance_service:execute_pipeline:505 | 合规性检查流水线失败 | ID: 3c650d57-4044-47e8-9eb0-c402ca737a3c | 耗时: 0.066秒 | 错误: 文件处理阶段失败: 文件下载失败: 403 Client Error: Forbidden for url: http://localhost:9090/api/v1/download-shared-object/aHR0cDovLzEyNy4wLjAuMTo5MDAwL3poYW9iaWFvd2VuamlhbmppYW5jaGF6aHVzaG91LyVFNSU4RCU5NyVFNSU4QyVCQSVFNiVCMSVBMSVFNiVCMCVCNCVFNSU4QyVCQiVFNSVCQSU5RiVFMyU4MCU4MSVFNyU5NCU5RiVFNiVCNCVCQiVFNSU5RSU4MyVFNSU5QyVCRSVFNCVCOCU4MCVFNCVCRCU5MyVFNSU4QyU5NiVFNiU4OSU5OCVFNyVBRSVBMSVFNiU5QyU4RCVFNSU4QSVBMSVFOSVBMSVCOSVFNyU5QiVBRSVFNCVCQSU4QyVFNiVBQyVBMSVFNiU4QiU5QiVFNiVBMCU4NyVFNiU5NiU4NyVFNCVCQiVCNiVFRiVCQyU4OCVFNSU4RiU5MSVFNSU5NCVBRSVFNyU4OSU4OCVFRiVCQyU4OS5kb2N4P1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9SDNLMVAwMkZBQjJKRUZZSFRUUTAlMkYyMDI1MDgwNiUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNTA4MDZUMTA0MDEwWiZYLUFtei1FeHBpcmVzPTQzMTk5JlgtQW16LVNlY3VyaXR5LVRva2VuPWV5SmhiR2NpT2lKSVV6VXhNaUlzSW5SNWNDSTZJa3BYVkNKOS5leUpoWTJObGMzTkxaWGtpT2lKSU0wc3hVREF5UmtGQ01rcEZSbGxJVkZSUk1DSXNJbVY0Y0NJNk1UYzFORFV4TnpVMU5Dd2ljR0Z5Wlc1MElqb2ljR1Z1WjJOb1lXNW5JbjAuUGdjMXpiSEZlS3diMkw2cFZ5b2VfVVRlNXVmU3l5UjEySFNhZ1dSWkdfRVo3bFVfcEVTNExlWEZMVkFud0doZEw3NVpENjNmcGFCVl81VGcwcnJvVXcmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0JnZlcnNpb25JZD1udWxsJlgtQW16LVNpZ25hdHVyZT0wNTZjNmIwNDU5ZGZiMDYzMmNkN2QxMThlOGFlNjk5MDVjM2E4M2U4ZDYxMjU5ZGY3MWJjOTI2MzA2MjMyYTAy (URL: http://localhost:9090/api/v1/download-shared-object/aHR0cDovLzEyNy4wLjAuMTo5MDAwL3poYW9iaWFvd2VuamlhbmppYW5jaGF6aHVzaG91LyVFNSU4RCU5NyVFNSU4QyVCQSVFNiVCMSVBMSVFNiVCMCVCNCVFNSU4QyVCQiVFNSVCQSU5RiVFMyU4MCU4MSVFNyU5NCU5RiVFNiVCNCVCQiVFNSU5RSU4MyVFNSU5QyVCRSVFNCVCOCU4MCVFNCVCRCU5MyVFNSU4QyU5NiVFNiU4OSU5OCVFNyVBRSVBMSVFNiU5QyU4RCVFNSU4QSVBMSVFOSVBMSVCOSVFNyU5QiVBRSVFNCVCQSU4QyVFNiVBQyVBMSVFNiU4QiU5QiVFNiVBMCU4NyVFNiU5NiU4NyVFNCVCQiVCNiVFRiVCQyU4OCVFNSU4RiU5MSVFNSU5NCVBRSVFNyU4OSU4OCVFRiVCQyU4OS5kb2N4P1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9SDNLMVAwMkZBQjJKRUZZSFRUUTAlMkYyMDI1MDgwNiUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNTA4MDZUMTA0MDEwWiZYLUFtei1FeHBpcmVzPTQzMTk5JlgtQW16LVNlY3VyaXR5LVRva2VuPWV5SmhiR2NpT2lKSVV6VXhNaUlzSW5SNWNDSTZJa3BYVkNKOS5leUpoWTJObGMzTkxaWGtpT2lKSU0wc3hVREF5UmtGQ01rcEZSbGxJVkZSUk1DSXNJbVY0Y0NJNk1UYzFORFV4TnpVMU5Dd2ljR0Z5Wlc1MElqb2ljR1Z1WjJOb1lXNW5JbjAuUGdjMXpiSEZlS3diMkw2cFZ5b2VfVVRlNXVmU3l5UjEySFNhZ1dSWkdfRVo3bFVfcEVTNExlWEZMVkFud0doZEw3NVpENjNmcGFCVl81VGcwcnJvVXcmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0JnZlcnNpb25JZD1udWxsJlgtQW16LVNpZ25hdHVyZT0wNTZjNmIwNDU5ZGZiMDYzMmNkN2QxMThlOGFlNjk5MDVjM2E4M2U4ZDYxMjU5ZGY3MWJjOTI2MzA2MjMyYTAy)
2025-08-07 19:18:17 | ERROR    | app.core.logger:wrapper:452 | 函数调用失败: execute_pipeline | 耗时: 0.066秒 | 错误: 文件处理阶段失败: 文件下载失败: 403 Client Error: Forbidden for url: http://localhost:9090/api/v1/download-shared-object/aHR0cDovLzEyNy4wLjAuMTo5MDAwL3poYW9iaWFvd2VuamlhbmppYW5jaGF6aHVzaG91LyVFNSU4RCU5NyVFNSU4QyVCQSVFNiVCMSVBMSVFNiVCMCVCNCVFNSU4QyVCQiVFNSVCQSU5RiVFMyU4MCU4MSVFNyU5NCU5RiVFNiVCNCVCQiVFNSU5RSU4MyVFNSU5QyVCRSVFNCVCOCU4MCVFNCVCRCU5MyVFNSU4QyU5NiVFNiU4OSU5OCVFNyVBRSVBMSVFNiU5QyU4RCVFNSU4QSVBMSVFOSVBMSVCOSVFNyU5QiVBRSVFNCVCQSU4QyVFNiVBQyVBMSVFNiU4QiU5QiVFNiVBMCU4NyVFNiU5NiU4NyVFNCVCQiVCNiVFRiVCQyU4OCVFNSU4RiU5MSVFNSU5NCVBRSVFNyU4OSU4OCVFRiVCQyU4OS5kb2N4P1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9SDNLMVAwMkZBQjJKRUZZSFRUUTAlMkYyMDI1MDgwNiUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNTA4MDZUMTA0MDEwWiZYLUFtei1FeHBpcmVzPTQzMTk5JlgtQW16LVNlY3VyaXR5LVRva2VuPWV5SmhiR2NpT2lKSVV6VXhNaUlzSW5SNWNDSTZJa3BYVkNKOS5leUpoWTJObGMzTkxaWGtpT2lKSU0wc3hVREF5UmtGQ01rcEZSbGxJVkZSUk1DSXNJbVY0Y0NJNk1UYzFORFV4TnpVMU5Dd2ljR0Z5Wlc1MElqb2ljR1Z1WjJOb1lXNW5JbjAuUGdjMXpiSEZlS3diMkw2cFZ5b2VfVVRlNXVmU3l5UjEySFNhZ1dSWkdfRVo3bFVfcEVTNExlWEZMVkFud0doZEw3NVpENjNmcGFCVl81VGcwcnJvVXcmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0JnZlcnNpb25JZD1udWxsJlgtQW16LVNpZ25hdHVyZT0wNTZjNmIwNDU5ZGZiMDYzMmNkN2QxMThlOGFlNjk5MDVjM2E4M2U4ZDYxMjU5ZGY3MWJjOTI2MzA2MjMyYTAy (URL: http://localhost:9090/api/v1/download-shared-object/aHR0cDovLzEyNy4wLjAuMTo5MDAwL3poYW9iaWFvd2VuamlhbmppYW5jaGF6aHVzaG91LyVFNSU4RCU5NyVFNSU4QyVCQSVFNiVCMSVBMSVFNiVCMCVCNCVFNSU4QyVCQiVFNSVCQSU5RiVFMyU4MCU4MSVFNyU5NCU5RiVFNiVCNCVCQiVFNSU5RSU4MyVFNSU5QyVCRSVFNCVCOCU4MCVFNCVCRCU5MyVFNSU4QyU5NiVFNiU4OSU5OCVFNyVBRSVBMSVFNiU5QyU4RCVFNSU4QSVBMSVFOSVBMSVCOSVFNyU5QiVBRSVFNCVCQSU4QyVFNiVBQyVBMSVFNiU4QiU5QiVFNiVBMCU4NyVFNiU5NiU4NyVFNCVCQiVCNiVFRiVCQyU4OCVFNSU4RiU5MSVFNSU5NCVBRSVFNyU4OSU4OCVFRiVCQyU4OS5kb2N4P1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9SDNLMVAwMkZBQjJKRUZZSFRUUTAlMkYyMDI1MDgwNiUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNTA4MDZUMTA0MDEwWiZYLUFtei1FeHBpcmVzPTQzMTk5JlgtQW16LVNlY3VyaXR5LVRva2VuPWV5SmhiR2NpT2lKSVV6VXhNaUlzSW5SNWNDSTZJa3BYVkNKOS5leUpoWTJObGMzTkxaWGtpT2lKSU0wc3hVREF5UmtGQ01rcEZSbGxJVkZSUk1DSXNJbVY0Y0NJNk1UYzFORFV4TnpVMU5Dd2ljR0Z5Wlc1MElqb2ljR1Z1WjJOb1lXNW5JbjAuUGdjMXpiSEZlS3diMkw2cFZ5b2VfVVRlNXVmU3l5UjEySFNhZ1dSWkdfRVo3bFVfcEVTNExlWEZMVkFud0doZEw3NVpENjNmcGFCVl81VGcwcnJvVXcmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0JnZlcnNpb25JZD1udWxsJlgtQW16LVNpZ25hdHVyZT0wNTZjNmIwNDU5ZGZiMDYzMmNkN2QxMThlOGFlNjk5MDVjM2E4M2U4ZDYxMjU5ZGY3MWJjOTI2MzA2MjMyYTAy)
2025-08-07 19:18:17 | ERROR    | app.services.compliance_service:check_compliance:645 | 合规性检查服务失败 | ID: 3c650d57-4044-47e8-9eb0-c402ca737a3c | 错误: 文件处理阶段失败: 文件下载失败: 403 Client Error: Forbidden for url: http://localhost:9090/api/v1/download-shared-object/aHR0cDovLzEyNy4wLjAuMTo5MDAwL3poYW9iaWFvd2VuamlhbmppYW5jaGF6aHVzaG91LyVFNSU4RCU5NyVFNSU4QyVCQSVFNiVCMSVBMSVFNiVCMCVCNCVFNSU4QyVCQiVFNSVCQSU5RiVFMyU4MCU4MSVFNyU5NCU5RiVFNiVCNCVCQiVFNSU5RSU4MyVFNSU5QyVCRSVFNCVCOCU4MCVFNCVCRCU5MyVFNSU4QyU5NiVFNiU4OSU5OCVFNyVBRSVBMSVFNiU5QyU4RCVFNSU4QSVBMSVFOSVBMSVCOSVFNyU5QiVBRSVFNCVCQSU4QyVFNiVBQyVBMSVFNiU4QiU5QiVFNiVBMCU4NyVFNiU5NiU4NyVFNCVCQiVCNiVFRiVCQyU4OCVFNSU4RiU5MSVFNSU5NCVBRSVFNyU4OSU4OCVFRiVCQyU4OS5kb2N4P1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9SDNLMVAwMkZBQjJKRUZZSFRUUTAlMkYyMDI1MDgwNiUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNTA4MDZUMTA0MDEwWiZYLUFtei1FeHBpcmVzPTQzMTk5JlgtQW16LVNlY3VyaXR5LVRva2VuPWV5SmhiR2NpT2lKSVV6VXhNaUlzSW5SNWNDSTZJa3BYVkNKOS5leUpoWTJObGMzTkxaWGtpT2lKSU0wc3hVREF5UmtGQ01rcEZSbGxJVkZSUk1DSXNJbVY0Y0NJNk1UYzFORFV4TnpVMU5Dd2ljR0Z5Wlc1MElqb2ljR1Z1WjJOb1lXNW5JbjAuUGdjMXpiSEZlS3diMkw2cFZ5b2VfVVRlNXVmU3l5UjEySFNhZ1dSWkdfRVo3bFVfcEVTNExlWEZMVkFud0doZEw3NVpENjNmcGFCVl81VGcwcnJvVXcmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0JnZlcnNpb25JZD1udWxsJlgtQW16LVNpZ25hdHVyZT0wNTZjNmIwNDU5ZGZiMDYzMmNkN2QxMThlOGFlNjk5MDVjM2E4M2U4ZDYxMjU5ZGY3MWJjOTI2MzA2MjMyYTAy (URL: http://localhost:9090/api/v1/download-shared-object/aHR0cDovLzEyNy4wLjAuMTo5MDAwL3poYW9iaWFvd2VuamlhbmppYW5jaGF6aHVzaG91LyVFNSU4RCU5NyVFNSU4QyVCQSVFNiVCMSVBMSVFNiVCMCVCNCVFNSU4QyVCQiVFNSVCQSU5RiVFMyU4MCU4MSVFNyU5NCU5RiVFNiVCNCVCQiVFNSU5RSU4MyVFNSU5QyVCRSVFNCVCOCU4MCVFNCVCRCU5MyVFNSU4QyU5NiVFNiU4OSU5OCVFNyVBRSVBMSVFNiU5QyU4RCVFNSU4QSVBMSVFOSVBMSVCOSVFNyU5QiVBRSVFNCVCQSU4QyVFNiVBQyVBMSVFNiU4QiU5QiVFNiVBMCU4NyVFNiU5NiU4NyVFNCVCQiVCNiVFRiVCQyU4OCVFNSU4RiU5MSVFNSU5NCVBRSVFNyU4OSU4OCVFRiVCQyU4OS5kb2N4P1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9SDNLMVAwMkZBQjJKRUZZSFRUUTAlMkYyMDI1MDgwNiUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNTA4MDZUMTA0MDEwWiZYLUFtei1FeHBpcmVzPTQzMTk5JlgtQW16LVNlY3VyaXR5LVRva2VuPWV5SmhiR2NpT2lKSVV6VXhNaUlzSW5SNWNDSTZJa3BYVkNKOS5leUpoWTJObGMzTkxaWGtpT2lKSU0wc3hVREF5UmtGQ01rcEZSbGxJVkZSUk1DSXNJbVY0Y0NJNk1UYzFORFV4TnpVMU5Dd2ljR0Z5Wlc1MElqb2ljR1Z1WjJOb1lXNW5JbjAuUGdjMXpiSEZlS3diMkw2cFZ5b2VfVVRlNXVmU3l5UjEySFNhZ1dSWkdfRVo3bFVfcEVTNExlWEZMVkFud0doZEw3NVpENjNmcGFCVl81VGcwcnJvVXcmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0JnZlcnNpb25JZD1udWxsJlgtQW16LVNpZ25hdHVyZT0wNTZjNmIwNDU5ZGZiMDYzMmNkN2QxMThlOGFlNjk5MDVjM2E4M2U4ZDYxMjU5ZGY3MWJjOTI2MzA2MjMyYTAy)
2025-08-07 19:18:17 | ERROR    | app.core.logger:wrapper:452 | 函数调用失败: check_compliance | 耗时: 0.066秒 | 错误: 合规性检查失败: 文件处理阶段失败: 文件下载失败: 403 Client Error: Forbidden for url: http://localhost:9090/api/v1/download-shared-object/aHR0cDovLzEyNy4wLjAuMTo5MDAwL3poYW9iaWFvd2VuamlhbmppYW5jaGF6aHVzaG91LyVFNSU4RCU5NyVFNSU4QyVCQSVFNiVCMSVBMSVFNiVCMCVCNCVFNSU4QyVCQiVFNSVCQSU5RiVFMyU4MCU4MSVFNyU5NCU5RiVFNiVCNCVCQiVFNSU5RSU4MyVFNSU5QyVCRSVFNCVCOCU4MCVFNCVCRCU5MyVFNSU4QyU5NiVFNiU4OSU5OCVFNyVBRSVBMSVFNiU5QyU4RCVFNSU4QSVBMSVFOSVBMSVCOSVFNyU5QiVBRSVFNCVCQSU4QyVFNiVBQyVBMSVFNiU4QiU5QiVFNiVBMCU4NyVFNiU5NiU4NyVFNCVCQiVCNiVFRiVCQyU4OCVFNSU4RiU5MSVFNSU5NCVBRSVFNyU4OSU4OCVFRiVCQyU4OS5kb2N4P1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9SDNLMVAwMkZBQjJKRUZZSFRUUTAlMkYyMDI1MDgwNiUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNTA4MDZUMTA0MDEwWiZYLUFtei1FeHBpcmVzPTQzMTk5JlgtQW16LVNlY3VyaXR5LVRva2VuPWV5SmhiR2NpT2lKSVV6VXhNaUlzSW5SNWNDSTZJa3BYVkNKOS5leUpoWTJObGMzTkxaWGtpT2lKSU0wc3hVREF5UmtGQ01rcEZSbGxJVkZSUk1DSXNJbVY0Y0NJNk1UYzFORFV4TnpVMU5Dd2ljR0Z5Wlc1MElqb2ljR1Z1WjJOb1lXNW5JbjAuUGdjMXpiSEZlS3diMkw2cFZ5b2VfVVRlNXVmU3l5UjEySFNhZ1dSWkdfRVo3bFVfcEVTNExlWEZMVkFud0doZEw3NVpENjNmcGFCVl81VGcwcnJvVXcmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0JnZlcnNpb25JZD1udWxsJlgtQW16LVNpZ25hdHVyZT0wNTZjNmIwNDU5ZGZiMDYzMmNkN2QxMThlOGFlNjk5MDVjM2E4M2U4ZDYxMjU5ZGY3MWJjOTI2MzA2MjMyYTAy (URL: http://localhost:9090/api/v1/download-shared-object/aHR0cDovLzEyNy4wLjAuMTo5MDAwL3poYW9iaWFvd2VuamlhbmppYW5jaGF6aHVzaG91LyVFNSU4RCU5NyVFNSU4QyVCQSVFNiVCMSVBMSVFNiVCMCVCNCVFNSU4QyVCQiVFNSVCQSU5RiVFMyU4MCU4MSVFNyU5NCU5RiVFNiVCNCVCQiVFNSU5RSU4MyVFNSU5QyVCRSVFNCVCOCU4MCVFNCVCRCU5MyVFNSU4QyU5NiVFNiU4OSU5OCVFNyVBRSVBMSVFNiU5QyU4RCVFNSU4QSVBMSVFOSVBMSVCOSVFNyU5QiVBRSVFNCVCQSU4QyVFNiVBQyVBMSVFNiU4QiU5QiVFNiVBMCU4NyVFNiU5NiU4NyVFNCVCQiVCNiVFRiVCQyU4OCVFNSU4RiU5MSVFNSU5NCVBRSVFNyU4OSU4OCVFRiVCQyU4OS5kb2N4P1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9SDNLMVAwMkZBQjJKRUZZSFRUUTAlMkYyMDI1MDgwNiUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNTA4MDZUMTA0MDEwWiZYLUFtei1FeHBpcmVzPTQzMTk5JlgtQW16LVNlY3VyaXR5LVRva2VuPWV5SmhiR2NpT2lKSVV6VXhNaUlzSW5SNWNDSTZJa3BYVkNKOS5leUpoWTJObGMzTkxaWGtpT2lKSU0wc3hVREF5UmtGQ01rcEZSbGxJVkZSUk1DSXNJbVY0Y0NJNk1UYzFORFV4TnpVMU5Dd2ljR0Z5Wlc1MElqb2ljR1Z1WjJOb1lXNW5JbjAuUGdjMXpiSEZlS3diMkw2cFZ5b2VfVVRlNXVmU3l5UjEySFNhZ1dSWkdfRVo3bFVfcEVTNExlWEZMVkFud0doZEw3NVpENjNmcGFCVl81VGcwcnJvVXcmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0JnZlcnNpb25JZD1udWxsJlgtQW16LVNpZ25hdHVyZT0wNTZjNmIwNDU5ZGZiMDYzMmNkN2QxMThlOGFlNjk5MDVjM2E4M2U4ZDYxMjU5ZGY3MWJjOTI2MzA2MjMyYTAy)
2025-08-07 19:18:17 | ERROR    | app.api.routes:check_compliance:102 | 合规性检查失败 | ID: 3c650d57-4044-47e8-9eb0-c402ca737a3c | 错误: 合规性检查失败: 文件处理阶段失败: 文件下载失败: 403 Client Error: Forbidden for url: http://localhost:9090/api/v1/download-shared-object/aHR0cDovLzEyNy4wLjAuMTo5MDAwL3poYW9iaWFvd2VuamlhbmppYW5jaGF6aHVzaG91LyVFNSU4RCU5NyVFNSU4QyVCQSVFNiVCMSVBMSVFNiVCMCVCNCVFNSU4QyVCQiVFNSVCQSU5RiVFMyU4MCU4MSVFNyU5NCU5RiVFNiVCNCVCQiVFNSU5RSU4MyVFNSU5QyVCRSVFNCVCOCU4MCVFNCVCRCU5MyVFNSU4QyU5NiVFNiU4OSU5OCVFNyVBRSVBMSVFNiU5QyU4RCVFNSU4QSVBMSVFOSVBMSVCOSVFNyU5QiVBRSVFNCVCQSU4QyVFNiVBQyVBMSVFNiU4QiU5QiVFNiVBMCU4NyVFNiU5NiU4NyVFNCVCQiVCNiVFRiVCQyU4OCVFNSU4RiU5MSVFNSU5NCVBRSVFNyU4OSU4OCVFRiVCQyU4OS5kb2N4P1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9SDNLMVAwMkZBQjJKRUZZSFRUUTAlMkYyMDI1MDgwNiUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNTA4MDZUMTA0MDEwWiZYLUFtei1FeHBpcmVzPTQzMTk5JlgtQW16LVNlY3VyaXR5LVRva2VuPWV5SmhiR2NpT2lKSVV6VXhNaUlzSW5SNWNDSTZJa3BYVkNKOS5leUpoWTJObGMzTkxaWGtpT2lKSU0wc3hVREF5UmtGQ01rcEZSbGxJVkZSUk1DSXNJbVY0Y0NJNk1UYzFORFV4TnpVMU5Dd2ljR0Z5Wlc1MElqb2ljR1Z1WjJOb1lXNW5JbjAuUGdjMXpiSEZlS3diMkw2cFZ5b2VfVVRlNXVmU3l5UjEySFNhZ1dSWkdfRVo3bFVfcEVTNExlWEZMVkFud0doZEw3NVpENjNmcGFCVl81VGcwcnJvVXcmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0JnZlcnNpb25JZD1udWxsJlgtQW16LVNpZ25hdHVyZT0wNTZjNmIwNDU5ZGZiMDYzMmNkN2QxMThlOGFlNjk5MDVjM2E4M2U4ZDYxMjU5ZGY3MWJjOTI2MzA2MjMyYTAy (URL: http://localhost:9090/api/v1/download-shared-object/aHR0cDovLzEyNy4wLjAuMTo5MDAwL3poYW9iaWFvd2VuamlhbmppYW5jaGF6aHVzaG91LyVFNSU4RCU5NyVFNSU4QyVCQSVFNiVCMSVBMSVFNiVCMCVCNCVFNSU4QyVCQiVFNSVCQSU5RiVFMyU4MCU4MSVFNyU5NCU5RiVFNiVCNCVCQiVFNSU5RSU4MyVFNSU5QyVCRSVFNCVCOCU4MCVFNCVCRCU5MyVFNSU4QyU5NiVFNiU4OSU5OCVFNyVBRSVBMSVFNiU5QyU4RCVFNSU4QSVBMSVFOSVBMSVCOSVFNyU5QiVBRSVFNCVCQSU4QyVFNiVBQyVBMSVFNiU4QiU5QiVFNiVBMCU4NyVFNiU5NiU4NyVFNCVCQiVCNiVFRiVCQyU4OCVFNSU4RiU5MSVFNSU5NCVBRSVFNyU4OSU4OCVFRiVCQyU4OS5kb2N4P1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9SDNLMVAwMkZBQjJKRUZZSFRUUTAlMkYyMDI1MDgwNiUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNTA4MDZUMTA0MDEwWiZYLUFtei1FeHBpcmVzPTQzMTk5JlgtQW16LVNlY3VyaXR5LVRva2VuPWV5SmhiR2NpT2lKSVV6VXhNaUlzSW5SNWNDSTZJa3BYVkNKOS5leUpoWTJObGMzTkxaWGtpT2lKSU0wc3hVREF5UmtGQ01rcEZSbGxJVkZSUk1DSXNJbVY0Y0NJNk1UYzFORFV4TnpVMU5Dd2ljR0Z5Wlc1MElqb2ljR1Z1WjJOb1lXNW5JbjAuUGdjMXpiSEZlS3diMkw2cFZ5b2VfVVRlNXVmU3l5UjEySFNhZ1dSWkdfRVo3bFVfcEVTNExlWEZMVkFud0doZEw3NVpENjNmcGFCVl81VGcwcnJvVXcmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0JnZlcnNpb25JZD1udWxsJlgtQW16LVNpZ25hdHVyZT0wNTZjNmIwNDU5ZGZiMDYzMmNkN2QxMThlOGFlNjk5MDVjM2E4M2U4ZDYxMjU5ZGY3MWJjOTI2MzA2MjMyYTAy)
2025-08-07 19:18:17 | ERROR    | app.middleware.validation:dispatch:55 | 请求验证异常 | ID: 3c650d57-4044-47e8-9eb0-c402ca737a3c | 错误: 合规性检查失败: 文件处理阶段失败: 文件下载失败: 403 Client Error: Forbidden for url: http://localhost:9090/api/v1/download-shared-object/aHR0cDovLzEyNy4wLjAuMTo5MDAwL3poYW9iaWFvd2VuamlhbmppYW5jaGF6aHVzaG91LyVFNSU4RCU5NyVFNSU4QyVCQSVFNiVCMSVBMSVFNiVCMCVCNCVFNSU4QyVCQiVFNSVCQSU5RiVFMyU4MCU4MSVFNyU5NCU5RiVFNiVCNCVCQiVFNSU5RSU4MyVFNSU5QyVCRSVFNCVCOCU4MCVFNCVCRCU5MyVFNSU4QyU5NiVFNiU4OSU5OCVFNyVBRSVBMSVFNiU5QyU4RCVFNSU4QSVBMSVFOSVBMSVCOSVFNyU5QiVBRSVFNCVCQSU4QyVFNiVBQyVBMSVFNiU4QiU5QiVFNiVBMCU4NyVFNiU5NiU4NyVFNCVCQiVCNiVFRiVCQyU4OCVFNSU4RiU5MSVFNSU5NCVBRSVFNyU4OSU4OCVFRiVCQyU4OS5kb2N4P1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9SDNLMVAwMkZBQjJKRUZZSFRUUTAlMkYyMDI1MDgwNiUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNTA4MDZUMTA0MDEwWiZYLUFtei1FeHBpcmVzPTQzMTk5JlgtQW16LVNlY3VyaXR5LVRva2VuPWV5SmhiR2NpT2lKSVV6VXhNaUlzSW5SNWNDSTZJa3BYVkNKOS5leUpoWTJObGMzTkxaWGtpT2lKSU0wc3hVREF5UmtGQ01rcEZSbGxJVkZSUk1DSXNJbVY0Y0NJNk1UYzFORFV4TnpVMU5Dd2ljR0Z5Wlc1MElqb2ljR1Z1WjJOb1lXNW5JbjAuUGdjMXpiSEZlS3diMkw2cFZ5b2VfVVRlNXVmU3l5UjEySFNhZ1dSWkdfRVo3bFVfcEVTNExlWEZMVkFud0doZEw3NVpENjNmcGFCVl81VGcwcnJvVXcmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0JnZlcnNpb25JZD1udWxsJlgtQW16LVNpZ25hdHVyZT0wNTZjNmIwNDU5ZGZiMDYzMmNkN2QxMThlOGFlNjk5MDVjM2E4M2U4ZDYxMjU5ZGY3MWJjOTI2MzA2MjMyYTAy (URL: http://localhost:9090/api/v1/download-shared-object/aHR0cDovLzEyNy4wLjAuMTo5MDAwL3poYW9iaWFvd2VuamlhbmppYW5jaGF6aHVzaG91LyVFNSU4RCU5NyVFNSU4QyVCQSVFNiVCMSVBMSVFNiVCMCVCNCVFNSU4QyVCQiVFNSVCQSU5RiVFMyU4MCU4MSVFNyU5NCU5RiVFNiVCNCVCQiVFNSU5RSU4MyVFNSU5QyVCRSVFNCVCOCU4MCVFNCVCRCU5MyVFNSU4QyU5NiVFNiU4OSU5OCVFNyVBRSVBMSVFNiU5QyU4RCVFNSU4QSVBMSVFOSVBMSVCOSVFNyU5QiVBRSVFNCVCQSU4QyVFNiVBQyVBMSVFNiU4QiU5QiVFNiVBMCU4NyVFNiU5NiU4NyVFNCVCQiVCNiVFRiVCQyU4OCVFNSU4RiU5MSVFNSU5NCVBRSVFNyU4OSU4OCVFRiVCQyU4OS5kb2N4P1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9SDNLMVAwMkZBQjJKRUZZSFRUUTAlMkYyMDI1MDgwNiUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNTA4MDZUMTA0MDEwWiZYLUFtei1FeHBpcmVzPTQzMTk5JlgtQW16LVNlY3VyaXR5LVRva2VuPWV5SmhiR2NpT2lKSVV6VXhNaUlzSW5SNWNDSTZJa3BYVkNKOS5leUpoWTJObGMzTkxaWGtpT2lKSU0wc3hVREF5UmtGQ01rcEZSbGxJVkZSUk1DSXNJbVY0Y0NJNk1UYzFORFV4TnpVMU5Dd2ljR0Z5Wlc1MElqb2ljR1Z1WjJOb1lXNW5JbjAuUGdjMXpiSEZlS3diMkw2cFZ5b2VfVVRlNXVmU3l5UjEySFNhZ1dSWkdfRVo3bFVfcEVTNExlWEZMVkFud0doZEw3NVpENjNmcGFCVl81VGcwcnJvVXcmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0JnZlcnNpb25JZD1udWxsJlgtQW16LVNpZ25hdHVyZT0wNTZjNmIwNDU5ZGZiMDYzMmNkN2QxMThlOGFlNjk5MDVjM2E4M2U4ZDYxMjU5ZGY3MWJjOTI2MzA2MjMyYTAy)
2025-08-07 19:18:17 | INFO     | app.middleware.logging:dispatch:48 | 请求完成 | ID: 3c650d57-4044-47e8-9eb0-c402ca737a3c | 状态码: 400 | 耗时: 0.079秒
2025-08-07 19:18:38 | INFO     | app.middleware.logging:dispatch:35 | 请求开始 | ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c | 方法: POST | URL: http://localhost:8088/api/v1/check-compliance | 客户端IP: 127.0.0.1 | User-Agent: Apifox/1.0.0 (https://apifox.com)
2025-08-07 19:18:38 | INFO     | app.middleware.validation:_validate_request:75 | 跳过中间件验证 | ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c | 路径: /api/v1/check-compliance
2025-08-07 19:18:38 | INFO     | app.api.routes:check_compliance:78 | 开始合规性检查 | ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c | 文件: 南区污水医废、生活垃圾一体化托管服务项目二次招标文件（发售版）.docx
2025-08-07 19:18:38 | INFO     | app.services.compliance_service:check_compliance:636 | 合规性检查服务开始 | ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c
2025-08-07 19:18:38 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 完整合规性检查流水线 | ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c | 详情: {}
2025-08-07 19:18:38 | INFO     | app.services.compliance_service:execute_pipeline:376 | 开始执行合规性检查流水线 | ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c | 文件: 南区污水医废、生活垃圾一体化托管服务项目二次招标文件（发售版）.docx
2025-08-07 19:18:38 | INFO     | app.services.compliance_service:execute_pipeline:386 | 步骤1: 开始验证服务前置条件 | ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c
2025-08-07 19:18:38 | INFO     | app.services.compliance_service:validate_prerequisites:73 | 开始验证服务前置条件 | ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c
2025-08-07 19:18:38 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 敏感词服务健康检查 | ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c | 详情: {}
2025-08-07 19:18:38 | INFO     | app.services.sensitive_word_service:check_health:121 | 检查敏感词服务健康状态: http://*************:8087/health
2025-08-07 19:18:38 | INFO     | app.services.sensitive_word_service:check_health:131 | 敏感词服务健康状态: 健康
2025-08-07 19:18:38 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 敏感词服务健康检查 | ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c | 状态: 成功 | 耗时: 0.010秒
2025-08-07 19:18:38 | INFO     | app.services.compliance_service:validate_prerequisites:92 | 服务健康检查完成 | ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c | 状态: {'file_processor': True, 'ai_model': True, 'sensitive_word': True, 'result_processor': True}
2025-08-07 19:18:38 | INFO     | app.services.compliance_service:execute_pipeline:388 | 步骤1: 服务前置条件验证完成 | ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c | 状态: {'file_processor': True, 'ai_model': True, 'sensitive_word': True, 'result_processor': True}
2025-08-07 19:18:38 | INFO     | app.services.compliance_service:execute_pipeline:394 | 步骤2: 开始获取项目信息 | ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c
2025-08-07 19:18:38 | INFO     | app.services.compliance_service:execute_pipeline:396 | 步骤2: 项目信息获取完成 | ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c
2025-08-07 19:18:38 | INFO     | app.services.compliance_service:execute_pipeline:400 | 步骤3: 开始文件处理阶段 | ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c
2025-08-07 19:18:38 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 文件处理阶段 | ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c | 详情: {}
2025-08-07 19:18:38 | INFO     | app.services.compliance_service:process_file_stage:114 | 开始文件处理阶段 | ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c | 文件: 南区污水医废、生活垃圾一体化托管服务项目二次招标文件（发售版）.docx
2025-08-07 19:18:38 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 优化文件处理流程 | ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c | 详情: {}
2025-08-07 19:18:38 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 文件下载 | ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c | 详情: {}
2025-08-07 19:18:38 | INFO     | app.services.file_processor_v2:download_file:255 | 开始下载文件: http://localhost:9090/api/v1/download-shared-object/aHR0cDovLzEyNy4wLjAuMTo5MDAwL3poYW9iaWFvd2VuamlhbmppYW5jaGF6aHVzaG91LyVFNSU4RCU5NyVFNSU4QyVCQSVFNiVCMSVBMSVFNiVCMCVCNCVFNSU4QyVCQiVFNSVCQSU5RiVFMyU4MCU4MSVFNyU5NCU5RiVFNiVCNCVCQiVFNSU5RSU4MyVFNSU5QyVCRSVFNCVCOCU4MCVFNCVCRCU5MyVFNSU4QyU5NiVFNiU4OSU5OCVFNyVBRSVBMSVFNiU5QyU4RCVFNSU4QSVBMSVFOSVBMSVCOSVFNyU5QiVBRSVFNCVCQSU4QyVFNiVBQyVBMSVFNiU4QiU5QiVFNiVBMCU4NyVFNiU5NiU4NyVFNCVCQiVCNiVFRiVCQyU4OCVFNSU4RiU5MSVFNSU5NCVBRSVFNyU4OSU4OCVFRiVCQyU4OS5kb2N4P1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9MU1NT09WMTBXVzhKMjMyMFBSOUMlMkYyMDI1MDgwNyUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNTA4MDdUMDkxMDMyWiZYLUFtei1FeHBpcmVzPTQzMTk5JlgtQW16LVNlY3VyaXR5LVRva2VuPWV5SmhiR2NpT2lKSVV6VXhNaUlzSW5SNWNDSTZJa3BYVkNKOS5leUpoWTJObGMzTkxaWGtpT2lJeFRVMVBUMVl4TUZkWE9Fb3lNekl3VUZJNVF5SXNJbVY0Y0NJNk1UYzFORFUzTWpJeE1Td2ljR0Z5Wlc1MElqb2ljR1Z1WjJOb1lXNW5JbjAuejhoTXF3dTYtR3dNdHVqSy1zQnNuWmkxTGdFaTAyTVEyOHYzNXNmZHh4MS0yRnAwN0t3cy0yUjFoU2xUVWY1MjdvUU9oZDNRMTlQajY0YzJDZEwwRncmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0JnZlcnNpb25JZD1udWxsJlgtQW16LVNpZ25hdHVyZT0wYzc2MDQ2N2ZlYzQwMDkzN2Y4MDVjZjMzZDAwM2UwMzYwNDQzYjIyNDA1Mjk0Zjc4YTMwZjMzMGYyYTBmNDcy
2025-08-07 19:18:38 | INFO     | app.services.file_processor_v2:download_file:289 | 文件下载完成: 2362185 字节
2025-08-07 19:18:38 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 文件下载 | ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c | 状态: 成功 | 耗时: 0.063秒
2025-08-07 19:18:38 | WARNING  | app.services.file_processor_v2:process_file:515 | MarkItDown不可用，使用备用方案
2025-08-07 19:18:38 | WARNING  | app.services.file_processor_v2:process_file:519 | MarkItDown处理失败: MarkItDown不可用，使用备用方案
2025-08-07 19:18:38 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | DOCX备用提取 | ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c | 详情: {}
2025-08-07 19:18:39 | INFO     | app.services.file_processor_v2:extract_docx_content_fallback:401 | DOCX备用提取完成: 60369 字符
2025-08-07 19:18:39 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | DOCX备用提取 | ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c | 状态: 成功 | 耗时: 0.285秒
2025-08-07 19:18:39 | INFO     | app.core.logger:log_file_processing:159 | 文件处理 | ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c | 文件: 南区污水医废、生活垃圾一体化托管服务项目二次招标文件（发售版）.docx | 大小: 2362185 字节 | 处理时间: 0.000秒
2025-08-07 19:18:39 | INFO     | app.services.file_processor_v2:process_file:548 | 文件处理完成(备用方案): 南区污水医废、生活垃圾一体化托管服务项目二次招标文件（发售版）.docx, 输出长度: 60431 字符
2025-08-07 19:18:39 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 优化文件处理流程 | ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c | 状态: 成功 | 耗时: 0.349秒
2025-08-07 19:18:39 | INFO     | app.services.compliance_service:process_file_stage:131 | 文件处理完成 | ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c | 内容长度: 60431 字符
2025-08-07 19:18:39 | INFO     | app.core.logger:log_file_processing:159 | 文件处理 | ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c | 文件: 南区污水医废、生活垃圾一体化托管服务项目二次招标文件（发售版）.docx | 大小: 2362185 字节 | 处理时间: 0.000秒
2025-08-07 19:18:39 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | file_processing | ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c | 状态: 成功 | 耗时: 0.350秒
2025-08-07 19:18:39 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 文件处理阶段 | ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c | 状态: 成功 | 耗时: 0.350秒
2025-08-07 19:18:39 | INFO     | app.services.compliance_service:execute_pipeline:405 | 步骤3: 文件处理完成 | ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c | 内容长度: 60431
2025-08-07 19:18:39 | INFO     | app.services.compliance_service:execute_pipeline:416 | 步骤4: 开始AI合规性检查阶段 | ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c
2025-08-07 19:18:39 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | AI合规性检查阶段 | ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c | 详情: {}
2025-08-07 19:18:39 | INFO     | app.services.compliance_service:ai_compliance_check_stage:188 | 开始AI合规性检查 | ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c
2025-08-07 19:18:39 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | AI模型调用(尝试1) | ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c | 详情: {}
2025-08-07 19:18:39 | INFO     | app.services.ai_model_service:_call_model_with_retry:374 | 调用AI模型: gemini-2.5-flash, 消息数: 2, 尝试: 1/4 | 请求ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c
2025-08-07 19:19:24 | WARNING  | app.services.ai_model_service:_call_model_with_retry:385 | AI模型返回空响应 | 尝试: 1 | 请求ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c
2025-08-07 19:19:24 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | AI模型调用(尝试1) | ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c | 状态: 失败 | 耗时: 45.745秒
2025-08-07 19:19:24 | ERROR    | app.core.logger:__exit__:421 | 操作 AI模型调用(尝试1) 发生异常: AI模型返回空响应 (模型: gemini-2.5-flash, 错误码: EMPTY_RESPONSE)
2025-08-07 19:19:24 | WARNING  | app.services.ai_model_service:_call_model_with_retry:429 | AI模型调用失败，将在 1.0秒后重试 | 尝试: 1/4 | 错误: AI模型返回空响应 (模型: gemini-2.5-flash, 错误码: EMPTY_RESPONSE) | 请求ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c
2025-08-07 19:19:26 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | AI模型调用(尝试2) | ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c | 详情: {}
2025-08-07 19:19:26 | INFO     | app.services.ai_model_service:_call_model_with_retry:374 | 调用AI模型: gemini-2.5-flash, 消息数: 2, 尝试: 2/4 | 请求ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c
2025-08-07 19:20:08 | WARNING  | app.services.ai_model_service:_call_model_with_retry:385 | AI模型返回空响应 | 尝试: 2 | 请求ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c
2025-08-07 19:20:08 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | AI模型调用(尝试2) | ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c | 状态: 失败 | 耗时: 42.676秒
2025-08-07 19:20:08 | ERROR    | app.core.logger:__exit__:421 | 操作 AI模型调用(尝试2) 发生异常: AI模型返回空响应 (模型: gemini-2.5-flash, 错误码: EMPTY_RESPONSE)
2025-08-07 19:20:08 | WARNING  | app.services.ai_model_service:_call_model_with_retry:429 | AI模型调用失败，将在 2.0秒后重试 | 尝试: 2/4 | 错误: AI模型返回空响应 (模型: gemini-2.5-flash, 错误码: EMPTY_RESPONSE) | 请求ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c
2025-08-07 19:20:10 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | AI模型调用(尝试3) | ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c | 详情: {}
2025-08-07 19:20:10 | INFO     | app.services.ai_model_service:_call_model_with_retry:374 | 调用AI模型: gemini-2.5-flash, 消息数: 2, 尝试: 3/4 | 请求ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c
2025-08-07 19:20:52 | WARNING  | app.services.ai_model_service:_call_model_with_retry:385 | AI模型返回空响应 | 尝试: 3 | 请求ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c
2025-08-07 19:20:52 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | AI模型调用(尝试3) | ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c | 状态: 失败 | 耗时: 41.977秒
2025-08-07 19:20:52 | ERROR    | app.core.logger:__exit__:421 | 操作 AI模型调用(尝试3) 发生异常: AI模型返回空响应 (模型: gemini-2.5-flash, 错误码: EMPTY_RESPONSE)
2025-08-07 19:20:52 | WARNING  | app.services.ai_model_service:_call_model_with_retry:429 | AI模型调用失败，将在 4.0秒后重试 | 尝试: 3/4 | 错误: AI模型返回空响应 (模型: gemini-2.5-flash, 错误码: EMPTY_RESPONSE) | 请求ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c
2025-08-07 19:20:56 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | AI模型调用(尝试4) | ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c | 详情: {}
2025-08-07 19:20:56 | INFO     | app.services.ai_model_service:_call_model_with_retry:374 | 调用AI模型: gemini-2.5-flash, 消息数: 2, 尝试: 4/4 | 请求ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c
2025-08-07 19:21:37 | WARNING  | app.services.ai_model_service:_call_model_with_retry:385 | AI模型返回空响应 | 尝试: 4 | 请求ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c
2025-08-07 19:21:37 | INFO     | app.core.logger:log_api_call:170 | API调用 | OpenAI-gemini-2.5-flash | ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c | 状态: 失败 | 耗时: 0.000秒
2025-08-07 19:21:37 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | AI模型调用(尝试4) | ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c | 状态: 失败 | 耗时: 40.924秒
2025-08-07 19:21:37 | ERROR    | app.core.logger:__exit__:421 | 操作 AI模型调用(尝试4) 发生异常: AI模型返回空响应（重试后仍失败） (模型: gemini-2.5-flash, 错误码: EMPTY_RESPONSE_FINAL)
2025-08-07 19:21:37 | INFO     | app.core.logger:log_api_call:170 | API调用 | OpenAI-gemini-2.5-flash | ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c | 状态: 失败 | 耗时: 0.000秒
2025-08-07 19:21:37 | ERROR    | app.services.ai_model_service:_call_model_with_retry:469 | AI模型调用最终失败，已重试 3 次 | 最后错误: AI模型返回空响应（重试后仍失败） (模型: gemini-2.5-flash, 错误码: EMPTY_RESPONSE_FINAL) | 请求ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c
2025-08-07 19:21:37 | ERROR    | app.core.logger:wrapper:452 | 函数调用失败: call_model | 耗时: 178.332秒 | 错误: AI模型返回空响应（重试后仍失败） (模型: gemini-2.5-flash, 错误码: EMPTY_RESPONSE_FINAL)
2025-08-07 19:21:37 | ERROR    | app.services.ai_model_service:check_compliance:743 | AI模型错误 | 请求ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c | 错误: AI模型返回空响应（重试后仍失败） (模型: gemini-2.5-flash, 错误码: EMPTY_RESPONSE_FINAL)
2025-08-07 19:21:37 | ERROR    | app.core.logger:wrapper:452 | 函数调用失败: check_compliance | 耗时: 178.332秒 | 错误: AI模型返回空响应（重试后仍失败） (模型: gemini-2.5-flash, 错误码: EMPTY_RESPONSE_FINAL)
2025-08-07 19:21:37 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | AI合规性检查阶段 | ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c | 状态: 失败 | 耗时: 178.334秒
2025-08-07 19:21:37 | ERROR    | app.core.logger:__exit__:421 | 操作 AI合规性检查阶段 发生异常: AI模型返回空响应（重试后仍失败） (模型: gemini-2.5-flash, 错误码: EMPTY_RESPONSE_FINAL)
2025-08-07 19:21:37 | WARNING  | app.core.logger:record_stage_performance:204 | 性能警告 | 阶段: ai_model_call | ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c | 耗时: 178.340秒 > 阈值: 30.0秒
2025-08-07 19:21:37 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | ai_model_call | ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c | 状态: 失败 | 耗时: 178.340秒
2025-08-07 19:21:37 | ERROR    | app.core.logger:wrapper:452 | 函数调用失败: ai_compliance_check_stage | 耗时: 178.340秒 | 错误: AI合规性检查阶段失败: AI模型返回空响应（重试后仍失败） (模型: gemini-2.5-flash, 错误码: EMPTY_RESPONSE_FINAL)
2025-08-07 19:21:37 | WARNING  | app.services.compliance_service:execute_pipeline:427 | 步骤4: AI合规性检查异常 | ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c | 错误: AI合规性检查阶段失败: AI模型返回空响应（重试后仍失败） (模型: gemini-2.5-flash, 错误码: EMPTY_RESPONSE_FINAL)
2025-08-07 19:21:37 | WARNING  | app.services.compliance_service:execute_pipeline:431 | AI检查失败，使用降级策略 | ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c
2025-08-07 19:21:37 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 敏感词检测阶段 | ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c | 详情: {}
2025-08-07 19:21:37 | INFO     | app.services.compliance_service:sensitive_word_check_stage:245 | 开始敏感词检测 | ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c
2025-08-07 19:21:37 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 敏感词服务健康检查 | ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c | 详情: {}
2025-08-07 19:21:37 | INFO     | app.services.sensitive_word_service:check_health:121 | 检查敏感词服务健康状态: http://*************:8087/health
2025-08-07 19:21:37 | INFO     | app.services.sensitive_word_service:check_health:131 | 敏感词服务健康状态: 健康
2025-08-07 19:21:37 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 敏感词服务健康检查 | ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c | 状态: 成功 | 耗时: 0.007秒
2025-08-07 19:21:37 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 敏感词检测 | ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c | 详情: {}
2025-08-07 19:21:37 | INFO     | app.services.sensitive_word_service:detect_sensitive_words:213 | 开始敏感词检测: 内容长度=60431, 政府采购=True
2025-08-07 19:21:37 | INFO     | app.services.sensitive_word_service:detect_sensitive_words:222 | 敏感词检测超时设置: 60.0秒 | ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c
2025-08-07 19:21:37 | INFO     | app.services.sensitive_word_service:_convert_to_target_format:363 | 敏感词转换完成: 原始9个，有效9个
2025-08-07 19:21:37 | INFO     | app.core.logger:log_api_call:170 | API调用 | 敏感词检测API | ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c | 状态: 成功 | 耗时: 0.000秒
2025-08-07 19:21:37 | INFO     | app.services.sensitive_word_service:detect_sensitive_words:268 | 敏感词检测完成: 发现 9 个敏感词
2025-08-07 19:21:37 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 敏感词检测 | ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c | 状态: 成功 | 耗时: 0.022秒
2025-08-07 19:21:37 | INFO     | app.services.compliance_service:sensitive_word_check_stage:252 | 敏感词检测完成 | ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c | 发现敏感词: 9个
2025-08-07 19:21:37 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | sensitive_word | ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c | 状态: 成功 | 耗时: 0.029秒
2025-08-07 19:21:37 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 敏感词检测阶段 | ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c | 状态: 成功 | 耗时: 0.033秒
2025-08-07 19:21:37 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 结果聚合阶段 | ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c | 详情: {}
2025-08-07 19:21:37 | INFO     | app.services.compliance_service:result_aggregation_stage:305 | 开始结果聚合 | ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c
2025-08-07 19:21:37 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 结果聚合 | ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c | 详情: {}
2025-08-07 19:21:37 | INFO     | app.services.result_processor:aggregate_results:314 | 开始结果聚合: 敏感词9个，检查结果0个
2025-08-07 19:21:37 | INFO     | app.services.result_processor:validate_sensitive_words:107 | 敏感词验证完成: 原始9个，有效9个
2025-08-07 19:21:37 | INFO     | app.services.result_processor:deduplicate_sensitive_words:225 | 敏感词去重完成: 原始9个，去重后9个
2025-08-07 19:21:37 | INFO     | app.services.result_processor:validate_check_results:179 | 检查结果验证完成: 原始0个，有效0个
2025-08-07 19:21:37 | INFO     | app.services.result_processor:prioritize_check_results:263 | 检查结果排序完成: 0个结果
2025-08-07 19:21:37 | INFO     | app.services.result_processor:aggregate_results:345 | 结果聚合完成: 敏感词9个，检查结果0个
2025-08-07 19:21:37 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 结果聚合 | ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c | 状态: 成功 | 耗时: 0.002秒
2025-08-07 19:21:37 | INFO     | app.services.result_processor:validate_final_response:422 | 响应验证完成: 敏感词9个，检查结果0个
2025-08-07 19:21:37 | INFO     | app.services.compliance_service:result_aggregation_stage:312 | 结果聚合完成 | ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c | 敏感词: 9个 | 检查结果: 0个
2025-08-07 19:21:37 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | result_aggregation | ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c | 状态: 成功 | 耗时: 0.006秒
2025-08-07 19:21:37 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 结果聚合阶段 | ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c | 状态: 成功 | 耗时: 0.006秒
2025-08-07 19:21:37 | INFO     | app.services.compliance_service:_add_pipeline_metadata:538 | 流水线元数据 | ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c | 处理时间: 178.743秒 | 降级使用: True | 敏感词数量: 9 | 检查结果数量: 0
2025-08-07 19:21:37 | WARNING  | app.core.logger:record_stage_performance:204 | 性能警告 | 阶段: total_pipeline | ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c | 耗时: 178.743秒 > 阈值: 60.0秒
2025-08-07 19:21:37 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | total_pipeline | ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c | 状态: 成功 | 耗时: 178.743秒
2025-08-07 19:21:37 | INFO     | app.services.compliance_service:execute_pipeline:479 | 合规性检查流水线完成 | ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c | 耗时: 178.743秒 | 降级: True
2025-08-07 19:21:37 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 完整合规性检查流水线 | ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c | 状态: 成功 | 耗时: 178.744秒
2025-08-07 19:21:37 | INFO     | app.services.compliance_service:check_compliance:641 | 合规性检查服务完成 | ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c
2025-08-07 19:21:37 | INFO     | app.api.routes:check_compliance:93 | 合规性检查完成 | ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c | 敏感词: 9个 | 检查结果: 0个
2025-08-07 19:21:37 | INFO     | app.middleware.logging:dispatch:48 | 请求完成 | ID: 822a6348-06d9-4441-8ddc-3cc6a97fb29c | 状态码: 200 | 耗时: 178.751秒
