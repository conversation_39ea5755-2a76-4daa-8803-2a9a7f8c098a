# 招标文件合规性检查助手 API 文档

## 概述

招标文件合规性检查助手是一个基于AI大模型的API服务，用于自动检查招标文件的合规性。系统支持docx和pdf格式的文件，通过AI模型分析文件内容，识别合规性、逻辑性、风险管理、规范性、公平性、可操作性等问题，并调用敏感词检测服务，最终返回结构化的检查结果。

**新增功能**: 现在支持简化的API接口，只需提供文件URL即可自动推断文件信息。

## 基础信息

- **基础URL**: `http://localhost:8088`
- **API版本**: v1
- **支持格式**: JSON
- **文件格式**: docx, pdf
- **最大文件大小**: 300MB

## 认证

当前版本不需要认证，但建议在生产环境中添加适当的认证机制。

## 通用响应格式

### 成功响应
```json
{
  "success": true,
  "data": {},
  "message": "操作成功"
}
```

### 错误响应
```json
{
  "success": false,
  "error_code": "ERROR_CODE",
  "message": "错误描述",
  "details": {}
}
```

## API 端点

### 1. 健康检查

检查服务运行状态。

**端点**: `GET /health`

**响应示例**:
```json
{
  "status": "healthy",
  "service": "bidding-document-compliance-checker",
  "version": "1.0.0"
}
```

### 2. 获取枚举值

获取所有可用的枚举参数值。

**端点**: `GET /api/v1/enums`

**响应示例**:
```json
{
  "message": "枚举值列表",
  "data": {
    "procurement_project_type": ["工程类", "服务类", "货物类"],
    "project_category": ["依法招标", "非依法招标", "政府采购"],
    "bidding_procurement_method": [
      "公开招标", "单一来源", "竞争性磋商", "竞争性磋商邀请",
      "邀请招标", "竞争性谈判", "公开竞价", "邀请竞价",
      "询价", "其他", "比选"
    ]
  }
}
```

### 3. 服务状态

获取详细的服务状态信息。

**端点**: `GET /api/v1/service-status`

**响应示例**:
```json
{
  "status": "operational",
  "compliance_service": {
    "service_info": {
      "service_name": "bidding-document-compliance-checker",
      "version": "1.0.0",
      "supported_formats": [".docx", ".pdf"],
      "max_file_size_mb": 300
    },
    "health_status": {
      "file_processor": true,
      "ai_model": true,
      "sensitive_word": true,
      "result_processor": true
    },
    "pipeline_stats": {
      "total_requests": 10,
      "successful_requests": 8,
      "failed_requests": 2,
      "success_rate": 0.8
    }
  }
}
```

### 4. 合规性检查（主要接口）

对招标文件进行合规性检查。

**端点**: `POST /api/v1/check-compliance`

### 4.1 简化的合规性检查接口（推荐）

简化的招标文件合规性检查接口，只需提供文件URL，系统会自动推断文件信息。

**端点**: `POST /api/v1/check-compliance-simple`

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| procurement_project_type | string | 是 | 采购项目类型：工程类/服务类/货物类 |
| project_category | string | 是 | 项目类别：依法招标/非依法招标/政府采购 |
| bidding_procurement_method | string | 是 | 招标采购方式 |
| file_url | string | 是 | 文件下载URL |

**请求示例**:
```json
{
  "procurement_project_type": "服务类",
  "project_category": "政府采购",
  "bidding_procurement_method": "公开招标",
  "file_url": "http://localhost:9090/api/v1/download-shared-object/aHR0cDovLzEyNy4wLjAuMTo5MDAwL3poYW9iaWFvd2VuamlhbmppYW5jaGF6aHVzaG91LyVFNSU4RCU5NyVFNSU4QyVCQSVFNiVCMSVBMSVFNiVCMCVCNCVFNSU4QyVCQiVFNSVCQSU5RiVFMyU4MCU4MSVFNyU5NCU5RiVFNiVCNCVCQSVFNSU5RSU4MyVFNSU5QyVCRSVFNCVCOCU4MCVFNCVCRCU5MyVFNSU4QyU5NiVFNiU4OSU5OCVFNyVBRSVBMSVFNiU5QyU4RCVFNSU4QSVBMSVFOSVBMSVCOSVFNyU5QiVBRSVFNCVCQSU4QyVFNiVBQyVBMSVFNiU4QiU5QiVFNiVBMCU4NyVFNiU5NiU4NyVFNCVCQiVCNiVFRiVCQyU4OCVFNSU4RiU5MSVFNSU5NCVBRSVFNyU4OSU4OCVFRiVCQyU4OS5kb2N4P1gtQW16LUFsZ29yaXRobT1BV1M0LUhNQUMtU0hBMjU2JlgtQW16LUNyZWRlbnRpYWw9RVpTNjlMMTlFNjhDS1dSSzVKMTQlMkYyMDI1MDgwOCUyRnVzLWVhc3QtMSUyRnMzJTJGYXdzNF9yZXF1ZXN0JlgtQW16LURhdGU9MjAyNTA4MDhUMDEyMDA0WiZYLUFtei1FeHBpcmVzPTQzMTk4JlgtQW16LVNlY3VyaXR5LVRva2VuPWV5SmhiR2NpT2lKSVV6VXhNaUlzSW5SNWNDSTZJa3BYVkNKOS5leUpoWTJObGMzTkxaWGtpT2lKRldsTTJPVXd4T1VVMk9FTkxWMUpMTlVveE5DSXNJbVY0Y0NJNk1UYzFORFkxT1RBek15d2ljR0Z5Wlc1MElqb2ljR1Z1WjJOb1lXNW5JbjAuQXBLbDZqTHVDVmZlWnJiazJpUjBEYmw5d0xCUWVzVURGTnRRUjB3cWg0UjllaGc0bFc4MmlKcnJPb2RLaGdiUTBUY1c5N1hjeE11MHhMMTV6clpRTGcmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0JnZlcnNpb25JZD1udWxsJlgtQW16LVNpZ25hdHVyZT00ZDUyMTFhYzE0ZWM5ZmMyYzdkZDMxNGQzNGVhOGRlYzhlZmY5MTJiZmNkNTQzYzM1NDBiNzIyM2MxY2UxZGRk"
}
```

**响应示例**: 与标准接口相同

### 4.2 标准合规性检查接口

**端点**: `POST /api/v1/check-compliance`

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| procurement_project_type | string | 是 | 采购项目类型：工程类/服务类/货物类 |
| project_category | string | 是 | 项目类别：依法招标/非依法招标/政府采购 |
| bidding_procurement_method | string | 是 | 招标采购方式 |
| bidding_doc | object | 是 | 文件信息对象 |

**文件信息对象 (bidding_doc)**:

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| filename | string | 是 | 文件名 |
| extension | string | 是 | 文件扩展名：.docx/.pdf |
| mime_type | string | 是 | MIME类型 |
| size | integer | 是 | 文件大小（字节） |
| url | string | 是 | 文件下载URL |

**请求示例**:
```json
{
  "procurement_project_type": "货物类",
  "project_category": "政府采购",
  "bidding_procurement_method": "公开招标",
  "bidding_doc": {
    "filename": "招标文件.docx",
    "extension": ".docx",
    "mime_type": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    "size": 1048576,
    "url": "https://example.com/files/招标文件.docx"
  }
}
```

**响应示例**:
```json
{
  "sensitiveWordsArr": [
    {
      "type": "商业敏感",
      "content": "独家供应",
      "num": 1
    },
    {
      "type": "地域限制",
      "content": "本地企业",
      "num": 2
    }
  ],
  "checkResultArr": [
    {
      "quesType": "合规性",
      "quesDesc": "技术参数设置过于具体，可能限制竞争",
      "originalArr": [
        "设备必须为某某品牌型号ABC-123",
        "供应商必须具有本地服务网点"
      ],
      "point": "技术参数应具有通用性，不应指定特定品牌",
      "advice": "建议修改为功能性技术要求，删除品牌限制条款"
    },
    {
      "quesType": "公平性",
      "quesDesc": "评分标准存在主观性过强的条款",
      "originalArr": [
        "综合实力评价占30分"
      ],
      "point": "评分标准应客观、量化、可操作",
      "advice": "建议将综合实力评价细化为具体的量化指标"
    }
  ]
}
```

### 5. 文件验证

验证文件信息和处理能力。

**端点**: `POST /api/v1/validate-file`

**请求参数**: 与合规性检查中的 `bidding_doc` 对象相同

**响应示例**:
```json
{
  "file_info": {
    "filename": "test.docx",
    "extension": ".docx",
    "mime_type": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    "size": 1048576,
    "url": "https://example.com/test.docx"
  },
  "processing_capability": {
    "can_process": true,
    "estimated_time": 30.5,
    "warnings": [],
    "file_type_supported": true,
    "size_acceptable": true
  }
}
```

### 6. 敏感词服务统计

获取敏感词检测服务的统计信息。

**端点**: `GET /api/v1/sensitive-word-stats`

**响应示例**:
```json
{
  "health": true,
  "stats": {
    "total_detections": 150,
    "total_sensitive_words": 45,
    "average_response_time": 0.8
  },
  "service_info": {
    "api_url": "http://*************:8087",
    "version": "1.0.0",
    "status": "operational"
  }
}
```

### 7. 重载敏感词库

重新加载敏感词库。

**端点**: `POST /api/v1/reload-sensitive-words`

**响应示例**:
```json
{
  "success": true,
  "message": "敏感词库重载成功"
}
```

### 8. 处理指标

获取系统处理指标和性能监控数据。

**端点**: `GET /api/v1/processing-metrics`

**响应示例**:
```json
{
  "message": "处理指标获取成功",
  "data": {
    "pipeline_metrics": {
      "total_requests": 100,
      "successful_requests": 95,
      "failed_requests": 5,
      "average_processing_time": 25.3,
      "success_rate": 0.95
    },
    "service_health": {
      "file_processor": true,
      "ai_model": true,
      "sensitive_word": true,
      "result_processor": true
    },
    "component_info": {
      "file_processor": {
        "processed_files": 95,
        "average_processing_time": 5.2
      },
      "ai_model": {
        "total_calls": 95,
        "average_response_time": 15.8,
        "success_rate": 0.98
      }
    }
  }
}
```

## 错误代码

| 错误代码 | HTTP状态码 | 描述 |
|----------|------------|------|
| VALIDATION_ERROR | 400 | 请求参数验证失败 |
| FILE_PROCESSING_ERROR | 400 | 文件处理失败 |
| AI_MODEL_ERROR | 500 | AI模型调用失败 |
| EXTERNAL_SERVICE_ERROR | 502 | 外部服务调用失败 |
| RATE_LIMIT_EXCEEDED | 429 | 请求频率超限 |
| INTERNAL_SERVER_ERROR | 500 | 内部服务器错误 |

## 使用限制

### 请求频率限制
- 默认限制：100次请求/小时
- 超出限制时返回429状态码
- 响应头包含限流信息：
  - `X-RateLimit-Limit`: 限制数量
  - `X-RateLimit-Remaining`: 剩余次数
  - `X-RateLimit-Reset`: 重置时间

### 文件大小限制
- 最大文件大小：300MB
- 支持格式：.docx, .pdf
- 文件必须可通过HTTP下载

### 超时设置
- 请求超时：300秒（5分钟）
- 文件下载超时：60秒
- AI模型调用超时：120秒

## 最佳实践

### 1. 错误处理
```javascript
try {
  const response = await fetch('/api/v1/check-compliance', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(requestData)
  });
  
  if (!response.ok) {
    const error = await response.json();
    console.error('API错误:', error.message);
    return;
  }
  
  const result = await response.json();
  // 处理成功响应
} catch (error) {
  console.error('网络错误:', error);
}
```

### 2. 文件URL要求
- 文件URL必须可公开访问
- 建议使用HTTPS协议
- 确保文件服务器支持Range请求（用于大文件下载）

### 3. 性能优化建议
- 对于重复文件，系统会自动缓存处理结果
- 建议在业务高峰期外进行大批量处理
- 可通过 `/api/v1/service-status` 监控系统负载

### 4. 监控和日志
- 所有请求都会生成唯一的请求ID
- 可通过日志文件追踪请求处理过程
- 建议定期检查 `/api/v1/processing-metrics` 了解系统性能

## 示例代码

### Python示例
```python
import requests
import json

def check_compliance(file_info, project_info):
    url = "http://localhost:8088/api/v1/check-compliance"
    
    payload = {
        "procurement_project_type": project_info["type"],
        "project_category": project_info["category"],
        "bidding_procurement_method": project_info["method"],
        "bidding_doc": file_info
    }
    
    try:
        response = requests.post(url, json=payload, timeout=300)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"请求失败: {e}")
        return None

# 使用示例
file_info = {
    "filename": "招标文件.docx",
    "extension": ".docx",
    "mime_type": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    "size": 1048576,
    "url": "https://example.com/files/招标文件.docx"
}

project_info = {
    "type": "货物类",
    "category": "政府采购",
    "method": "公开招标"
}

result = check_compliance(file_info, project_info)
if result:
    print(f"发现敏感词: {len(result['sensitiveWordsArr'])}个")
    print(f"检查问题: {len(result['checkResultArr'])}个")
```

### JavaScript示例
```javascript
async function checkCompliance(fileInfo, projectInfo) {
  const url = 'http://localhost:8088/api/v1/check-compliance';
  
  const payload = {
    procurement_project_type: projectInfo.type,
    project_category: projectInfo.category,
    bidding_procurement_method: projectInfo.method,
    bidding_doc: fileInfo
  };
  
  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(payload)
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('请求失败:', error);
    return null;
  }
}

// 使用示例
const fileInfo = {
  filename: '招标文件.docx',
  extension: '.docx',
  mime_type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  size: 1048576,
  url: 'https://example.com/files/招标文件.docx'
};

const projectInfo = {
  type: '货物类',
  category: '政府采购',
  method: '公开招标'
};

checkCompliance(fileInfo, projectInfo).then(result => {
  if (result) {
    console.log(`发现敏感词: ${result.sensitiveWordsArr.length}个`);
    console.log(`检查问题: ${result.checkResultArr.length}个`);
  }
});
```

## 常见问题

### Q: 支持哪些文件格式？
A: 目前支持 .docx 和 .pdf 格式的文件。

### Q: 文件大小有限制吗？
A: 是的，最大支持300MB的文件。

### Q: 处理一个文件需要多长时间？
A: 通常在30-60秒内完成，具体时间取决于文件大小和复杂度。

### Q: 如何处理超时错误？
A: 如果遇到超时，可以稍后重试。系统会缓存处理结果，重复请求会更快。

### Q: 敏感词检测的准确性如何？
A: 敏感词检测基于专业的词库，准确率较高，但建议结合人工审核。

### Q: 可以批量处理文件吗？
A: 当前版本需要逐个文件处理，建议控制并发数量以避免超出限流限制。

## 更新日志

### v1.0.0 (2025-08-08)
- 初始版本发布
- 支持docx和pdf文件处理
- 集成AI模型进行合规性检查
- 集成敏感词检测服务
- 提供完整的REST API接口
- 支持性能监控和统计