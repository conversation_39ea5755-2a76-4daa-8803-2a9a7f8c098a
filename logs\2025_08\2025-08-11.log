2025-08-11 10:59:19 | INFO     | app.middleware.validation:_validate_request:75 | 跳过中间件验证 | ID: 0d36c210-cf14-4739-b6c0-fdca7e888375 | 路径: /api/v1/check-compliance-simple
2025-08-11 10:59:19 | INFO     | app.api.routes:check_compliance_simple:125 | 开始简化合规性检查 | ID: 0d36c210-cf14-4739-b6c0-fdca7e888375 | URL: https://test-fds.xinecai.com/group1/M00/02/8F/rBIKBWiZXGyAOPxYACHumRgYULE457.doc?token=2f972578697a92047addff55377d9cbf&ts=1754881147
2025-08-11 10:59:19 | INFO     | app.utils.file_info_utils:infer_file_info_from_url:154 | 开始从URL推断文件信息: https://test-fds.xinecai.com/group1/M00/02/8F/rBIKBWiZXGyAOPxYACHumRgYULE457.doc?token=2f972578697a92047addff55377d9cbf&ts=1754881147
2025-08-11 10:59:19 | INFO     | app.utils.file_info_utils:infer_file_info_from_url:158 | 推断的文件名: rBIKBWiZXGyAOPxYACHumRgYULE457.doc
2025-08-11 10:59:19 | WARNING  | app.utils.file_info_utils:infer_file_extension_and_mime_type:107 | 未识别的文件扩展名: .doc，默认使用docx格式
2025-08-11 10:59:19 | INFO     | app.utils.file_info_utils:infer_file_info_from_url:162 | 推断的扩展名: .docx, MIME类型: application/vnd.openxmlformats-officedocument.wordprocessingml.document
2025-08-11 10:59:20 | INFO     | app.utils.file_info_utils:infer_file_info_from_url:171 | 获取的文件大小: 2223769 字节
2025-08-11 10:59:20 | INFO     | app.utils.file_info_utils:infer_file_info_from_url:182 | 文件信息推断完成: rBIKBWiZXGyAOPxYACHumRgYULE457.doc
2025-08-11 10:59:20 | INFO     | app.api.routes:check_compliance_simple:131 | 文件信息推断成功 | ID: 0d36c210-cf14-4739-b6c0-fdca7e888375 | 文件: rBIKBWiZXGyAOPxYACHumRgYULE457.doc
2025-08-11 10:59:20 | ERROR    | app.api.routes:check_compliance_simple:166 | 简化合规性检查失败 | ID: 0d36c210-cf14-4739-b6c0-fdca7e888375 | 错误: 1 validation error for ComplianceCheckRequest
project_category
  Input should be '政府采购', '非政府采购' or '非依法招标' [type=enum, input_value='依法招标', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/enum
2025-08-11 10:59:20 | ERROR    | app.middleware.validation:dispatch:55 | 请求验证异常 | ID: 0d36c210-cf14-4739-b6c0-fdca7e888375 | 错误: 1 validation error for ComplianceCheckRequest
project_category
  Input should be '政府采购', '非政府采购' or '非依法招标' [type=enum, input_value='依法招标', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/enum
2025-08-11 10:59:20 | INFO     | app.middleware.logging:dispatch:48 | 请求完成 | ID: 0d36c210-cf14-4739-b6c0-fdca7e888375 | 状态码: 400 | 耗时: 1.414秒
2025-08-11 11:02:52 | INFO     | app.middleware.logging:dispatch:35 | 请求开始 | ID: 4b252d29-17ad-46ac-9ff4-e6b595029ed7 | 方法: POST | URL: http://***********:8088/api/v1/check-compliance-simple | 客户端IP: ************ | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/75.0.3770.142 Safari/537.36 Hutool
2025-08-11 11:02:52 | INFO     | app.middleware.validation:_validate_request:75 | 跳过中间件验证 | ID: 4b252d29-17ad-46ac-9ff4-e6b595029ed7 | 路径: /api/v1/check-compliance-simple
2025-08-11 11:02:52 | INFO     | app.api.routes:check_compliance_simple:125 | 开始简化合规性检查 | ID: 4b252d29-17ad-46ac-9ff4-e6b595029ed7 | URL: https://test-fds.xinecai.com/group1/M00/02/8F/rBIKBmiZXUSARGmGACHulHUUsVs790.doc?token=53d5127d4f2e56b351bce1f7d8af5a08&ts=1754881369
2025-08-11 11:02:52 | INFO     | app.utils.file_info_utils:infer_file_info_from_url:154 | 开始从URL推断文件信息: https://test-fds.xinecai.com/group1/M00/02/8F/rBIKBmiZXUSARGmGACHulHUUsVs790.doc?token=53d5127d4f2e56b351bce1f7d8af5a08&ts=1754881369
2025-08-11 11:02:52 | INFO     | app.utils.file_info_utils:infer_file_info_from_url:158 | 推断的文件名: rBIKBmiZXUSARGmGACHulHUUsVs790.doc
2025-08-11 11:02:52 | WARNING  | app.utils.file_info_utils:infer_file_extension_and_mime_type:107 | 未识别的文件扩展名: .doc，默认使用docx格式
2025-08-11 11:02:52 | INFO     | app.utils.file_info_utils:infer_file_info_from_url:162 | 推断的扩展名: .docx, MIME类型: application/vnd.openxmlformats-officedocument.wordprocessingml.document
2025-08-11 11:02:54 | INFO     | app.utils.file_info_utils:infer_file_info_from_url:171 | 获取的文件大小: 2223764 字节
2025-08-11 11:02:54 | INFO     | app.utils.file_info_utils:infer_file_info_from_url:182 | 文件信息推断完成: rBIKBmiZXUSARGmGACHulHUUsVs790.doc
2025-08-11 11:02:54 | INFO     | app.api.routes:check_compliance_simple:131 | 文件信息推断成功 | ID: 4b252d29-17ad-46ac-9ff4-e6b595029ed7 | 文件: rBIKBmiZXUSARGmGACHulHUUsVs790.doc
2025-08-11 11:02:54 | ERROR    | app.api.routes:check_compliance_simple:166 | 简化合规性检查失败 | ID: 4b252d29-17ad-46ac-9ff4-e6b595029ed7 | 错误: 1 validation error for ComplianceCheckRequest
project_category
  Input should be '政府采购', '非政府采购' or '非依法招标' [type=enum, input_value='依法招标', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/enum
2025-08-11 11:02:54 | ERROR    | app.middleware.validation:dispatch:55 | 请求验证异常 | ID: 4b252d29-17ad-46ac-9ff4-e6b595029ed7 | 错误: 1 validation error for ComplianceCheckRequest
project_category
  Input should be '政府采购', '非政府采购' or '非依法招标' [type=enum, input_value='依法招标', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/enum
2025-08-11 11:02:54 | INFO     | app.middleware.logging:dispatch:48 | 请求完成 | ID: 4b252d29-17ad-46ac-9ff4-e6b595029ed7 | 状态码: 400 | 耗时: 1.408秒
2025-08-11 11:06:53 | INFO     | app.middleware.logging:dispatch:35 | 请求开始 | ID: d4da9454-3c15-48e9-9ebd-087049196ec8 | 方法: POST | URL: http://***********:8088/api/v1/check-compliance-simple | 客户端IP: ************ | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/75.0.3770.142 Safari/537.36 Hutool
2025-08-11 11:06:53 | INFO     | app.middleware.validation:_validate_request:75 | 跳过中间件验证 | ID: d4da9454-3c15-48e9-9ebd-087049196ec8 | 路径: /api/v1/check-compliance-simple
2025-08-11 11:06:53 | INFO     | app.api.routes:check_compliance_simple:125 | 开始简化合规性检查 | ID: d4da9454-3c15-48e9-9ebd-087049196ec8 | URL: https://test-fds.xinecai.com/group1/M00/02/8F/rBIKBWiZXeuASu24ACHukxnb5Jo740.doc?token=11a09701e8d4fb589897ac5a20150253&ts=1754881529
2025-08-11 11:06:53 | INFO     | app.utils.file_info_utils:infer_file_info_from_url:154 | 开始从URL推断文件信息: https://test-fds.xinecai.com/group1/M00/02/8F/rBIKBWiZXeuASu24ACHukxnb5Jo740.doc?token=11a09701e8d4fb589897ac5a20150253&ts=1754881529
2025-08-11 11:06:53 | INFO     | app.utils.file_info_utils:infer_file_info_from_url:158 | 推断的文件名: rBIKBWiZXeuASu24ACHukxnb5Jo740.doc
2025-08-11 11:06:53 | WARNING  | app.utils.file_info_utils:infer_file_extension_and_mime_type:107 | 未识别的文件扩展名: .doc，默认使用docx格式
2025-08-11 11:06:53 | INFO     | app.utils.file_info_utils:infer_file_info_from_url:162 | 推断的扩展名: .docx, MIME类型: application/vnd.openxmlformats-officedocument.wordprocessingml.document
2025-08-11 11:06:55 | INFO     | app.utils.file_info_utils:infer_file_info_from_url:171 | 获取的文件大小: 2223763 字节
2025-08-11 11:06:55 | INFO     | app.utils.file_info_utils:infer_file_info_from_url:182 | 文件信息推断完成: rBIKBWiZXeuASu24ACHukxnb5Jo740.doc
2025-08-11 11:06:55 | INFO     | app.api.routes:check_compliance_simple:131 | 文件信息推断成功 | ID: d4da9454-3c15-48e9-9ebd-087049196ec8 | 文件: rBIKBWiZXeuASu24ACHukxnb5Jo740.doc
2025-08-11 11:06:55 | ERROR    | app.api.routes:check_compliance_simple:166 | 简化合规性检查失败 | ID: d4da9454-3c15-48e9-9ebd-087049196ec8 | 错误: 1 validation error for ComplianceCheckRequest
project_category
  Input should be '政府采购', '非政府采购' or '非依法招标' [type=enum, input_value='依法招标', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/enum
2025-08-11 11:06:55 | ERROR    | app.middleware.validation:dispatch:55 | 请求验证异常 | ID: d4da9454-3c15-48e9-9ebd-087049196ec8 | 错误: 1 validation error for ComplianceCheckRequest
project_category
  Input should be '政府采购', '非政府采购' or '非依法招标' [type=enum, input_value='依法招标', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/enum
2025-08-11 11:06:55 | INFO     | app.middleware.logging:dispatch:48 | 请求完成 | ID: d4da9454-3c15-48e9-9ebd-087049196ec8 | 状态码: 400 | 耗时: 1.199秒
2025-08-11 11:27:58 | INFO     | app.middleware.logging:dispatch:35 | 请求开始 | ID: 7b55afb2-e55e-40e8-ac10-c2ce58dad1e7 | 方法: POST | URL: http://***********:8088/api/v1/check-compliance-simple | 客户端IP: ************ | User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/75.0.3770.142 Safari/537.36 Hutool
2025-08-11 11:27:58 | INFO     | app.middleware.validation:_validate_request:75 | 跳过中间件验证 | ID: 7b55afb2-e55e-40e8-ac10-c2ce58dad1e7 | 路径: /api/v1/check-compliance-simple
2025-08-11 11:27:58 | INFO     | app.api.routes:check_compliance_simple:125 | 开始简化合规性检查 | ID: 7b55afb2-e55e-40e8-ac10-c2ce58dad1e7 | URL: https://test-fds.xinecai.com/group1/M00/02/8F/rBIKBmiZYy-AepOUACHuksoAtb8817.doc?token=8014169c60c88ddc02849a3fc6f313fc&ts=1754882876
2025-08-11 11:27:58 | INFO     | app.utils.file_info_utils:infer_file_info_from_url:154 | 开始从URL推断文件信息: https://test-fds.xinecai.com/group1/M00/02/8F/rBIKBmiZYy-AepOUACHuksoAtb8817.doc?token=8014169c60c88ddc02849a3fc6f313fc&ts=1754882876
2025-08-11 11:27:58 | INFO     | app.utils.file_info_utils:infer_file_info_from_url:158 | 推断的文件名: rBIKBmiZYy-AepOUACHuksoAtb8817.doc
2025-08-11 11:27:58 | WARNING  | app.utils.file_info_utils:infer_file_extension_and_mime_type:107 | 未识别的文件扩展名: .doc，默认使用docx格式
2025-08-11 11:27:58 | INFO     | app.utils.file_info_utils:infer_file_info_from_url:162 | 推断的扩展名: .docx, MIME类型: application/vnd.openxmlformats-officedocument.wordprocessingml.document
2025-08-11 11:27:59 | INFO     | app.utils.file_info_utils:infer_file_info_from_url:171 | 获取的文件大小: 2223762 字节
2025-08-11 11:27:59 | INFO     | app.utils.file_info_utils:infer_file_info_from_url:182 | 文件信息推断完成: rBIKBmiZYy-AepOUACHuksoAtb8817.doc
2025-08-11 11:27:59 | INFO     | app.api.routes:check_compliance_simple:131 | 文件信息推断成功 | ID: 7b55afb2-e55e-40e8-ac10-c2ce58dad1e7 | 文件: rBIKBmiZYy-AepOUACHuksoAtb8817.doc
2025-08-11 11:27:59 | INFO     | app.services.compliance_service:check_compliance:636 | 合规性检查服务开始 | ID: 7b55afb2-e55e-40e8-ac10-c2ce58dad1e7
2025-08-11 11:27:59 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 完整合规性检查流水线 | ID: 7b55afb2-e55e-40e8-ac10-c2ce58dad1e7 | 详情: {}
2025-08-11 11:27:59 | INFO     | app.services.compliance_service:execute_pipeline:376 | 开始执行合规性检查流水线 | ID: 7b55afb2-e55e-40e8-ac10-c2ce58dad1e7 | 文件: rBIKBmiZYy-AepOUACHuksoAtb8817.doc
2025-08-11 11:27:59 | INFO     | app.services.compliance_service:execute_pipeline:386 | 步骤1: 开始验证服务前置条件 | ID: 7b55afb2-e55e-40e8-ac10-c2ce58dad1e7
2025-08-11 11:27:59 | INFO     | app.services.compliance_service:validate_prerequisites:73 | 开始验证服务前置条件 | ID: 7b55afb2-e55e-40e8-ac10-c2ce58dad1e7
2025-08-11 11:27:59 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 敏感词服务健康检查 | ID: 7b55afb2-e55e-40e8-ac10-c2ce58dad1e7 | 详情: {}
2025-08-11 11:27:59 | INFO     | app.services.sensitive_word_service:check_health:122 | 检查敏感词服务健康状态: http://*************:8087/health
2025-08-11 11:27:59 | INFO     | app.services.sensitive_word_service:check_health:132 | 敏感词服务健康状态: 健康
2025-08-11 11:27:59 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 敏感词服务健康检查 | ID: 7b55afb2-e55e-40e8-ac10-c2ce58dad1e7 | 状态: 成功 | 耗时: 0.035秒
2025-08-11 11:27:59 | INFO     | app.services.compliance_service:validate_prerequisites:92 | 服务健康检查完成 | ID: 7b55afb2-e55e-40e8-ac10-c2ce58dad1e7 | 状态: {'file_processor': True, 'ai_model': True, 'sensitive_word': True, 'result_processor': True}
2025-08-11 11:27:59 | INFO     | app.services.compliance_service:execute_pipeline:388 | 步骤1: 服务前置条件验证完成 | ID: 7b55afb2-e55e-40e8-ac10-c2ce58dad1e7 | 状态: {'file_processor': True, 'ai_model': True, 'sensitive_word': True, 'result_processor': True}
2025-08-11 11:27:59 | INFO     | app.services.compliance_service:execute_pipeline:394 | 步骤2: 开始获取项目信息 | ID: 7b55afb2-e55e-40e8-ac10-c2ce58dad1e7
2025-08-11 11:27:59 | INFO     | app.services.compliance_service:execute_pipeline:396 | 步骤2: 项目信息获取完成 | ID: 7b55afb2-e55e-40e8-ac10-c2ce58dad1e7
2025-08-11 11:27:59 | INFO     | app.services.compliance_service:execute_pipeline:400 | 步骤3: 开始文件处理阶段 | ID: 7b55afb2-e55e-40e8-ac10-c2ce58dad1e7
2025-08-11 11:27:59 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 文件处理阶段 | ID: 7b55afb2-e55e-40e8-ac10-c2ce58dad1e7 | 详情: {}
2025-08-11 11:27:59 | INFO     | app.services.compliance_service:process_file_stage:114 | 开始文件处理阶段 | ID: 7b55afb2-e55e-40e8-ac10-c2ce58dad1e7 | 文件: rBIKBmiZYy-AepOUACHuksoAtb8817.doc
2025-08-11 11:27:59 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 优化文件处理流程 | ID: 7b55afb2-e55e-40e8-ac10-c2ce58dad1e7 | 详情: {}
2025-08-11 11:27:59 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 文件下载 | ID: 7b55afb2-e55e-40e8-ac10-c2ce58dad1e7 | 详情: {}
2025-08-11 11:27:59 | INFO     | app.services.file_processor_v2:download_file:255 | 开始下载文件: https://test-fds.xinecai.com/group1/M00/02/8F/rBIKBmiZYy-AepOUACHuksoAtb8817.doc?token=8014169c60c88ddc02849a3fc6f313fc&ts=1754882876
2025-08-11 11:28:00 | INFO     | app.services.file_processor_v2:download_file:289 | 文件下载完成: 2223762 字节
2025-08-11 11:28:00 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 文件下载 | ID: 7b55afb2-e55e-40e8-ac10-c2ce58dad1e7 | 状态: 成功 | 耗时: 1.375秒
2025-08-11 11:28:00 | WARNING  | app.services.file_processor_v2:process_file:515 | MarkItDown不可用，使用备用方案
2025-08-11 11:28:00 | WARNING  | app.services.file_processor_v2:process_file:519 | MarkItDown处理失败: MarkItDown不可用，使用备用方案
2025-08-11 11:28:00 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | DOCX备用提取 | ID: 7b55afb2-e55e-40e8-ac10-c2ce58dad1e7 | 详情: {}
2025-08-11 11:28:00 | INFO     | app.services.file_processor_v2:extract_docx_content_fallback:401 | DOCX备用提取完成: 60374 字符
2025-08-11 11:28:00 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | DOCX备用提取 | ID: 7b55afb2-e55e-40e8-ac10-c2ce58dad1e7 | 状态: 成功 | 耗时: 0.242秒
2025-08-11 11:28:00 | INFO     | app.core.logger:log_file_processing:159 | 文件处理 | ID: 7b55afb2-e55e-40e8-ac10-c2ce58dad1e7 | 文件: rBIKBmiZYy-AepOUACHuksoAtb8817.doc | 大小: 2223762 字节 | 处理时间: 0.000秒
2025-08-11 11:28:00 | INFO     | app.services.file_processor_v2:process_file:548 | 文件处理完成(备用方案): rBIKBmiZYy-AepOUACHuksoAtb8817.doc, 输出长度: 60434 字符
2025-08-11 11:28:00 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 优化文件处理流程 | ID: 7b55afb2-e55e-40e8-ac10-c2ce58dad1e7 | 状态: 成功 | 耗时: 1.617秒
2025-08-11 11:28:00 | INFO     | app.services.compliance_service:process_file_stage:131 | 文件处理完成 | ID: 7b55afb2-e55e-40e8-ac10-c2ce58dad1e7 | 内容长度: 60434 字符
2025-08-11 11:28:00 | INFO     | app.core.logger:log_file_processing:159 | 文件处理 | ID: 7b55afb2-e55e-40e8-ac10-c2ce58dad1e7 | 文件: rBIKBmiZYy-AepOUACHuksoAtb8817.doc | 大小: 2223762 字节 | 处理时间: 0.000秒
2025-08-11 11:28:00 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | file_processing | ID: 7b55afb2-e55e-40e8-ac10-c2ce58dad1e7 | 状态: 成功 | 耗时: 1.617秒
2025-08-11 11:28:00 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 文件处理阶段 | ID: 7b55afb2-e55e-40e8-ac10-c2ce58dad1e7 | 状态: 成功 | 耗时: 1.617秒
2025-08-11 11:28:00 | INFO     | app.services.compliance_service:execute_pipeline:405 | 步骤3: 文件处理完成 | ID: 7b55afb2-e55e-40e8-ac10-c2ce58dad1e7 | 内容长度: 60434
2025-08-11 11:28:00 | INFO     | app.services.compliance_service:execute_pipeline:416 | 步骤4: 开始AI合规性检查阶段 | ID: 7b55afb2-e55e-40e8-ac10-c2ce58dad1e7
2025-08-11 11:28:00 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | AI合规性检查阶段 | ID: 7b55afb2-e55e-40e8-ac10-c2ce58dad1e7 | 详情: {}
2025-08-11 11:28:00 | INFO     | app.services.compliance_service:ai_compliance_check_stage:188 | 开始AI合规性检查 | ID: 7b55afb2-e55e-40e8-ac10-c2ce58dad1e7
2025-08-11 11:28:00 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | AI模型调用(尝试1) | ID: 7b55afb2-e55e-40e8-ac10-c2ce58dad1e7 | 详情: {}
2025-08-11 11:28:00 | INFO     | app.services.ai_model_service:_call_model_with_retry:648 | 调用AI模型: gemini-2.5-flash, 消息数: 2, 尝试: 1/4 | 请求ID: 7b55afb2-e55e-40e8-ac10-c2ce58dad1e7
2025-08-11 11:29:06 | INFO     | app.core.logger:log_api_call:170 | API调用 | OpenAI-gemini-2.5-flash | ID: 7b55afb2-e55e-40e8-ac10-c2ce58dad1e7 | 状态: 成功 | 耗时: 0.000秒
2025-08-11 11:29:06 | INFO     | app.services.ai_model_service:_call_model_with_retry:687 | AI模型调用成功: 响应长度 8561 字符, 尝试次数: 1 | 请求ID: 7b55afb2-e55e-40e8-ac10-c2ce58dad1e7
2025-08-11 11:29:06 | WARNING  | app.services.ai_model_service:_call_model_with_retry:691 | AI模型原始响应内容: '```json
{
  "checkResultArr": [
    {
      "quesType": "规范性",
      "quesDesc": "招标文件封面标题中包含多余文字，影响专业性和规范性。原文条款：招标文件封面“安徽理工大学第一附属医院南区污水医废、生活垃圾一体化托管服务项目二次阿萨德”",
      "originalArr": [
        "安徽理工大学第一附属医院南区污水医废、生活垃圾一体化托管服务项目二次阿萨德"
      ],
      "point": "文件标题的准确性和规范性。",
      "advice": "删除封面标题中多余的“阿萨德”文字，确保标题准确无误。"
    },
    {
      "quesType": "逻辑性",
      "quesDesc": "第三章 投标人须知 2.5条中“近X年内”的定义未明确具体年限，导致条款含义模糊。原文条款：第三章 投标人须知 2.5 “近X年内：按招标文件要求，没作要求的为自开标之日往前追溯X年。”",
      "originalArr": [
        "2.5近X年内：按招标文件要求，没作要求的为自开标之日往前追溯X年。"
      ],
      "point": "条款定义的清晰性和可操作性。",
      "advice": "明确“X”的具体年限，例如修改为“近3年内”或“近5年内”，以消除歧义。"
    },
    {
      "quesType": "规范性",
      "quesDesc": "第三章 投标人须知 5.1、5.2、5.3条中多次出现“供货现场”、“供货使用地”等描述，与本项目服务类性质不符。原文条款：第三章 投标人须知 5.1 “投标人应自行对供货现场和周围环境进行勘察...”；5.2 “招标人向投标人提供的有关供货现场的资料和数据...”；5.3 “除非有特殊要求，招标文件不单独提供供货使用地的自然环境、气候条件、公用设施等情况...”",
      "originalArr": [
        "5.1投标人应自行对供货现场和周围环境进行勘察，以获取编制投标文件和签署合同所需的资料。",
        "5.2勘察现场所发生的费用由投标人自行承担。招标人向投标人提供的有关供货现场的资料和数据，是招标人现有的能使投标人利用的资料。招标人对投标人由此而做出的推论、理解和结论概不负责。投标人未到供货现场实地踏勘的，中标后签订合同时和履约过程中，不得以不完全了解现场情况为由，提出任何形式的增加合同价款或索赔的要求。",
        "5.3除非有特殊要求，招标文件不单独提供供货使用地的自然环境、气候条件、公用设施等情况，投标人被视为熟悉上述与履行合同有关的一切情况。"
      ],
      "point": "招标文件描述与项目性质的一致性。",
      "advice": "将“供货现场”、“供货使用地”等词语修改为“服务现场”、“服务地点”或“项目现场”等，使其与服务类项目性质保持一致。"
    },
    {
      "quesType": "规范性",
      "quesDesc": "第三章 投标人须知 15.8条将本项目描述为“交钥匙工程”，此表述通常用于工程建设类项目，与本项目服务类性质不符。原文条款：第三章 投标人须知 15.8 “本项目为交钥匙工程，报价应包括所有发生的全部费用。除政策性文件规定以外，投标人所报价格在合同实施期间不因市场变化因素而变动。”",
      "originalArr": [
        "15.8本项目为交钥匙工程，报价应包括所有发生的全部费用。除政策性文件规定以外，投标人所报价格在合同实施期间不因市场变化因素而变动。"
      ],
      "point": "项目性质描述的准确性。",
      "advice": "删除“本项目为交钥匙工程”的描述，或修改为更符合服务类项目性质的表述，如“本项目为整体托管服务项目”。"
    },
    {
      "quesType": "合规性",
      "quesDesc": "第三章 投标人须知 23.8 (1) 和 (2) 条款关于流标后处理方式的规定与公开招标原则存在冲突，且表述不清晰。公开招标项目投标人不足三家时，通常应重新招标，而非直接转为磋商。原文条款：第三章 投标人须知 23.8 (1) “投标人不足三家处理预案。发布过二次招标公告或二次招标（重新招标）的项目，在投标文件递交截止时间后出现投标人不足三家情形的，为保证本项目的采购进度，经现场投标人、招标人同意后，可采用磋商方式继续采购...”；23.8 (2) “对于未发布或发布过二次招标公告的项目（非政府采购项目）如果评标期间出现符合资格条件的投标人或者对招标文件作实质响应的投标人不足三家情形的，如有两家有效投标人进入最终详细评审，且评标委员会认为具有一定的竞争性的，将按照招标文件规定的评审办法进行评审；对于未发布过二次招标公告的项目（非政府采购项目）如进入最终详细评审的投标人只有一家，招标人将进行重新招标。”",
      "originalArr": [
        "23.8 (1) 投标人不足三家处理预案。发布过二次招标公告或二次招标（重新招标）的项目，在投标文件递交截止时间后出现投标人不足三家情形的，为保证本项目的采购进度，经现场投标人、招标人同意后，可采用磋商方式继续采购，其投标保证金转为磋商保证金。资格性审查或符合性审查不合格的投标人不得参加磋商。",
        "23.8 (2) 对于未发布或发布过二次招标公告的项目（非政府采购项目）如果评标期间出现符合资格条件的投标人或者对招标文件作实质响应的投标人不足三家情形的，如有两家有效投标人进入最终详细评审，且评标委员会认为具有一定的竞争性的，将按照招标文件规定的评审办法进行评审；对于未发布过二次招标公告的项目（非政府采购项目）如进入最终详细评审的投标人只有一家，招标人将进行重新招标。"
      ],
      "point": "流标处理方式的合规性和逻辑一致性。",
      "advice": "整合并明确流标后的处理方式，避免逻辑冲突和重复。对于公开招标项目，应遵循《招标投标法》关于流标后重新招标的规定。如果确实需要采用磋商方式，应在招标文件中明确其依据和适用条件，并确保符合相关法律法规。"
    },
    {
      "quesType": "公平性",
      "quesDesc": "第三章 投标人须知 25.1 (3) 和第五章 评标方法 二、11 备注中关于投标文件和证明材料的描述过于主观，可能导致评标委员会自由裁量权过大，影响评审公平性。原文条款：第三章 投标人须知 25.1 (3) “未按规定的格式填写，编排混乱、无序，内容不全或字迹模糊，辨认不清”；第五章 评标方法 二、11 备注 “投标文件中提供的证明材料（文字、图片）不完整、模糊不清、不准确、有遮挡等，造成评标委员会的误评、错评及不利的评审后果由投标人自行承担。”",
      "originalArr": [
        "25.1 (3) 未按规定的格式填写，编排混乱、无序，内容不全或字迹模糊，辨认不清；",
        "第五章 评标方法 二、11 备注：1.投标文件中提供的证明材料（文字、图片）不完整、模糊不清、不准确、有遮挡等，造成评标委员会的误评、错评及不利的评审后果由投标人自行承担。"
      ],
      "point": "评审标准的客观性和可操作性。",
      "advice": "细化或量化“编排混乱、无序，内容不全或字迹模糊，辨认不清”、“不完整、模糊不清、不准确、有遮挡等”的具体标准，例如规定关键信息缺失、无法识别等，以减少主观判断，确保评审的客观公正。"
    },
    {
      "quesType": "公平性",
      "quesDesc": "第三章 投标人须知 25.2 (12)、(13)、(14) 和第八章 电子招投标相关要求 51.4 (12) 条款关于串通投标的认定标准过于宽泛和主观，可能导致误判或不公平的废标。原文条款：第三章 投标人须知 25.2 (12) “不同投标人的投标文件的编制人或审核人为同一人或同一单位人员的”；(13) “不同投标人的投标文件由同一人或同一单位人员提交的”；(14) “不同投标人的投标文件以及密封包装出现了评标委员会认为不应当雷同的情况”；第八章 电子招投标相关要求 51.4 (12) “投标人投标报价与公布的最高投标限价（控制价）相比降幅过小，明显缺乏竞争性，有围标、串标嫌疑的。”",
      "originalArr": [
        "25.2 (12) 不同投标人的投标文件的编制人或审核人为同一人或同一单位人员的；",
        "25.2 (13) 不同投标人的投标文件由同一人或同一单位人员提交的；",
        "25.2 (14) 不同投标人的投标文件以及密封包装出现了评标委员会认为不应当雷同的情况；",
        "第八章 电子招投标相关要求 51.4 (12) 投标人投标报价与公布的最高投标限价（控制价）相比降幅过小，明显缺乏竞争性，有围标、串标嫌疑的。"
      ],
      "point": "串通投标认定标准的客观性和合法性。",
      "advice": "删除过于主观的串通投标认定标准，或提供更具体、可量化的判断依据，并结合其他证据综合判断，避免争议和不当废标。应严格依据《招标投标法实施条例》等法律法规规定的串通投标情形进行认定。"
    },
    {
      "quesType": "公平性",
      "quesDesc": "第三章 投标人须知 26.3.3条规定评标过程中的各细项评审内容、分项得分和总分属于保密内容，招标人无义务公布，可能影响投标人提出有效异议的权利。原文条款：第三章 投标人须知 26.3.3 “各投标人的各细项评审内容、分项得分和总分均属于需保密的评标过程内容，招标人和招标代理机构无义务向各投标人公布。”",
      "originalArr": [
        "26.3.3各投标人的各细项评审内容、分项得分和总分均属于需保密的评标过程内容，招标人和招标代理机构无义务向各投标人公布。"
      ],
      "point": "投标人知情权和异议权的保障。",
      "advice": "在不泄露商业秘密的前提下，建议适当公开部分评审信息，例如公布中标候选人的综合得分和排名，或在异议处理时提供必要的解释，以保障投标人的知情权和异议权。"
    },
    {
      "quesType": "合规性",
      "quesDesc": "第三章 投标人须知 30.4条规定中标人未在30日内签订合同将取消中标资格并没收保证金，但未明确是否因中标人自身原因导致，可能不符合《招标投标法实施条例》相关规定。原文条款：第三章 投标人须知 30.4 “中标人接到中标通知书30日内没有和招标人签订合同的，将取消其中标资格，其投标保证金将不予退还。并按照招标文件规定推荐下一个中标候选人为中标人或进行重新招标。”",
      "originalArr": [
        "30.4中标人接到中标通知书30日内没有和招标人签订合同的，将取消其中标资格，其投标保证金将不予退还。并按照招标文件规定推荐下一个中标候选人为中标人或进行重新招标。"
      ],
      "point": "合同签订条款的合规性和合理性。",
      "advice": "明确“中标人无正当理由”未在规定时间内签订合同的，才取消其中标资格并没收保证金，以符合法律法规规定。"
    },
    {
      "quesType": "逻辑性",
      "quesDesc": "第四章 服务要求 (一) 2.2.3条和附件2 3.条关于污泥处置费用的承担方表述不一致，可能引起歧义。原文条款：第四章 服务要求 (一) 2.2.3 “涉及危险废物的处置费用由运维单位代为支付。”；第四章 附件2 3. “关于污泥、废液、活性炭处理项目，因医院为产废单位，根据相关文件要求必须由医院签订处置合同，故届时由医院牵头签订三方合同，中标单位支付处置费用，投标人报价时需自行考虑。”",
      "originalArr": [
        "第四章 服务要求 (一) 2.2.3 污水处理站污泥处置要求：...涉及危险废物的处置费用由运维单位代为支付。",
        "第四章 附件2 3. 关于污泥、废液、活性炭处理项目，因医院为产废单位，根据相关文件要求必须由医院签订处置合同，故届时由医院牵头签订三方合同，中标单位支付处置费用，投标人报价时需自行考虑。"
      ],
      "point": "费用承担条款的清晰性和一致性。",
      "advice": "统一表述，明确污泥处置费用的最终承担方和支付方式，例如统一为“由中标单位承担并支付，并在投标报价中予以考虑”。"
    },
    {
      "quesType": "公平性",
      "quesDesc": "第四章 服务要求 (一) 2.2.5 (3) 条规定采购人抽检费用由中标人承担，这可能增加中标人的不确定成本，且不符合一般合同惯例。原文条款：第四章 服务要求 (一) 2.2.5 (3) “采购人抽检：采购人有权不定期自行或委托有CMA资质的第三方对出水水质进行检测（此部分费用由中标人承担），服务期内累计三次不达标的，采购人有权单方面解除合同，并且不需要支付任何赔偿或补偿。”",
      "originalArr": [
        "第四章 服务要求 (一) 2.2.5 (3) 采购人抽检：采购人有权不定期自行或委托有CMA资质的第三方对出水水质进行检测（此部分费用由中标人承担），服务期内累计三次不达标的，采购人有权单方面解除合同，并且不需要支付任何赔偿或补偿。"
      ],
      "point": "检测费用承担的合理性。",
      "advice": "修改为“此部分费用由采购人承担”，或明确只有在中标人服务不达标的情况下，由此产生的检测费用才由中标人承担。"
    },
    {
      "quesType": "逻辑性",
      "quesDesc": "第四章 服务要求 (一) 3.1条中关于超过1000元设备或配件的维保费用承担界定不清晰，存在“配件价格为配件货物本身价格，不含安装费用”与“中标人负责安装维修调试”的矛盾。原文条款：第四章 服务要求 (一) 3.1 “维保过程中1000 元（含）以内的设备或配件，费用包含在总报价中，并由中标人安装维修调试；超过 1000 元（不含）以上的设备或配件，由采购人采购，采购费用由采购人承担，中标人负责安装维修调试。配件价格参照采购时间的市场价格，配件价格为配件货物本身价格，不含安装费用；采购人、中标人对配件价格有分歧时，双方协商认可充分询价材料或举证材料。”",
      "originalArr": [
        "第四章 服务要求 (一) 3.1 完善设备、设施资料，制定设备、设施维修保养制度，并严格执各种设备要精心保养维护，保持其处于最佳工作状态。维保过程中1000 元（含）以内的设备或配件，费用包含在总报价中，并由中标人安装维修调试；超过 1000 元（不含）以上的设备或配件，由采购人采购，采购费用由采购人承担，中标人负责安装维修调试。配件价格参照采购时间的市场价格，配件价格为配件货物本身价格，不含安装费用；采购人、中标人对配件价格有分歧时，双方协商认可充分询价材料或举证材料。"
      ],
      "point": "维保费用承担的清晰性。",
      "advice": "明确超过1000元（不含）以上设备或配件的安装维修调试费用由谁承担，是包含在总报价中还是由采购人承担。"
    },
    {
      "quesType": "公平性",
      "quesDesc": "第四章 服务要求 (一) 3.16条要求提供“最近3个月社保证明材料”，并规定“未提供视为不响应文件，按无效响应处理”，此要求过于严苛，可能限制部分投标人的参与。原文条款：第四章 服务要求 (一) 3.16 “注：本条款所涉及人员，供应商须提供其相关证件扫描件及为其缴纳的最近3个月社保证明材料，未提供视为不响应文件，按无效响应处理。”",
      "originalArr": [
        "第四章 服务要求 (一) 3.16 负责污水处理站运营托管的人员在进行有限空间作业时必须不少于两人，修理时须有2人（含）及以上人员在场，并有应急救援人员在场，应急救援人员需具有人社部门颁发的相关证书。\n注：本条款所涉及人员，供应商须提供其相关证件扫描件及为其缴纳的最近3个月社保证明材料，未提供视为不响应文件，按无效响应处理。"
      ],
      "point": "人员资质证明要求的合理性和公平性。",
      "advice": "考虑放宽社保证明的时间要求，例如“近一年内任意连续三个月”或“提供劳动合同及社保证明”，并对未提供社保证明的处理方式进行调整，例如扣分而非直接无效。"
    },
    {
      "quesType": "规范性",
      "quesDesc": "第八章 电子招投标相关要求 51.9条关于投标专用章、业务专用章等效力规定表述不严谨，可能导致对印章效力的不当质疑。原文条款：第八章 电子招投标相关要求 51.9 “在有授权文件(原件)表明投标专用章、业务专用章等法律效力等同于投标人公章的情况下，可以加盖投标专用章或业务专用章，否则将导致投标无效。”",
      "originalArr": [
        "第八章 电子招投标相关要求 51.9 招标文件中明确要求加盖公章的，投标人必须加盖投标人公章。在有授权文件(原件)表明投标专用章、业务专用章等法律效力等同于投标人公章的情况下，可以加盖投标专用章或业务专用章，否则将导致投标无效。"
      ],
      "point": "印章使用效力规定的严谨性。",
      "advice": "明确印章的使用范围和效力，例如“投标专用章或业务专用章需经工商备案或有明确的内部授权文件规定其效力等同于公章，并在投标文件中提供相关证明”。或者直接要求必须加盖公章，避免歧义。"
    },
    {
      "quesType": "公平性",
      "quesDesc": "第八章 电子招投标相关要求 51.11 (5) 条要求投标人发送弃标函并阐明原因，并对未及时发送弃标函的投标人进行惩罚，此要求可能限制投标人的自由投标权，且惩罚措施缺乏法律依据。原文条款：第八章 电子招投标相关要求 51.11 (5) “已领取招标文件的投标人，如确定放弃此次投标，须至少于开标前三个工作日发送弃标函且阐明弃标原因（格式自拟）发送至招标代理机构邮箱（见招标公告），并以电话方式告知。若在开标前临时弃标，且未及时发送弃标函影响开标造成不良后果者，招标人有权拒绝其（6个月内）参加医院任何招标采购项目的投标。”",
      "originalArr": [
        "第八章 电子招投标相关要求 51.11 (5) 已领取招标文件的投标人，如确定放弃此次投标，须至少于开标前三个工作日发送弃标函且阐明弃标原因（格式自拟）发送至招标代理机构邮箱（见招标公告），并以电话方式告知。若在开标前临时弃标，且未及时发送弃标函影响开标造成不良后果者，招标人有权拒绝其（6个月内）参加医院任何招标采购项目的投标。"
      ],
      "point": "投标人权利的保障和惩罚措施的合法性。",
      "advice": "删除此条款，或修改为“鼓励投标人发送弃标函，以便招标人了解情况”，但不应强制或进行惩罚，以保障投标人的合法权益。"
    }
  ]
}
```'
2025-08-11 11:29:06 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | AI模型调用(尝试1) | ID: 7b55afb2-e55e-40e8-ac10-c2ce58dad1e7 | 状态: 成功 | 耗时: 65.972秒
2025-08-11 11:29:06 | INFO     | app.services.ai_model_service:check_compliance:1018 | 合规性检查完成，发现 15 个问题
2025-08-11 11:29:06 | INFO     | app.services.compliance_service:ai_compliance_check_stage:195 | AI合规性检查完成 | ID: 7b55afb2-e55e-40e8-ac10-c2ce58dad1e7 | 发现问题: 15个
2025-08-11 11:29:06 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | ai_model_call | ID: 7b55afb2-e55e-40e8-ac10-c2ce58dad1e7 | 状态: 成功 | 耗时: 65.974秒
2025-08-11 11:29:06 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | AI合规性检查阶段 | ID: 7b55afb2-e55e-40e8-ac10-c2ce58dad1e7 | 状态: 成功 | 耗时: 65.974秒
2025-08-11 11:29:06 | INFO     | app.services.compliance_service:execute_pipeline:423 | 步骤4: AI合规性检查完成 | ID: 7b55afb2-e55e-40e8-ac10-c2ce58dad1e7 | 结果数量: 15
2025-08-11 11:29:06 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 敏感词检测阶段 | ID: 7b55afb2-e55e-40e8-ac10-c2ce58dad1e7 | 详情: {}
2025-08-11 11:29:06 | INFO     | app.services.compliance_service:sensitive_word_check_stage:245 | 开始敏感词检测 | ID: 7b55afb2-e55e-40e8-ac10-c2ce58dad1e7
2025-08-11 11:29:06 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 敏感词服务健康检查 | ID: 7b55afb2-e55e-40e8-ac10-c2ce58dad1e7 | 详情: {}
2025-08-11 11:29:06 | INFO     | app.services.sensitive_word_service:check_health:122 | 检查敏感词服务健康状态: http://*************:8087/health
2025-08-11 11:29:06 | INFO     | app.services.sensitive_word_service:check_health:132 | 敏感词服务健康状态: 健康
2025-08-11 11:29:06 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 敏感词服务健康检查 | ID: 7b55afb2-e55e-40e8-ac10-c2ce58dad1e7 | 状态: 成功 | 耗时: 0.028秒
2025-08-11 11:29:06 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 敏感词检测 | ID: 7b55afb2-e55e-40e8-ac10-c2ce58dad1e7 | 详情: {}
2025-08-11 11:29:06 | INFO     | app.services.sensitive_word_service:detect_sensitive_words:214 | 开始敏感词检测: 内容长度=60434, 政府采购=False
2025-08-11 11:29:06 | INFO     | app.services.sensitive_word_service:detect_sensitive_words:223 | 敏感词检测超时设置: 60.0秒 | ID: 7b55afb2-e55e-40e8-ac10-c2ce58dad1e7
2025-08-11 11:29:06 | INFO     | app.services.sensitive_word_service:_convert_to_target_format:376 | 敏感词转换完成: 原始5个，有效5个
2025-08-11 11:29:06 | INFO     | app.core.logger:log_api_call:170 | API调用 | 敏感词检测API | ID: 7b55afb2-e55e-40e8-ac10-c2ce58dad1e7 | 状态: 成功 | 耗时: 0.000秒
2025-08-11 11:29:06 | INFO     | app.services.sensitive_word_service:detect_sensitive_words:269 | 敏感词检测完成: 发现 5 个敏感词
2025-08-11 11:29:06 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 敏感词检测 | ID: 7b55afb2-e55e-40e8-ac10-c2ce58dad1e7 | 状态: 成功 | 耗时: 0.029秒
2025-08-11 11:29:06 | INFO     | app.services.compliance_service:sensitive_word_check_stage:252 | 敏感词检测完成 | ID: 7b55afb2-e55e-40e8-ac10-c2ce58dad1e7 | 发现敏感词: 5个
2025-08-11 11:29:06 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | sensitive_word | ID: 7b55afb2-e55e-40e8-ac10-c2ce58dad1e7 | 状态: 成功 | 耗时: 0.057秒
2025-08-11 11:29:06 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 敏感词检测阶段 | ID: 7b55afb2-e55e-40e8-ac10-c2ce58dad1e7 | 状态: 成功 | 耗时: 0.057秒
2025-08-11 11:29:06 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 结果聚合阶段 | ID: 7b55afb2-e55e-40e8-ac10-c2ce58dad1e7 | 详情: {}
2025-08-11 11:29:06 | INFO     | app.services.compliance_service:result_aggregation_stage:305 | 开始结果聚合 | ID: 7b55afb2-e55e-40e8-ac10-c2ce58dad1e7
2025-08-11 11:29:06 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 结果聚合 | ID: 7b55afb2-e55e-40e8-ac10-c2ce58dad1e7 | 详情: {}
2025-08-11 11:29:06 | INFO     | app.services.result_processor:aggregate_results:323 | 开始结果聚合: 敏感词5个，检查结果15个 | ID: 7b55afb2-e55e-40e8-ac10-c2ce58dad1e7
2025-08-11 11:29:06 | INFO     | app.services.result_processor:validate_sensitive_words:109 | 敏感词验证完成: 原始5个，有效5个
2025-08-11 11:29:06 | INFO     | app.services.result_processor:deduplicate_sensitive_words:234 | 敏感词去重完成: 原始5个，去重后5个
2025-08-11 11:29:06 | INFO     | app.services.result_processor:_verify_deduplication_integrity:943 | 去重完整性验证 | ID: 7b55afb2-e55e-40e8-ac10-c2ce58dad1e7 | 状态: 完美 | 输入: 5个 | 输出: 5个 | 出现次数变化: 118 -> 118
2025-08-11 11:29:06 | INFO     | app.services.result_processor:validate_check_results:188 | 检查结果验证完成: 原始15个，有效15个
2025-08-11 11:29:06 | INFO     | app.services.result_processor:prioritize_check_results:272 | 检查结果排序完成: 15个结果
2025-08-11 11:29:06 | INFO     | app.services.result_processor:_verify_final_integrity:743 | 完整性报告 | ID: 7b55afb2-e55e-40e8-ac10-c2ce58dad1e7 | 敏感词保留率: 100.0% | 检查结果保留率: 100.0%
2025-08-11 11:29:06 | INFO     | app.services.result_processor:aggregate_results:385 | 结果聚合完成: 敏感词5个，检查结果15个 | ID: 7b55afb2-e55e-40e8-ac10-c2ce58dad1e7
2025-08-11 11:29:06 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 结果聚合 | ID: 7b55afb2-e55e-40e8-ac10-c2ce58dad1e7 | 状态: 成功 | 耗时: 0.005秒
2025-08-11 11:29:06 | INFO     | app.services.result_processor:validate_final_response:479 | 响应验证完成: 敏感词5个，检查结果15个
2025-08-11 11:29:06 | INFO     | app.services.compliance_service:result_aggregation_stage:312 | 结果聚合完成 | ID: 7b55afb2-e55e-40e8-ac10-c2ce58dad1e7 | 敏感词: 5个 | 检查结果: 15个
2025-08-11 11:29:06 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | result_aggregation | ID: 7b55afb2-e55e-40e8-ac10-c2ce58dad1e7 | 状态: 成功 | 耗时: 0.006秒
2025-08-11 11:29:06 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 结果聚合阶段 | ID: 7b55afb2-e55e-40e8-ac10-c2ce58dad1e7 | 状态: 成功 | 耗时: 0.006秒
2025-08-11 11:29:06 | INFO     | app.services.compliance_service:_add_pipeline_metadata:538 | 流水线元数据 | ID: 7b55afb2-e55e-40e8-ac10-c2ce58dad1e7 | 处理时间: 67.696秒 | 降级使用: False | 敏感词数量: 5 | 检查结果数量: 15
2025-08-11 11:29:06 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | total_pipeline | ID: 7b55afb2-e55e-40e8-ac10-c2ce58dad1e7 | 状态: 成功 | 耗时: 67.696秒
2025-08-11 11:29:06 | INFO     | app.services.compliance_service:execute_pipeline:479 | 合规性检查流水线完成 | ID: 7b55afb2-e55e-40e8-ac10-c2ce58dad1e7 | 耗时: 67.696秒 | 降级: False
2025-08-11 11:29:06 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 完整合规性检查流水线 | ID: 7b55afb2-e55e-40e8-ac10-c2ce58dad1e7 | 状态: 成功 | 耗时: 67.698秒
2025-08-11 11:29:06 | INFO     | app.services.compliance_service:check_compliance:641 | 合规性检查服务完成 | ID: 7b55afb2-e55e-40e8-ac10-c2ce58dad1e7
2025-08-11 11:29:06 | INFO     | app.api.routes:check_compliance_simple:156 | 简化合规性检查完成 | ID: 7b55afb2-e55e-40e8-ac10-c2ce58dad1e7 | 文件: rBIKBmiZYy-AepOUACHuksoAtb8817.doc | 敏感词: 5个 | 检查结果: 15个
2025-08-11 11:29:06 | INFO     | app.middleware.logging:dispatch:48 | 请求完成 | ID: 7b55afb2-e55e-40e8-ac10-c2ce58dad1e7 | 状态码: 200 | 耗时: 68.883秒
2025-08-11 11:40:22 | INFO     | main:lifespan:47 | 招标文件合规性检查助手关闭中...
2025-08-11 11:40:22 | INFO     | main:lifespan:51 | 停止异步请求队列...
2025-08-11 11:40:22 | INFO     | app.core.queue_manager:stop_workers:125 | 停止请求队列工作线程
2025-08-11 11:40:22 | INFO     | app.core.queue_manager:_worker:296 | 工作线程被取消: worker-0
2025-08-11 11:40:22 | INFO     | app.core.queue_manager:_worker:301 | 工作线程停止: worker-0
2025-08-11 11:40:22 | INFO     | app.core.queue_manager:_worker:296 | 工作线程被取消: worker-1
2025-08-11 11:40:22 | INFO     | app.core.queue_manager:_worker:301 | 工作线程停止: worker-1
2025-08-11 11:40:22 | INFO     | app.core.queue_manager:_worker:296 | 工作线程被取消: worker-2
2025-08-11 11:40:22 | INFO     | app.core.queue_manager:_worker:301 | 工作线程停止: worker-2
2025-08-11 11:40:22 | INFO     | app.core.queue_manager:_worker:296 | 工作线程被取消: worker-3
2025-08-11 11:40:22 | INFO     | app.core.queue_manager:_worker:301 | 工作线程停止: worker-3
2025-08-11 11:40:22 | INFO     | main:lifespan:53 | 请求队列已停止
2025-08-11 11:40:22 | INFO     | app.core.cache_manager:clear:198 | 缓存已清空
2025-08-11 11:40:22 | INFO     | main:lifespan:58 | 缓存已清理
2025-08-11 11:40:22 | INFO     | main:lifespan:60 | 招标文件合规性检查助手已关闭
2025-08-11 11:40:26 | INFO     | app.services.ai_model_service:_initialize_client:80 | AI模型客户端初始化成功: gemini-2.5-flash
2025-08-11 11:40:26 | INFO     | main:lifespan:26 | 招标文件合规性检查助手启动中...
2025-08-11 11:40:26 | INFO     | main:lifespan:27 | 配置信息: 模型=gemini-2.5-flash, 环境=development
2025-08-11 11:40:26 | INFO     | main:lifespan:31 | 启动异步请求队列...
2025-08-11 11:40:26 | INFO     | app.core.queue_manager:start_workers:117 | 启动请求队列工作线程: 4个
2025-08-11 11:40:26 | INFO     | main:lifespan:33 | 请求队列已启动，工作线程数: 4
2025-08-11 11:40:26 | INFO     | main:lifespan:37 | 文档缓存已启用，最大大小: 100MB
2025-08-11 11:40:26 | INFO     | main:lifespan:40 | 内存阈值: 2048MB, 自动GC: True
2025-08-11 11:40:26 | INFO     | app.core.queue_manager:_worker:266 | 工作线程启动: worker-0
2025-08-11 11:40:26 | INFO     | app.core.queue_manager:_worker:266 | 工作线程启动: worker-1
2025-08-11 11:40:26 | INFO     | app.core.queue_manager:_worker:266 | 工作线程启动: worker-2
2025-08-11 11:40:26 | INFO     | app.core.queue_manager:_worker:266 | 工作线程启动: worker-3
2025-08-11 11:40:56 | INFO     | app.middleware.logging:dispatch:35 | 请求开始 | ID: 20b51477-8374-4059-8f81-5a3c50d1c381 | 方法: POST | URL: http://localhost:8088/api/v1/check-compliance-simple | 客户端IP: 127.0.0.1 | User-Agent: Apifox/1.0.0 (https://apifox.com)
2025-08-11 11:40:56 | INFO     | app.middleware.validation:_validate_request:75 | 跳过中间件验证 | ID: 20b51477-8374-4059-8f81-5a3c50d1c381 | 路径: /api/v1/check-compliance-simple
2025-08-11 11:40:56 | INFO     | app.middleware.logging:dispatch:48 | 请求完成 | ID: 20b51477-8374-4059-8f81-5a3c50d1c381 | 状态码: 422 | 耗时: 0.005秒
2025-08-11 11:41:12 | INFO     | app.middleware.logging:dispatch:35 | 请求开始 | ID: f1557241-829c-400c-abd7-9abb0291b869 | 方法: POST | URL: http://localhost:8088/api/v1/check-compliance-simple | 客户端IP: 127.0.0.1 | User-Agent: Apifox/1.0.0 (https://apifox.com)
2025-08-11 11:41:12 | INFO     | app.middleware.validation:_validate_request:75 | 跳过中间件验证 | ID: f1557241-829c-400c-abd7-9abb0291b869 | 路径: /api/v1/check-compliance-simple
2025-08-11 11:41:12 | INFO     | app.middleware.logging:dispatch:48 | 请求完成 | ID: f1557241-829c-400c-abd7-9abb0291b869 | 状态码: 422 | 耗时: 0.003秒
2025-08-11 11:41:28 | INFO     | app.middleware.logging:dispatch:35 | 请求开始 | ID: ab7abca3-cf91-48a8-bc22-4d1b14d9497c | 方法: POST | URL: http://localhost:8088/api/v1/check-compliance-simple | 客户端IP: 127.0.0.1 | User-Agent: Apifox/1.0.0 (https://apifox.com)
2025-08-11 11:41:28 | INFO     | app.middleware.validation:_validate_request:75 | 跳过中间件验证 | ID: ab7abca3-cf91-48a8-bc22-4d1b14d9497c | 路径: /api/v1/check-compliance-simple
2025-08-11 11:41:28 | INFO     | app.api.routes:check_compliance_simple:125 | 开始简化合规性检查 | ID: ab7abca3-cf91-48a8-bc22-4d1b14d9497c | URL: https://test-fds.xinecai.com/group1/M00/02/8F/rBIKBmiZXUSARGmGACHulHUUsVs790.doc?token=53d5127d4f2e56b351bce1f7d8af5a08&ts=1754881369
2025-08-11 11:41:28 | INFO     | app.utils.file_info_utils:infer_file_info_from_url:154 | 开始从URL推断文件信息: https://test-fds.xinecai.com/group1/M00/02/8F/rBIKBmiZXUSARGmGACHulHUUsVs790.doc?token=53d5127d4f2e56b351bce1f7d8af5a08&ts=1754881369
2025-08-11 11:41:28 | INFO     | app.utils.file_info_utils:infer_file_info_from_url:158 | 推断的文件名: rBIKBmiZXUSARGmGACHulHUUsVs790.doc
2025-08-11 11:41:28 | WARNING  | app.utils.file_info_utils:infer_file_extension_and_mime_type:107 | 未识别的文件扩展名: .doc，默认使用docx格式
2025-08-11 11:41:28 | INFO     | app.utils.file_info_utils:infer_file_info_from_url:162 | 推断的扩展名: .docx, MIME类型: application/vnd.openxmlformats-officedocument.wordprocessingml.document
2025-08-11 11:41:30 | INFO     | app.utils.file_info_utils:infer_file_info_from_url:171 | 获取的文件大小: 2223764 字节
2025-08-11 11:41:30 | INFO     | app.utils.file_info_utils:infer_file_info_from_url:182 | 文件信息推断完成: rBIKBmiZXUSARGmGACHulHUUsVs790.doc
2025-08-11 11:41:30 | INFO     | app.api.routes:check_compliance_simple:131 | 文件信息推断成功 | ID: ab7abca3-cf91-48a8-bc22-4d1b14d9497c | 文件: rBIKBmiZXUSARGmGACHulHUUsVs790.doc
2025-08-11 11:41:30 | INFO     | app.services.compliance_service:check_compliance:636 | 合规性检查服务开始 | ID: ab7abca3-cf91-48a8-bc22-4d1b14d9497c
2025-08-11 11:41:30 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 完整合规性检查流水线 | ID: ab7abca3-cf91-48a8-bc22-4d1b14d9497c | 详情: {}
2025-08-11 11:41:30 | INFO     | app.services.compliance_service:execute_pipeline:376 | 开始执行合规性检查流水线 | ID: ab7abca3-cf91-48a8-bc22-4d1b14d9497c | 文件: rBIKBmiZXUSARGmGACHulHUUsVs790.doc
2025-08-11 11:41:30 | INFO     | app.services.compliance_service:execute_pipeline:386 | 步骤1: 开始验证服务前置条件 | ID: ab7abca3-cf91-48a8-bc22-4d1b14d9497c
2025-08-11 11:41:30 | INFO     | app.services.compliance_service:validate_prerequisites:73 | 开始验证服务前置条件 | ID: ab7abca3-cf91-48a8-bc22-4d1b14d9497c
2025-08-11 11:41:30 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 敏感词服务健康检查 | ID: ab7abca3-cf91-48a8-bc22-4d1b14d9497c | 详情: {}
2025-08-11 11:41:30 | INFO     | app.services.sensitive_word_service:check_health:122 | 检查敏感词服务健康状态: http://*************:8087/health
2025-08-11 11:41:30 | INFO     | app.services.sensitive_word_service:check_health:132 | 敏感词服务健康状态: 健康
2025-08-11 11:41:30 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 敏感词服务健康检查 | ID: ab7abca3-cf91-48a8-bc22-4d1b14d9497c | 状态: 成功 | 耗时: 0.034秒
2025-08-11 11:41:30 | INFO     | app.services.compliance_service:validate_prerequisites:92 | 服务健康检查完成 | ID: ab7abca3-cf91-48a8-bc22-4d1b14d9497c | 状态: {'file_processor': True, 'ai_model': True, 'sensitive_word': True, 'result_processor': True}
2025-08-11 11:41:30 | INFO     | app.services.compliance_service:execute_pipeline:388 | 步骤1: 服务前置条件验证完成 | ID: ab7abca3-cf91-48a8-bc22-4d1b14d9497c | 状态: {'file_processor': True, 'ai_model': True, 'sensitive_word': True, 'result_processor': True}
2025-08-11 11:41:30 | INFO     | app.services.compliance_service:execute_pipeline:394 | 步骤2: 开始获取项目信息 | ID: ab7abca3-cf91-48a8-bc22-4d1b14d9497c
2025-08-11 11:41:30 | INFO     | app.services.compliance_service:execute_pipeline:396 | 步骤2: 项目信息获取完成 | ID: ab7abca3-cf91-48a8-bc22-4d1b14d9497c
2025-08-11 11:41:30 | INFO     | app.services.compliance_service:execute_pipeline:400 | 步骤3: 开始文件处理阶段 | ID: ab7abca3-cf91-48a8-bc22-4d1b14d9497c
2025-08-11 11:41:30 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 文件处理阶段 | ID: ab7abca3-cf91-48a8-bc22-4d1b14d9497c | 详情: {}
2025-08-11 11:41:30 | INFO     | app.services.compliance_service:process_file_stage:114 | 开始文件处理阶段 | ID: ab7abca3-cf91-48a8-bc22-4d1b14d9497c | 文件: rBIKBmiZXUSARGmGACHulHUUsVs790.doc
2025-08-11 11:41:30 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 优化文件处理流程 | ID: ab7abca3-cf91-48a8-bc22-4d1b14d9497c | 详情: {}
2025-08-11 11:41:30 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 文件下载 | ID: ab7abca3-cf91-48a8-bc22-4d1b14d9497c | 详情: {}
2025-08-11 11:41:30 | INFO     | app.services.file_processor_v2:download_file:255 | 开始下载文件: https://test-fds.xinecai.com/group1/M00/02/8F/rBIKBmiZXUSARGmGACHulHUUsVs790.doc?token=53d5127d4f2e56b351bce1f7d8af5a08&ts=1754881369
2025-08-11 11:41:31 | INFO     | app.services.file_processor_v2:download_file:289 | 文件下载完成: 2223764 字节
2025-08-11 11:41:31 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 文件下载 | ID: ab7abca3-cf91-48a8-bc22-4d1b14d9497c | 状态: 成功 | 耗时: 1.428秒
2025-08-11 11:41:31 | WARNING  | app.services.file_processor_v2:process_file:515 | MarkItDown不可用，使用备用方案
2025-08-11 11:41:31 | WARNING  | app.services.file_processor_v2:process_file:519 | MarkItDown处理失败: MarkItDown不可用，使用备用方案
2025-08-11 11:41:31 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | DOCX备用提取 | ID: ab7abca3-cf91-48a8-bc22-4d1b14d9497c | 详情: {}
2025-08-11 11:41:31 | INFO     | app.services.file_processor_v2:extract_docx_content_fallback:401 | DOCX备用提取完成: 60374 字符
2025-08-11 11:41:31 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | DOCX备用提取 | ID: ab7abca3-cf91-48a8-bc22-4d1b14d9497c | 状态: 成功 | 耗时: 0.267秒
2025-08-11 11:41:31 | INFO     | app.core.logger:log_file_processing:159 | 文件处理 | ID: ab7abca3-cf91-48a8-bc22-4d1b14d9497c | 文件: rBIKBmiZXUSARGmGACHulHUUsVs790.doc | 大小: 2223764 字节 | 处理时间: 0.000秒
2025-08-11 11:41:31 | INFO     | app.services.file_processor_v2:process_file:548 | 文件处理完成(备用方案): rBIKBmiZXUSARGmGACHulHUUsVs790.doc, 输出长度: 60434 字符
2025-08-11 11:41:31 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 优化文件处理流程 | ID: ab7abca3-cf91-48a8-bc22-4d1b14d9497c | 状态: 成功 | 耗时: 1.701秒
2025-08-11 11:41:31 | INFO     | app.services.compliance_service:process_file_stage:131 | 文件处理完成 | ID: ab7abca3-cf91-48a8-bc22-4d1b14d9497c | 内容长度: 60434 字符
2025-08-11 11:41:31 | INFO     | app.core.logger:log_file_processing:159 | 文件处理 | ID: ab7abca3-cf91-48a8-bc22-4d1b14d9497c | 文件: rBIKBmiZXUSARGmGACHulHUUsVs790.doc | 大小: 2223764 字节 | 处理时间: 0.000秒
2025-08-11 11:41:31 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | file_processing | ID: ab7abca3-cf91-48a8-bc22-4d1b14d9497c | 状态: 成功 | 耗时: 1.702秒
2025-08-11 11:41:31 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 文件处理阶段 | ID: ab7abca3-cf91-48a8-bc22-4d1b14d9497c | 状态: 成功 | 耗时: 1.703秒
2025-08-11 11:41:31 | INFO     | app.services.compliance_service:execute_pipeline:405 | 步骤3: 文件处理完成 | ID: ab7abca3-cf91-48a8-bc22-4d1b14d9497c | 内容长度: 60434
2025-08-11 11:41:31 | INFO     | app.services.compliance_service:execute_pipeline:416 | 步骤4: 开始AI合规性检查阶段 | ID: ab7abca3-cf91-48a8-bc22-4d1b14d9497c
2025-08-11 11:41:31 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | AI合规性检查阶段 | ID: ab7abca3-cf91-48a8-bc22-4d1b14d9497c | 详情: {}
2025-08-11 11:41:31 | INFO     | app.services.compliance_service:ai_compliance_check_stage:188 | 开始AI合规性检查 | ID: ab7abca3-cf91-48a8-bc22-4d1b14d9497c
2025-08-11 11:41:31 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | AI模型调用(尝试1) | ID: ab7abca3-cf91-48a8-bc22-4d1b14d9497c | 详情: {}
2025-08-11 11:41:31 | INFO     | app.services.ai_model_service:_call_model_with_retry:648 | 调用AI模型: gemini-2.5-flash, 消息数: 2, 尝试: 1/4 | 请求ID: ab7abca3-cf91-48a8-bc22-4d1b14d9497c
2025-08-11 11:42:46 | INFO     | app.core.logger:log_api_call:170 | API调用 | OpenAI-gemini-2.5-flash | ID: ab7abca3-cf91-48a8-bc22-4d1b14d9497c | 状态: 成功 | 耗时: 0.000秒
2025-08-11 11:42:46 | INFO     | app.services.ai_model_service:_call_model_with_retry:687 | AI模型调用成功: 响应长度 6688 字符, 尝试次数: 1 | 请求ID: ab7abca3-cf91-48a8-bc22-4d1b14d9497c
2025-08-11 11:42:46 | WARNING  | app.services.ai_model_service:_call_model_with_retry:691 | AI模型原始响应内容: '```json
{
  "checkResultArr": [
    {
      "quesType": "合规性/逻辑性/规范性",
      "quesDesc": "招标文件封面项目名称多出“阿萨德”字样，与招标公告及其他章节的项目名称不一致，存在笔误。",
      "originalArr": [
        "安徽理工大学第一附属医院南区污水医废、生活垃圾一体化托管服务项目二次阿萨德"
      ],
      "point": "项目信息核验，确保文件中的项目名称与招标公告完全一致。",
      "advice": "删除招标文件封面项目名称中多余的“阿萨德”字样，使其与招标公告及其他章节的项目名称保持一致。"
    },
    {
      "quesType": "规范性",
      "quesDesc": "招标文件目录页码与后文实际章节页码不对应，例如“第二章 投标人须知前附表 4”实际页码为8，存在排版错误。",
      "originalArr": [
        "第二章    投标人须知前附表\t4",
        "第三章    投标人须知\t22",
        "第四章    服务要求\t40",
        "第五章    评标方法\t57",
        "第六章    合同条款\t64",
        "第七章    投标文件相关格式\t65",
        "第八章    电子招投标相关要求\t85"
      ],
      "point": "文件结构检查，目录页码与后文实际章节页码必须严格对应。",
      "advice": "修正招标文件目录页码，使其与实际章节页码严格对应。"
    },
    {
      "quesType": "合规性/逻辑性",
      "quesDesc": "招标公告作为项目摘要，未明确投标保证金的金额、形式、退还方式以及付款方式等关键信息，而这些信息在投标人须知前附表和投标人须知中有详细规定，导致公告信息不完整。",
      "originalArr": [
        "招标公告中未提及投标保证金金额、形式、退还方式及付款方式。"
      ],
      "point": "招标公告应包含项目关键信息，确保信息完整性。",
      "advice": "在招标公告中补充投标保证金的金额、形式、退还方式以及付款方式等关键信息，或明确指引至具体章节。"
    },
    {
      "quesType": "逻辑性/合规性",
      "quesDesc": "招标文件关于履约保证金的条款存在矛盾。第三章 投标人须知 29.1 要求中标人提交履约保证金，而第八章 电子招投标相关要求 47 明确“不要求”履约保证金。",
      "originalArr": [
        "第三章 投标人须知 29.1 “中标人应提交履约保证金。履约保证金金额及收受人见投标人须知前附表规定。”",
        "第八章 电子招投标相关要求 47 “履约保证金：■不要求”"
      ],
      "point": "条款一致性验证，确保招标文件内部条款无矛盾。",
      "advice": "统一履约保证金要求。如果不需要履约保证金，则删除投标人须知中要求提交履约保证金的条款；如果需要，则在投标人须知前附表中明确履约保证金的金额、形式等具体要求。"
    },
    {
      "quesType": "合规性/公平性/可操作性",
      "quesDesc": "第四章 服务要求中对服务人员资质的要求，未提供相关证明材料将导致投标无效，而第五章 评标方法中相同的资质要求仅作为评分项，未提供仅导致不得分，存在评审标准不一致的问题。",
      "originalArr": [
        "第四章 服务要求 (一) 3.16 “注：本条款所涉及人员，供应商须提供其相关证件扫描件及为其缴纳的最近3个月社保证明材料，未提供视为不响应文件，按无效响应处理。”",
        "第四章 服务要求 (一) 4.4 “注：本条款所涉及人员，供应商须提供其相关证件扫描件及为其缴纳的最近3个月社保证明材料，未提供视为不响应文件，按无效响应处理。”",
        "第五章 评标方法 11. 技术标 评审要素“项目管理人员”评分标准 “注：1、响应文件中须提供证书扫描件，且符合以上要求，否则不予计分。2、投标人拟派服务本项目人员需为投标投标人正式员工，提供本单位自2024年9月份以来为其缴纳的任意连续三个月的社保证明（含人社部门官网在线打印件）否则不予计分。”"
      ],
      "point": "确保招标文件条款之间逻辑一致，避免对同一事项设置相互矛盾的评审标准，影响公平性。",
      "advice": "统一对服务人员资质要求的处理方式。如果该资质是实质性要求，则应在资格要求或符合性审查中明确，并统一规定未提供将导致投标无效；如果仅作为加分项，则应在服务要求中删除“按无效响应处理”的表述，仅在评标办法中作为评分依据。建议将人员资质要求作为评分项，以增加投标人的灵活性和竞争性。"
    },
    {
      "quesType": "规范性",
      "quesDesc": "第四章 服务要求 附件1中“1000元以内服务单位免费维修更换清单”的说明“本项目要求的1000元以内的设备免费维修部分包括但不限于上述清单已列明内容”，但未明确“上述清单”具体指哪个清单，存在歧义。",
      "originalArr": [
        "第四章 服务要求 附件1 “1000元以内服务单位免费维修更换清单”下的说明：“说明：本项目要求的1000元以内的设备免费维修部分包括但不限于上述清单已列明内容，投标单位可根据现场及自身服务能力在此基础上继续补充完善。”"
      ],
      "point": "文本准确性，确保描述清晰无歧义。",
      "advice": "明确“上述清单”具体指哪个清单，例如可以明确为“投标单位需为本项目配备的备用件及易损件清单”或“本清单”，以消除歧义。"
    },
    {
      "quesType": "规范性",
      "quesDesc": "第四章 服务要求中存在一个空的附件2，仅有标题而无任何内容，且标题格式与附件1不一致，不符合文件规范性要求。",
      "originalArr": [
        "第四章 服务要求 附件2 “附件2：”"
      ],
      "point": "文件结构检查，确保附件内容完整且格式规范。",
      "advice": "补充附件2的实际内容，或删除该空附件。同时，统一附件标题的格式，保持文件整体规范性。"
    },
    {
      "quesType": "规范性",
      "quesDesc": "第五章 评标方法中多处出现“其他”评审标准，例如“评委会依据招标文件认定的其他原则”、“其他被评委会认定无效的情况”和“其他实质性响应：符合法律、行政法规规定的其他条件或招标文件列明的其他要求”，但未明确具体内容，可能导致评审主观性。",
      "originalArr": [
        "第五章 评标方法 二、6 (5) “评委会依据招标文件认定的其他原则。”",
        "第五章 评标方法 二、25.1 (6) “其他被评委会认定无效的情况。”",
        "第五章 评标方法 二、10. 初步评审指标 “其他：详见招标公告，符合招标文件及法律法规规定”",
        "第五章 评标方法 二、10. 初步评审指标 “其他实质性响应：符合法律、行政法规规定的其他条件或招标文件列明的其他要求”"
      ],
      "point": "评标标准应具体明确，避免主观判断。",
      "advice": "明确“其他原则”、“其他情况”和“其他要求”的具体内容，或删除此类模糊表述，以确保评审的客观性和公正性。"
    },
    {
      "quesType": "规范性",
      "quesDesc": "第八章 电子招投标相关要求中，部分内容与第三章 投标人须知、第七章 投标文件相关格式中的内容存在重复，例如数字证书办理、电子投标文件制作、开标及投标文件解密等，导致文件冗余。",
      "originalArr": [
        "第八章 电子招投标相关要求 35 “关于数字证书的办理”",
        "第三章 投标人须知 19.1 (3) “投标人须用通过安天智采办理的移动认证证书（或介质数字证书）签章和加密投标文件，若使用介质数字证书的，建议使用企业法人主锁。如未办移动认证证书（或介质数字证书）请及时办理：移动认证办理联系电话：400-0878-198转1，移动认证办理须知详见安天智采平台“移动认证上线通知”（https://www.xinecai.com/ydrz.html）”；办理介质数字证书的，参见CA办理须知https://www.xinecai.com/quesinfo/20.html。”"
      ],
      "point": "文件结构检查，避免重复内容，保持文件简洁高效。",
      "advice": "整合重复内容，将详细说明集中在某一章节，其他章节引用或简要提及，以提高招标文件可读性和维护性。"
    },
    {
      "quesType": "规范性",
      "quesDesc": "第八章 电子招投标相关要求中，序号51的内容是“需补充的其他内容”，但其下又紧跟着51.1、51.2等子序号，导致逻辑上的不清晰和编号混乱。",
      "originalArr": [
        "第八章 电子招投标相关要求 51 “需补充的其他内容”"
      ],
      "point": "文件结构检查，确保序号逻辑清晰。",
      "advice": "将“需补充的其他内容”作为大标题，其下的具体内容（如付款方式、询标等）作为一级子项，重新编号，例如从52开始，或将51作为总括性标题，其下内容直接列出，不使用51.1等子序号。"
    },
    {
      "quesType": "风险管理",
      "quesDesc": "第八章 电子招投标相关要求 51.4 (12) 关于围标、串标行为的认定条款“投标人投标报价与公布的最高投标限价（控制价）相比降幅过小，明显缺乏竞争性，有围标、串标嫌疑的”，该表述过于宽泛，可能导致主观判断和争议。",
      "originalArr": [
        "第八章 电子招投标相关要求 51.4 (12) “投标人投标报价与公布的最高投标限价（控制价）相比降幅过小，明显缺乏竞争性，有围标、串标嫌疑的。”"
      ],
      "point": "风险管理，避免因条款模糊导致不必要的争议和法律风险。",
      "advice": "细化或量化“降幅过小”、“明显缺乏竞争性”的具体标准，或删除该条，避免主观判断。围标串标的认定应基于客观证据，而非主观推测。"
    },
    {
      "quesType": "风险管理",
      "quesDesc": "第八章 电子招投标相关要求 51.5 (3) 中关于“虚假、恶意异议”的认定和处理，特别是“一年内三次及以上异议均查无实据的”，可能存在操作上的困难和争议，且对投标人的惩罚措施过于严厉，可能限制投标人合法维权的权利。",
      "originalArr": [
        "第八章 电子招投标相关要求 51.5 (3) “对于虚假、恶意异议：提出异议的投标人不得以异议为名进行虚假、恶意异议，干扰招标投标活动的正常进行。对于提供虚假材料，以异议为名谋取中标或恶意异议扰乱招标工作秩序的，将报请行政监督部门处理。”"
      ],
      "point": "风险管理，确保异议处理机制的公平性和可操作性，避免过度惩罚。",
      "advice": "细化“虚假、恶意异议”的认定标准，并明确处理流程和救济途径，确保惩罚措施合法合规且有充分依据。对于“一年内三次及以上异议均查无实据的”条款，建议删除或修改，因为“查无实据”不等于“恶意”。"
    },
    {
      "quesType": "规范性",
      "quesDesc": "第八章 电子招投标相关要求 51.11 (5) 中关于弃标函的要求“若在开标前临时弃标，且未及时发送弃标函影响开标造成不良后果者，招标人有权拒绝其（6个月内）参加医院任何招标采购项目的投标。”此条款对弃标行为的惩罚过重，且“临时弃标”和“不良后果”的界定模糊。",
      "originalArr": [
        "第八章 电子招投标相关要求 51.11 (5) “若在开标前临时弃标，且未及时发送弃标函影响开标造成不良后果者，招标人有权拒绝其（6个月内）参加医院任何招标采购项目的投标。”"
      ],
      "point": "规范性，确保条款合理合法，避免过度惩罚。",
      "advice": "建议删除此条款或修改为更合理的惩罚措施，并明确“临时弃标”和“不良后果”的具体界定。弃标行为通常由投标保证金处理，不应额外设置过重的限制投标资格的惩罚。"
    },
    {
      "quesType": "规范性",
      "quesDesc": "第八章 电子招投标相关要求 51.8 (2) 中关于知识产权的表述“履行合同义务后，享有不受限制的无偿使用权”，可能与实际情况不符，且“如投标人不拥有相应的知识产权，则在投标报价中必须包括合法获取该知识产权的一切相关费用”与“无偿使用权”存在逻辑矛盾。",
      "originalArr": [
        "第八章 电子招投标相关要求 51.8 (2) “招标人在中华人民共和国境内使用中标货物（服务）、资料、技术、服务或其任何一部分时，履行合同义务后，享有不受限制的无偿使用权，不会产生因第三方提出侵犯其专利权、商标权或其它知识产权而引起的法律或经济纠纷。如投标人不拥有相应的知识产权，则在投标报价中必须包括合法获取该知识产权的一切相关费用。如因此导致招标人损失的，投标人须承担全部赔偿责任。”"
      ],
      "point": "文本准确性，确保条款表述严谨，避免逻辑矛盾。",
      "advice": "重新审视知识产权条款，明确“无偿使用权”的范围和条件，并消除与“合法获取该知识产权的一切相关费用”之间的逻辑矛盾。通常，无偿使用权是指无需额外支付费用，但获取知识产权本身可能需要成本。"
    },
    {
      "quesType": "规范性",
      "quesDesc": "第三章 投标人须知 2.5 中关于“近X年内”的描述为“按招标文件要求，没作要求的为自开标之日往前追溯X年”，其中“X年”未明确具体年限，存在歧义。",
      "originalArr": [
        "第三章 投标人须知 2.5 “近X年内：按招标文件要求，没作要求的为自开标之日往前追溯X年。”"
      ],
      "point": "文本准确性，确保描述清晰无歧义。",
      "advice": "明确“X年”的具体年限，例如“近三年内”或“近五年内”，以消除歧义。"
    }
  ]
}
```'
2025-08-11 11:42:46 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | AI模型调用(尝试1) | ID: ab7abca3-cf91-48a8-bc22-4d1b14d9497c | 状态: 成功 | 耗时: 74.547秒
2025-08-11 11:42:46 | WARNING  | app.services.ai_model_service:clean_json_data:369 | 第一次JSON解析失败，尝试深度修复: Expecting ',' delimiter: line 1 column 3795 (char 3794)
2025-08-11 11:42:46 | WARNING  | app.services.ai_model_service:clean_json_data:370 | 第一次解析失败的内容: {  "checkResultArr": [    {      "quesType": "合规性/逻辑性/规范性",      "quesDesc": "招标文件封面项目名称多出“阿萨德”字样，与招标公告及其他章节的项目名称不一致，存在笔误。",      "originalArr": [        "安徽理工大学第一附属医院南区污水医废、生活垃圾一体化托管服务项目二次阿萨德"      ],      "point": "项目信息核验，确保文件中的项目名称与招标公告完全一致。",      "advice": "删除招标文件封面项目名称中多余的“阿萨德”字样，使其与招标公告及其他章节的项目名称保持一致。"    },    {      "quesType": "规范性",      "quesDesc": "招标文件目录页码与后文实际章节页码不对应，例如“第二章 投标人须知前附表 4”实际页码为8，存在排版错误。",      "originalArr": [        "第二章    投标人须知前附表\t4",        "第三章    投标人须知\t22",   ...
2025-08-11 11:42:46 | WARNING  | app.services.ai_model_service:clean_json_data:376 | 提取JSON后的内容: {  "checkResultArr": [    {      "quesType": "合规性/逻辑性/规范性",      "quesDesc": "招标文件封面项目名称多出“阿萨德”字样，与招标公告及其他章节的项目名称不一致，存在笔误。",      "originalArr": [        "安徽理工大学第一附属医院南区污水医废、生活垃圾一体化托管服务项目二次阿萨德"      ],      "point": "项目信息核验，确保文件中的项目名称与招标公告完全一致。",      "advice": "删除招标文件封面项目名称中多余的“阿萨德”字样，使其与招标公告及其他章节的项目名称保持一致。"    },    {      "quesType": "规范性",      "quesDesc": "招标文件目录页码与后文实际章节页码不对应，例如“第二章 投标人须知前附表 4”实际页码为8，存在排版错误。",      "originalArr": [        "第二章    投标人须知前附表\t4",        "第三章    投标人须知\t22",   ...
2025-08-11 11:42:46 | WARNING  | app.services.ai_model_service:clean_json_data:379 | 尝试修复JSON引号问题，错误位置: 第3795列
2025-08-11 11:42:46 | WARNING  | app.services.ai_model_service:clean_json_data:386 | 全面JSON修复成功
2025-08-11 11:42:46 | INFO     | app.services.ai_model_service:check_compliance:1018 | 合规性检查完成，发现 1 个问题
2025-08-11 11:42:46 | INFO     | app.services.compliance_service:ai_compliance_check_stage:195 | AI合规性检查完成 | ID: ab7abca3-cf91-48a8-bc22-4d1b14d9497c | 发现问题: 1个
2025-08-11 11:42:46 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | ai_model_call | ID: ab7abca3-cf91-48a8-bc22-4d1b14d9497c | 状态: 成功 | 耗时: 74.552秒
2025-08-11 11:42:46 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | AI合规性检查阶段 | ID: ab7abca3-cf91-48a8-bc22-4d1b14d9497c | 状态: 成功 | 耗时: 74.552秒
2025-08-11 11:42:46 | INFO     | app.services.compliance_service:execute_pipeline:423 | 步骤4: AI合规性检查完成 | ID: ab7abca3-cf91-48a8-bc22-4d1b14d9497c | 结果数量: 1
2025-08-11 11:42:46 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 敏感词检测阶段 | ID: ab7abca3-cf91-48a8-bc22-4d1b14d9497c | 详情: {}
2025-08-11 11:42:46 | INFO     | app.services.compliance_service:sensitive_word_check_stage:245 | 开始敏感词检测 | ID: ab7abca3-cf91-48a8-bc22-4d1b14d9497c
2025-08-11 11:42:46 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 敏感词服务健康检查 | ID: ab7abca3-cf91-48a8-bc22-4d1b14d9497c | 详情: {}
2025-08-11 11:42:46 | INFO     | app.services.sensitive_word_service:check_health:122 | 检查敏感词服务健康状态: http://*************:8087/health
2025-08-11 11:42:46 | INFO     | app.services.sensitive_word_service:check_health:132 | 敏感词服务健康状态: 健康
2025-08-11 11:42:46 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 敏感词服务健康检查 | ID: ab7abca3-cf91-48a8-bc22-4d1b14d9497c | 状态: 成功 | 耗时: 0.023秒
2025-08-11 11:42:46 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 敏感词检测 | ID: ab7abca3-cf91-48a8-bc22-4d1b14d9497c | 详情: {}
2025-08-11 11:42:46 | INFO     | app.services.sensitive_word_service:detect_sensitive_words:214 | 开始敏感词检测: 内容长度=60434, 政府采购=False
2025-08-11 11:42:46 | INFO     | app.services.sensitive_word_service:detect_sensitive_words:223 | 敏感词检测超时设置: 60.0秒 | ID: ab7abca3-cf91-48a8-bc22-4d1b14d9497c
2025-08-11 11:42:46 | INFO     | app.services.sensitive_word_service:_convert_to_target_format:376 | 敏感词转换完成: 原始5个，有效5个
2025-08-11 11:42:46 | INFO     | app.core.logger:log_api_call:170 | API调用 | 敏感词检测API | ID: ab7abca3-cf91-48a8-bc22-4d1b14d9497c | 状态: 成功 | 耗时: 0.000秒
2025-08-11 11:42:46 | INFO     | app.services.sensitive_word_service:detect_sensitive_words:269 | 敏感词检测完成: 发现 5 个敏感词
2025-08-11 11:42:46 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 敏感词检测 | ID: ab7abca3-cf91-48a8-bc22-4d1b14d9497c | 状态: 成功 | 耗时: 0.019秒
2025-08-11 11:42:46 | INFO     | app.services.compliance_service:sensitive_word_check_stage:252 | 敏感词检测完成 | ID: ab7abca3-cf91-48a8-bc22-4d1b14d9497c | 发现敏感词: 5个
2025-08-11 11:42:46 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | sensitive_word | ID: ab7abca3-cf91-48a8-bc22-4d1b14d9497c | 状态: 成功 | 耗时: 0.046秒
2025-08-11 11:42:46 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 敏感词检测阶段 | ID: ab7abca3-cf91-48a8-bc22-4d1b14d9497c | 状态: 成功 | 耗时: 0.046秒
2025-08-11 11:42:46 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 结果聚合阶段 | ID: ab7abca3-cf91-48a8-bc22-4d1b14d9497c | 详情: {}
2025-08-11 11:42:46 | INFO     | app.services.compliance_service:result_aggregation_stage:305 | 开始结果聚合 | ID: ab7abca3-cf91-48a8-bc22-4d1b14d9497c
2025-08-11 11:42:46 | INFO     | app.core.logger:log_operation_start:139 | 操作开始 | 结果聚合 | ID: ab7abca3-cf91-48a8-bc22-4d1b14d9497c | 详情: {}
2025-08-11 11:42:46 | INFO     | app.services.result_processor:aggregate_results:323 | 开始结果聚合: 敏感词5个，检查结果1个 | ID: ab7abca3-cf91-48a8-bc22-4d1b14d9497c
2025-08-11 11:42:46 | INFO     | app.services.result_processor:validate_sensitive_words:109 | 敏感词验证完成: 原始5个，有效5个
2025-08-11 11:42:46 | INFO     | app.services.result_processor:deduplicate_sensitive_words:234 | 敏感词去重完成: 原始5个，去重后5个
2025-08-11 11:42:46 | INFO     | app.services.result_processor:_verify_deduplication_integrity:943 | 去重完整性验证 | ID: ab7abca3-cf91-48a8-bc22-4d1b14d9497c | 状态: 完美 | 输入: 5个 | 输出: 5个 | 出现次数变化: 118 -> 118
2025-08-11 11:42:46 | WARNING  | app.services.result_processor:validate_check_results:149 | 跳过空描述的检查结果: quesType=<QuestionType.COMPLIANCE: '合规性'> quesDesc='' originalArr=[] point='' advice=''
2025-08-11 11:42:46 | INFO     | app.services.result_processor:validate_check_results:188 | 检查结果验证完成: 原始1个，有效0个
2025-08-11 11:42:46 | ERROR    | app.services.result_processor:_verify_processing_integrity:832 | 完整性验证 | 检查结果验证 | ID: ab7abca3-cf91-48a8-bc22-4d1b14d9497c | 状态: 灾难性丢失 | 输入: 1 | 输出: 0 | 丢失: 1 (100.0%)
2025-08-11 11:42:46 | INFO     | app.services.result_processor:prioritize_check_results:272 | 检查结果排序完成: 0个结果
2025-08-11 11:42:46 | INFO     | app.services.result_processor:_verify_final_integrity:719 | 检查结果数量限制 | ID: ab7abca3-cf91-48a8-bc22-4d1b14d9497c | 1 -> 0
2025-08-11 11:42:46 | INFO     | app.services.result_processor:_verify_final_integrity:743 | 完整性报告 | ID: ab7abca3-cf91-48a8-bc22-4d1b14d9497c | 敏感词保留率: 100.0% | 检查结果保留率: 0.0%
2025-08-11 11:42:46 | INFO     | app.services.result_processor:aggregate_results:385 | 结果聚合完成: 敏感词5个，检查结果0个 | ID: ab7abca3-cf91-48a8-bc22-4d1b14d9497c
2025-08-11 11:42:46 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 结果聚合 | ID: ab7abca3-cf91-48a8-bc22-4d1b14d9497c | 状态: 成功 | 耗时: 0.008秒
2025-08-11 11:42:46 | INFO     | app.services.result_processor:validate_final_response:479 | 响应验证完成: 敏感词5个，检查结果0个
2025-08-11 11:42:46 | INFO     | app.services.compliance_service:result_aggregation_stage:312 | 结果聚合完成 | ID: ab7abca3-cf91-48a8-bc22-4d1b14d9497c | 敏感词: 5个 | 检查结果: 0个
2025-08-11 11:42:46 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | result_aggregation | ID: ab7abca3-cf91-48a8-bc22-4d1b14d9497c | 状态: 成功 | 耗时: 0.010秒
2025-08-11 11:42:46 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 结果聚合阶段 | ID: ab7abca3-cf91-48a8-bc22-4d1b14d9497c | 状态: 成功 | 耗时: 0.010秒
2025-08-11 11:42:46 | INFO     | app.services.compliance_service:_add_pipeline_metadata:538 | 流水线元数据 | ID: ab7abca3-cf91-48a8-bc22-4d1b14d9497c | 处理时间: 76.351秒 | 降级使用: False | 敏感词数量: 5 | 检查结果数量: 0
2025-08-11 11:42:46 | INFO     | app.core.logger:record_stage_performance:211 | 阶段性能 | total_pipeline | ID: ab7abca3-cf91-48a8-bc22-4d1b14d9497c | 状态: 成功 | 耗时: 76.351秒
2025-08-11 11:42:46 | INFO     | app.services.compliance_service:execute_pipeline:479 | 合规性检查流水线完成 | ID: ab7abca3-cf91-48a8-bc22-4d1b14d9497c | 耗时: 76.351秒 | 降级: False
2025-08-11 11:42:46 | INFO     | app.core.logger:log_operation_end:150 | 操作结束 | 完整合规性检查流水线 | ID: ab7abca3-cf91-48a8-bc22-4d1b14d9497c | 状态: 成功 | 耗时: 76.353秒
2025-08-11 11:42:46 | INFO     | app.services.compliance_service:check_compliance:641 | 合规性检查服务完成 | ID: ab7abca3-cf91-48a8-bc22-4d1b14d9497c
2025-08-11 11:42:46 | INFO     | app.api.routes:check_compliance_simple:156 | 简化合规性检查完成 | ID: ab7abca3-cf91-48a8-bc22-4d1b14d9497c | 文件: rBIKBmiZXUSARGmGACHulHUUsVs790.doc | 敏感词: 5个 | 检查结果: 0个
2025-08-11 11:42:46 | INFO     | app.middleware.logging:dispatch:48 | 请求完成 | ID: ab7abca3-cf91-48a8-bc22-4d1b14d9497c | 状态码: 200 | 耗时: 77.802秒
2025-08-11 14:11:16 | INFO     | main:lifespan:47 | 招标文件合规性检查助手关闭中...
2025-08-11 14:11:16 | INFO     | main:lifespan:51 | 停止异步请求队列...
2025-08-11 14:11:16 | INFO     | app.core.queue_manager:stop_workers:125 | 停止请求队列工作线程
2025-08-11 14:11:16 | INFO     | app.core.queue_manager:_worker:296 | 工作线程被取消: worker-0
2025-08-11 14:11:16 | INFO     | app.core.queue_manager:_worker:301 | 工作线程停止: worker-0
2025-08-11 14:11:16 | INFO     | app.core.queue_manager:_worker:296 | 工作线程被取消: worker-1
2025-08-11 14:11:16 | INFO     | app.core.queue_manager:_worker:301 | 工作线程停止: worker-1
2025-08-11 14:11:16 | INFO     | app.core.queue_manager:_worker:296 | 工作线程被取消: worker-2
2025-08-11 14:11:16 | INFO     | app.core.queue_manager:_worker:301 | 工作线程停止: worker-2
2025-08-11 14:11:16 | INFO     | app.core.queue_manager:_worker:296 | 工作线程被取消: worker-3
2025-08-11 14:11:16 | INFO     | app.core.queue_manager:_worker:301 | 工作线程停止: worker-3
2025-08-11 14:11:16 | INFO     | main:lifespan:53 | 请求队列已停止
2025-08-11 14:11:16 | INFO     | app.core.cache_manager:clear:198 | 缓存已清空
2025-08-11 14:11:16 | INFO     | main:lifespan:58 | 缓存已清理
2025-08-11 14:11:16 | INFO     | main:lifespan:60 | 招标文件合规性检查助手已关闭
2025-08-11 14:11:20 | INFO     | app.services.ai_model_service:_initialize_client:80 | AI模型客户端初始化成功: gemini-2.5-flash
2025-08-11 14:11:20 | INFO     | main:lifespan:26 | 招标文件合规性检查助手启动中...
2025-08-11 14:11:20 | INFO     | main:lifespan:27 | 配置信息: 模型=gemini-2.5-flash, 环境=development
2025-08-11 14:11:20 | INFO     | main:lifespan:31 | 启动异步请求队列...
2025-08-11 14:11:20 | INFO     | app.core.queue_manager:start_workers:117 | 启动请求队列工作线程: 4个
2025-08-11 14:11:20 | INFO     | main:lifespan:33 | 请求队列已启动，工作线程数: 4
2025-08-11 14:11:20 | INFO     | main:lifespan:37 | 文档缓存已启用，最大大小: 100MB
2025-08-11 14:11:20 | INFO     | main:lifespan:40 | 内存阈值: 2048MB, 自动GC: True
2025-08-11 14:11:20 | INFO     | app.core.queue_manager:_worker:266 | 工作线程启动: worker-0
2025-08-11 14:11:20 | INFO     | app.core.queue_manager:_worker:266 | 工作线程启动: worker-1
2025-08-11 14:11:20 | INFO     | app.core.queue_manager:_worker:266 | 工作线程启动: worker-2
2025-08-11 14:11:20 | INFO     | app.core.queue_manager:_worker:266 | 工作线程启动: worker-3
2025-08-11 14:11:50 | INFO     | main:lifespan:47 | 招标文件合规性检查助手关闭中...
2025-08-11 14:11:50 | INFO     | main:lifespan:51 | 停止异步请求队列...
2025-08-11 14:11:50 | INFO     | app.core.queue_manager:stop_workers:125 | 停止请求队列工作线程
2025-08-11 14:11:50 | INFO     | app.core.queue_manager:_worker:296 | 工作线程被取消: worker-0
2025-08-11 14:11:50 | INFO     | app.core.queue_manager:_worker:301 | 工作线程停止: worker-0
2025-08-11 14:11:50 | INFO     | app.core.queue_manager:_worker:296 | 工作线程被取消: worker-1
2025-08-11 14:11:50 | INFO     | app.core.queue_manager:_worker:301 | 工作线程停止: worker-1
2025-08-11 14:11:50 | INFO     | app.core.queue_manager:_worker:296 | 工作线程被取消: worker-2
2025-08-11 14:11:50 | INFO     | app.core.queue_manager:_worker:301 | 工作线程停止: worker-2
2025-08-11 14:11:50 | INFO     | app.core.queue_manager:_worker:296 | 工作线程被取消: worker-3
2025-08-11 14:11:50 | INFO     | app.core.queue_manager:_worker:301 | 工作线程停止: worker-3
2025-08-11 14:11:50 | INFO     | main:lifespan:53 | 请求队列已停止
2025-08-11 14:11:50 | INFO     | app.core.cache_manager:clear:198 | 缓存已清空
2025-08-11 14:11:50 | INFO     | main:lifespan:58 | 缓存已清理
2025-08-11 14:11:50 | INFO     | main:lifespan:60 | 招标文件合规性检查助手已关闭
2025-08-11 14:11:53 | INFO     | app.services.ai_model_service:_initialize_client:80 | AI模型客户端初始化成功: gemini-2.5-flash
2025-08-11 14:11:53 | INFO     | main:lifespan:26 | 招标文件合规性检查助手启动中...
2025-08-11 14:11:53 | INFO     | main:lifespan:27 | 配置信息: 模型=gemini-2.5-flash, 环境=development
2025-08-11 14:11:53 | INFO     | main:lifespan:31 | 启动异步请求队列...
2025-08-11 14:11:53 | INFO     | app.core.queue_manager:start_workers:117 | 启动请求队列工作线程: 4个
2025-08-11 14:11:53 | INFO     | main:lifespan:33 | 请求队列已启动，工作线程数: 4
2025-08-11 14:11:53 | INFO     | main:lifespan:37 | 文档缓存已启用，最大大小: 100MB
2025-08-11 14:11:53 | INFO     | main:lifespan:40 | 内存阈值: 2048MB, 自动GC: True
2025-08-11 14:11:53 | INFO     | app.core.queue_manager:_worker:266 | 工作线程启动: worker-0
2025-08-11 14:11:53 | INFO     | app.core.queue_manager:_worker:266 | 工作线程启动: worker-1
2025-08-11 14:11:53 | INFO     | app.core.queue_manager:_worker:266 | 工作线程启动: worker-2
2025-08-11 14:11:53 | INFO     | app.core.queue_manager:_worker:266 | 工作线程启动: worker-3
2025-08-11 14:15:38 | INFO     | main:lifespan:47 | 招标文件合规性检查助手关闭中...
2025-08-11 14:15:38 | INFO     | main:lifespan:51 | 停止异步请求队列...
2025-08-11 14:15:38 | INFO     | app.core.queue_manager:stop_workers:125 | 停止请求队列工作线程
2025-08-11 14:15:38 | INFO     | app.core.queue_manager:_worker:296 | 工作线程被取消: worker-0
2025-08-11 14:15:38 | INFO     | app.core.queue_manager:_worker:301 | 工作线程停止: worker-0
2025-08-11 14:15:38 | INFO     | app.core.queue_manager:_worker:296 | 工作线程被取消: worker-1
2025-08-11 14:15:38 | INFO     | app.core.queue_manager:_worker:301 | 工作线程停止: worker-1
2025-08-11 14:15:38 | INFO     | app.core.queue_manager:_worker:296 | 工作线程被取消: worker-2
2025-08-11 14:15:38 | INFO     | app.core.queue_manager:_worker:301 | 工作线程停止: worker-2
2025-08-11 14:15:38 | INFO     | app.core.queue_manager:_worker:296 | 工作线程被取消: worker-3
2025-08-11 14:15:38 | INFO     | app.core.queue_manager:_worker:301 | 工作线程停止: worker-3
2025-08-11 14:15:38 | INFO     | main:lifespan:53 | 请求队列已停止
2025-08-11 14:15:38 | INFO     | app.core.cache_manager:clear:198 | 缓存已清空
2025-08-11 14:15:38 | INFO     | main:lifespan:58 | 缓存已清理
2025-08-11 14:15:38 | INFO     | main:lifespan:60 | 招标文件合规性检查助手已关闭
2025-08-11 14:15:42 | INFO     | app.services.ai_model_service:_initialize_client:80 | AI模型客户端初始化成功: gemini-2.5-flash
2025-08-11 14:15:42 | INFO     | main:lifespan:26 | 招标文件合规性检查助手启动中...
2025-08-11 14:15:42 | INFO     | main:lifespan:27 | 配置信息: 模型=gemini-2.5-flash, 环境=development
2025-08-11 14:15:42 | INFO     | main:lifespan:31 | 启动异步请求队列...
2025-08-11 14:15:42 | INFO     | app.core.queue_manager:start_workers:117 | 启动请求队列工作线程: 4个
2025-08-11 14:15:42 | INFO     | main:lifespan:33 | 请求队列已启动，工作线程数: 4
2025-08-11 14:15:42 | INFO     | main:lifespan:37 | 文档缓存已启用，最大大小: 100MB
2025-08-11 14:15:42 | INFO     | main:lifespan:40 | 内存阈值: 2048MB, 自动GC: True
2025-08-11 14:15:42 | INFO     | app.core.queue_manager:_worker:266 | 工作线程启动: worker-0
2025-08-11 14:15:42 | INFO     | app.core.queue_manager:_worker:266 | 工作线程启动: worker-1
2025-08-11 14:15:42 | INFO     | app.core.queue_manager:_worker:266 | 工作线程启动: worker-2
2025-08-11 14:15:42 | INFO     | app.core.queue_manager:_worker:266 | 工作线程启动: worker-3
2025-08-11 14:19:16 | INFO     | main:lifespan:47 | 招标文件合规性检查助手关闭中...
2025-08-11 14:19:16 | INFO     | main:lifespan:51 | 停止异步请求队列...
2025-08-11 14:19:16 | INFO     | app.core.queue_manager:stop_workers:125 | 停止请求队列工作线程
2025-08-11 14:19:16 | INFO     | app.core.queue_manager:_worker:296 | 工作线程被取消: worker-0
2025-08-11 14:19:16 | INFO     | app.core.queue_manager:_worker:301 | 工作线程停止: worker-0
2025-08-11 14:19:16 | INFO     | app.core.queue_manager:_worker:296 | 工作线程被取消: worker-1
2025-08-11 14:19:16 | INFO     | app.core.queue_manager:_worker:301 | 工作线程停止: worker-1
2025-08-11 14:19:16 | INFO     | app.core.queue_manager:_worker:296 | 工作线程被取消: worker-2
2025-08-11 14:19:16 | INFO     | app.core.queue_manager:_worker:301 | 工作线程停止: worker-2
2025-08-11 14:19:16 | INFO     | app.core.queue_manager:_worker:296 | 工作线程被取消: worker-3
2025-08-11 14:19:16 | INFO     | app.core.queue_manager:_worker:301 | 工作线程停止: worker-3
2025-08-11 14:19:16 | INFO     | main:lifespan:53 | 请求队列已停止
2025-08-11 14:19:16 | INFO     | app.core.cache_manager:clear:198 | 缓存已清空
2025-08-11 14:19:16 | INFO     | main:lifespan:58 | 缓存已清理
2025-08-11 14:19:16 | INFO     | main:lifespan:60 | 招标文件合规性检查助手已关闭
2025-08-11 14:19:19 | INFO     | app.services.ai_model_service:_initialize_client:80 | AI模型客户端初始化成功: gemini-2.5-flash
2025-08-11 14:19:19 | INFO     | main:lifespan:26 | 招标文件合规性检查助手启动中...
2025-08-11 14:19:19 | INFO     | main:lifespan:27 | 配置信息: 模型=gemini-2.5-flash, 环境=development
2025-08-11 14:19:19 | INFO     | main:lifespan:31 | 启动异步请求队列...
2025-08-11 14:19:19 | INFO     | app.core.queue_manager:start_workers:117 | 启动请求队列工作线程: 4个
2025-08-11 14:19:19 | INFO     | main:lifespan:33 | 请求队列已启动，工作线程数: 4
2025-08-11 14:19:19 | INFO     | main:lifespan:37 | 文档缓存已启用，最大大小: 100MB
2025-08-11 14:19:19 | INFO     | main:lifespan:40 | 内存阈值: 2048MB, 自动GC: True
2025-08-11 14:19:19 | INFO     | app.core.queue_manager:_worker:266 | 工作线程启动: worker-0
2025-08-11 14:19:19 | INFO     | app.core.queue_manager:_worker:266 | 工作线程启动: worker-1
2025-08-11 14:19:19 | INFO     | app.core.queue_manager:_worker:266 | 工作线程启动: worker-2
2025-08-11 14:19:19 | INFO     | app.core.queue_manager:_worker:266 | 工作线程启动: worker-3
2025-08-11 14:19:30 | INFO     | main:lifespan:47 | 招标文件合规性检查助手关闭中...
2025-08-11 14:19:30 | INFO     | main:lifespan:51 | 停止异步请求队列...
2025-08-11 14:19:30 | INFO     | app.core.queue_manager:stop_workers:125 | 停止请求队列工作线程
2025-08-11 14:19:30 | INFO     | app.core.queue_manager:_worker:296 | 工作线程被取消: worker-0
2025-08-11 14:19:30 | INFO     | app.core.queue_manager:_worker:301 | 工作线程停止: worker-0
2025-08-11 14:19:30 | INFO     | app.core.queue_manager:_worker:296 | 工作线程被取消: worker-1
2025-08-11 14:19:30 | INFO     | app.core.queue_manager:_worker:301 | 工作线程停止: worker-1
2025-08-11 14:19:30 | INFO     | app.core.queue_manager:_worker:296 | 工作线程被取消: worker-2
2025-08-11 14:19:30 | INFO     | app.core.queue_manager:_worker:301 | 工作线程停止: worker-2
2025-08-11 14:19:30 | INFO     | app.core.queue_manager:_worker:296 | 工作线程被取消: worker-3
2025-08-11 14:19:30 | INFO     | app.core.queue_manager:_worker:301 | 工作线程停止: worker-3
2025-08-11 14:19:30 | INFO     | main:lifespan:53 | 请求队列已停止
2025-08-11 14:19:30 | INFO     | app.core.cache_manager:clear:198 | 缓存已清空
2025-08-11 14:19:30 | INFO     | main:lifespan:58 | 缓存已清理
2025-08-11 14:19:30 | INFO     | main:lifespan:60 | 招标文件合规性检查助手已关闭
2025-08-11 14:19:40 | INFO     | app.services.ai_model_service:_initialize_client:80 | AI模型客户端初始化成功: gemini-2.5-flash
2025-08-11 14:19:43 | INFO     | app.services.ai_model_service:_initialize_client:80 | AI模型客户端初始化成功: gemini-2.5-flash
2025-08-11 14:19:43 | INFO     | main:lifespan:26 | 招标文件合规性检查助手启动中...
2025-08-11 14:19:43 | INFO     | main:lifespan:27 | 配置信息: 模型=gemini-2.5-flash, 环境=development
2025-08-11 14:19:43 | INFO     | main:lifespan:31 | 启动异步请求队列...
2025-08-11 14:19:43 | INFO     | app.core.queue_manager:start_workers:117 | 启动请求队列工作线程: 4个
2025-08-11 14:19:43 | INFO     | main:lifespan:33 | 请求队列已启动，工作线程数: 4
2025-08-11 14:19:43 | INFO     | main:lifespan:37 | 文档缓存已启用，最大大小: 100MB
2025-08-11 14:19:43 | INFO     | main:lifespan:40 | 内存阈值: 2048MB, 自动GC: True
2025-08-11 14:19:43 | INFO     | app.core.queue_manager:_worker:266 | 工作线程启动: worker-0
2025-08-11 14:19:43 | INFO     | app.core.queue_manager:_worker:266 | 工作线程启动: worker-1
2025-08-11 14:19:43 | INFO     | app.core.queue_manager:_worker:266 | 工作线程启动: worker-2
2025-08-11 14:19:43 | INFO     | app.core.queue_manager:_worker:266 | 工作线程启动: worker-3
2025-08-11 14:19:52 | INFO     | app.services.ai_model_service:_initialize_client:80 | AI模型客户端初始化成功: gemini-2.5-flash
2025-08-11 14:19:52 | WARNING  | app.services.ai_model_service:clean_json_data:369 | 第一次JSON解析失败，尝试深度修复: Expecting ',' delimiter: line 1 column 92 (char 91)
2025-08-11 14:19:52 | WARNING  | app.services.ai_model_service:clean_json_data:370 | 第一次解析失败的内容: {  "checkResultArr": [    {      "quesType": "合规性/逻辑性/规范性",      "quesDesc": "招标文件封面项目名称多出"阿萨德"字样，与招标公告及其他章节的项目名称不一致，存在笔误。",      "originalArr": [        "安徽理工大学第一附属医院南区污水医废、生活垃圾一体化托管服务项目二次阿萨德"      ],      "point": "项目信息核验，确保文件中的项目名称与招标公告完全一致。",      "advice": "删除招标文件封面项目名称中多余的"阿萨德"字样，使其与招标公告及其他章节的项目名称保持一致。"    }  ]}...
2025-08-11 14:19:52 | WARNING  | app.services.ai_model_service:clean_json_data:376 | 提取JSON后的内容: {  "checkResultArr": [    {      "quesType": "合规性/逻辑性/规范性",      "quesDesc": "招标文件封面项目名称多出"阿萨德"字样，与招标公告及其他章节的项目名称不一致，存在笔误。",      "originalArr": [        "安徽理工大学第一附属医院南区污水医废、生活垃圾一体化托管服务项目二次阿萨德"      ],      "point": "项目信息核验，确保文件中的项目名称与招标公告完全一致。",      "advice": "删除招标文件封面项目名称中多余的"阿萨德"字样，使其与招标公告及其他章节的项目名称保持一致。"    }  ]}...
2025-08-11 14:19:52 | WARNING  | app.services.ai_model_service:clean_json_data:379 | 尝试修复JSON引号问题，错误位置: 第92列
2025-08-11 14:19:52 | WARNING  | app.services.ai_model_service:clean_json_data:386 | 全面JSON修复成功
2025-08-11 14:21:11 | INFO     | app.services.ai_model_service:_initialize_client:80 | AI模型客户端初始化成功: gemini-2.5-flash
2025-08-11 14:21:11 | WARNING  | app.services.ai_model_service:clean_json_data:369 | 第一次JSON解析失败，尝试深度修复: Expecting ',' delimiter: line 1 column 92 (char 91)
2025-08-11 14:21:11 | WARNING  | app.services.ai_model_service:clean_json_data:370 | 第一次解析失败的内容: {  "checkResultArr": [    {      "quesType": "合规性/逻辑性/规范性",      "quesDesc": "招标文件封面项目名称多出"阿萨德"字样，与招标公告及其他章节的项目名称不一致，存在笔误。",      "originalArr": [        "安徽理工大学第一附属医院南区污水医废、生活垃圾一体化托管服务项目二次阿萨德"      ],      "point": "项目信息核验，确保文件中的项目名称与招标公告完全一致。",      "advice": "删除招标文件封面项目名称中多余的"阿萨德"字样，使其与招标公告及其他章节的项目名称保持一致。"    }  ]}...
2025-08-11 14:21:11 | WARNING  | app.services.ai_model_service:clean_json_data:376 | 提取JSON后的内容: {  "checkResultArr": [    {      "quesType": "合规性/逻辑性/规范性",      "quesDesc": "招标文件封面项目名称多出"阿萨德"字样，与招标公告及其他章节的项目名称不一致，存在笔误。",      "originalArr": [        "安徽理工大学第一附属医院南区污水医废、生活垃圾一体化托管服务项目二次阿萨德"      ],      "point": "项目信息核验，确保文件中的项目名称与招标公告完全一致。",      "advice": "删除招标文件封面项目名称中多余的"阿萨德"字样，使其与招标公告及其他章节的项目名称保持一致。"    }  ]}...
2025-08-11 14:21:11 | WARNING  | app.services.ai_model_service:clean_json_data:379 | 尝试修复JSON引号问题，错误位置: 第92列
2025-08-11 14:21:11 | WARNING  | app.services.ai_model_service:clean_json_data:390 | 全面JSON修复失败: Expecting ',' delimiter: line 1 column 92 (char 91)
2025-08-11 14:21:11 | WARNING  | app.services.ai_model_service:clean_json_data:391 | 返回空结果
2025-08-11 14:22:14 | INFO     | app.services.ai_model_service:_initialize_client:80 | AI模型客户端初始化成功: gemini-2.5-flash
2025-08-11 14:22:14 | WARNING  | app.services.ai_model_service:clean_json_data:369 | 第一次JSON解析失败，尝试深度修复: Expecting ',' delimiter: line 1 column 92 (char 91)
2025-08-11 14:22:14 | WARNING  | app.services.ai_model_service:clean_json_data:370 | 第一次解析失败的内容: {  "checkResultArr": [    {      "quesType": "合规性/逻辑性/规范性",      "quesDesc": "招标文件封面项目名称多出"阿萨德"字样，与招标公告及其他章节的项目名称不一致，存在笔误。",      "originalArr": [        "安徽理工大学第一附属医院南区污水医废、生活垃圾一体化托管服务项目二次阿萨德"      ],      "point": "项目信息核验，确保文件中的项目名称与招标公告完全一致。",      "advice": "删除招标文件封面项目名称中多余的"阿萨德"字样，使其与招标公告及其他章节的项目名称保持一致。"    }  ]}...
2025-08-11 14:22:14 | WARNING  | app.services.ai_model_service:clean_json_data:376 | 提取JSON后的内容: {  "checkResultArr": [    {      "quesType": "合规性/逻辑性/规范性",      "quesDesc": "招标文件封面项目名称多出"阿萨德"字样，与招标公告及其他章节的项目名称不一致，存在笔误。",      "originalArr": [        "安徽理工大学第一附属医院南区污水医废、生活垃圾一体化托管服务项目二次阿萨德"      ],      "point": "项目信息核验，确保文件中的项目名称与招标公告完全一致。",      "advice": "删除招标文件封面项目名称中多余的"阿萨德"字样，使其与招标公告及其他章节的项目名称保持一致。"    }  ]}...
2025-08-11 14:22:14 | WARNING  | app.services.ai_model_service:clean_json_data:379 | 尝试修复JSON引号问题，错误位置: 第92列
2025-08-11 14:22:14 | WARNING  | app.services.ai_model_service:clean_json_data:390 | 全面JSON修复失败: Expecting ',' delimiter: line 1 column 92 (char 91)
2025-08-11 14:22:14 | WARNING  | app.services.ai_model_service:clean_json_data:391 | 返回空结果
